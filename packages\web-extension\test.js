

// import turndown from "turndown"

const turndown = require('turndown')

var turndownService = new turndown()
turndownService.addRule('codefor', {
  filter: ['pre'],
  replacement: function (content) {
    console.log('pre', content)
    return ['```', content, '```'].join('\n')
  },
})


var post = {
	"post_title": "Ractor 下多线程 Ruby 程序指南",
	"post_content": "<section data-tool=\"mdnice编辑器\" data-website=\"https://www.mdnice.com\" style=\"\"><h2 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;padding: 0px;font-weight: bold;color: black;font-size: 22px;\"><span style=\"display: none;\"></span>什么是 Ractor?</h2><p data-tool=\"mdnice编辑器\" style=\"font-size: 16px;padding-top: 8px;padding-bottom: 8px;margin: 0;line-height: 26px;color: black;\">Ractor 是 Ruby 3 新引入的特性。Ra<PERSON> 顾名思义是 Ruby 和 Actor 的组合词。Actor 模型是一个基于通讯的、非锁同步的并发模型。基于 Actor 的并发模型在 Ruby 中有很多应用，比如 <code style=\"font-size: 14px;word-wrap: break-word;padding: 2px 4px;border-radius: 4px;margin: 0 2px;color: #1e6bb8;background-color: rgba(27,31,35,.05);font-family: Operator Mono, Consolas, Monaco, Menlo, monospace;word-break: break-all;\">concurrent-ruby</code> 中的 <code style=\"font-size: 14px;word-wrap: break-word;padding: 2px 4px;border-radius: 4px;margin: 0 2px;color: #1e6bb8;background-color: rgba(27,31,35,.05);font-family: Operator Mono, Consolas, Monaco, Menlo, monospace;word-break: break-all;\">Concurrent::Actor</code>。Concurrent Ruby 虽然引入了大量的抽象模型，允许开发高并发的应用，但是它并不能摆脱 Ruby 的 GIL (Global Interpreter Lock)，这使得同一时间，只有一个线程是活跃的。所以通常 <code style=\"font-size: 14px;word-wrap: break-word;padding: 2px 4px;border-radius: 4px;margin: 0 2px;color: #1e6bb8;background-color: rgba(27,31,35,.05);font-family: Operator Mono, Consolas, Monaco, Menlo, monospace;word-break: break-all;\">concurrent-ruby</code> 需要搭配无锁的 JRuby 解释器使用。然而，直接解除 GIL 锁会导致大量默认 GIL 可用的依赖出现问题，在多线程开发中会产生难以预料的线程竞争问题。</p><p data-tool=\"mdnice编辑器\" style=\"font-size: 16px;padding-top: 8px;padding-bottom: 8px;margin: 0;line-height: 26px;color: black;\">去年在 RubyConf China 的时候，我问 matz 说 90 年代多核的小型机以及超级计算机已经变得非常普遍了，为什么会把 Ruby 的多线程设计成这样呢？matz 表示，他当时还在用装着 Windows 95 的 PC，如果他知道以后多核会那么普遍，他也不会把 Ruby 设计成这样。</p><h2 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;padding: 0px;font-weight: bold;color: black;font-size: 22px;\"><span style=\"display: none;\"></span>什么数据可以在 Ractor 间共享？</h2><p data-tool=\"mdnice编辑器\" style=\"font-size: 16px;padding-top: 8px;padding-bottom: 8px;margin: 0;line-height: 26px;color: black;\">但是，历史遗留问题依然需要解决。随着 Fiber Scheduler 在 Ruby 3 引用来提高 I/O 密集场景下单一线程利用率极低的问题；我们需要进一步解决，计算密集场景下，多线程的利用率。</p><p data-tool=\"mdnice编辑器\" style=\"font-size: 16px;padding-top: 8px;padding-bottom: 8px;margin: 0;line-height: 26px;color: black;\">为了解决这一问题，Ruby 3 引入了 Ractor 模型。Ractor 本质来说还是 Thread 线程，但是 Ractor 做了一系列的限制。首先，锁是不会在 Ractor 之间共享的；也就是说，不可能有两个线程争抢同一个锁。Ractor 和 Ractor 之间可以传递消息。Ractor 内部具有全局锁，确保 Ractor 内的行为和原先 Thread 是一致的。传递消息必须是值类型的，这意味着不会有指针跨 Ractor 生存，也会避免数据竞争问题。简而言之，Ractor 把每个 Thread 当作一个 Actor。</p><p data-tool=\"mdnice编辑器\" style=\"font-size: 16px;padding-top: 8px;padding-bottom: 8px;margin: 0;line-height: 26px;color: black;\">但 Ruby 没有真正的值类型。但值类型的本质就是用拷贝来替代引用。我们要做的就是确保 Ruby 对象的可拷贝性。我们查看 Ractor 的文档，我们可以看到这个的严格描述：</p><pre data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;border-radius: 5px;box-shadow: rgba(0, 0, 0, 0.55) 0px 2px 10px;\"><code>Ractors&nbsp;don't&nbsp;share&nbsp;everything,&nbsp;unlike&nbsp;threads.</code>\n<code></code>\n<code>*&nbsp;Most&nbsp;objects&nbsp;are&nbsp;*Unshareable&nbsp;objects*,&nbsp;so&nbsp;you&nbsp;don't&nbsp;need&nbsp;to&nbsp;care&nbsp;about&nbsp;thread-safety&nbsp;problem&nbsp;which&nbsp;is&nbsp;caused&nbsp;by&nbsp;sharing.</code>\n<code>*&nbsp;Some&nbsp;objects&nbsp;are&nbsp;*Shareable&nbsp;objects*.</code>\n<code>&nbsp;&nbsp;*&nbsp;Immutable&nbsp;objects:&nbsp;frozen&nbsp;objects&nbsp;which&nbsp;don't&nbsp;refer&nbsp;to&nbsp;unshareable-objects.</code>\n<code>&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;`i&nbsp;=&nbsp;123`:&nbsp;`i`&nbsp;is&nbsp;an&nbsp;immutable&nbsp;object.</code>\n<code>&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;`s&nbsp;=&nbsp;\"str\".freeze`:&nbsp;`s`&nbsp;is&nbsp;an&nbsp;immutable&nbsp;object.</code>\n<code>&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;`a&nbsp;=&nbsp;[1,&nbsp;[2],&nbsp;3].freeze`:&nbsp;`a`&nbsp;is&nbsp;not&nbsp;an&nbsp;immutable&nbsp;object&nbsp;because&nbsp;`a`&nbsp;refer&nbsp;unshareable-object&nbsp;`[2]`&nbsp;(which&nbsp;is&nbsp;not&nbsp;frozen).</code>\n<code>&nbsp;&nbsp;*&nbsp;Class/Module&nbsp;objects</code>\n<code>&nbsp;&nbsp;*&nbsp;Special&nbsp;shareable&nbsp;objects</code>\n<code>&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Ractor&nbsp;object&nbsp;itself.</code>\n<code>&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;And&nbsp;more...</code>\n<code></code></pre><h2 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;padding: 0px;font-weight: bold;color: black;font-size: 22px;\"><span style=\"display: none;\"></span>Ractor 性能提升测试</h2><p data-tool=\"mdnice编辑器\" style=\"font-size: 16px;padding-top: 8px;padding-bottom: 8px;margin: 0;line-height: 26px;color: black;\">为了测试出 Ractor 的效果，我们需要一个计算密集的场景。最计算密集的场景，当然就是做数学计算本身。比如我们有下面一个程序：</p><pre data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;border-radius: 5px;box-shadow: rgba(0, 0, 0, 0.55) 0px 2px 10px;\"><code>DAT&nbsp;=&nbsp;(0...72072000).to_a</code>\n<code>p&nbsp;DAT.map&nbsp;{&nbsp;|a|&nbsp;a**2&nbsp;}.reduce(:+)</code>\n<code></code></pre><p data-tool=\"mdnice编辑器\" style=\"font-size: 16px;padding-top: 8px;padding-bottom: 8px;margin: 0;line-height: 26px;color: black;\">这个程序计算 0 到 72072000 的平方和。我们运行一下这个程序，得到运行时间是 8.17s。</p><p data-tool=\"mdnice编辑器\" style=\"font-size: 16px;padding-top: 8px;padding-bottom: 8px;margin: 0;line-height: 26px;color: black;\">如果我们用传统的多线程来写，我们可以把程序写成这样：</p><pre data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;border-radius: 5px;box-shadow: rgba(0, 0, 0, 0.55) 0px 2px 10px;\"><code>THREADS&nbsp;=&nbsp;8</code>\n<code>LCM&nbsp;=&nbsp;72072000</code>\n<code>t&nbsp;=&nbsp;[]</code>\n<code></code>\n<code>res&nbsp;=&nbsp;[]</code>\n<code>(0...THREADS).each&nbsp;do&nbsp;|i|</code>\n<code>&nbsp;&nbsp;r&nbsp;=&nbsp;Thread.new&nbsp;do</code>\n<code>&nbsp;&nbsp;&nbsp;&nbsp;dat&nbsp;=&nbsp;(((LCM/THREADS)*i)...((LCM/THREADS)*(i+1))).to_a</code>\n<code>&nbsp;&nbsp;&nbsp;&nbsp;res&nbsp;&lt;&lt;&nbsp;dat.map{&nbsp;|a|&nbsp;a&nbsp;**&nbsp;2&nbsp;}.reduce(:+)</code>\n<code>&nbsp;&nbsp;end</code>\n<code>&nbsp;&nbsp;t&nbsp;&lt;&lt;&nbsp;r</code>\n<code>end</code>\n<code></code>\n<code>t.each&nbsp;{&nbsp;|t|&nbsp;t.join&nbsp;}</code>\n<code>p&nbsp;res.reduce(:+)</code>\n<code></code></pre><p data-tool=\"mdnice编辑器\" style=\"font-size: 16px;padding-top: 8px;padding-bottom: 8px;margin: 0;line-height: 26px;color: black;\">运行后，我们发现，虽然确实创建了 8 个系统线程，但是总运行时间变成了 8.21s。没有显著的性能提升。</p><p data-tool=\"mdnice编辑器\" style=\"font-size: 16px;padding-top: 8px;padding-bottom: 8px;margin: 0;line-height: 26px;color: black;\">使用 Ractor 重写程序，主要需要改变我们子线程内需要访问外面的 <code style=\"font-size: 14px;word-wrap: break-word;padding: 2px 4px;border-radius: 4px;margin: 0 2px;color: #1e6bb8;background-color: rgba(27,31,35,.05);font-family: Operator Mono, Consolas, Monaco, Menlo, monospace;word-break: break-all;\">i</code> 变量，我们用消息的方法传递进去，改进后的代码会变成这样：</p><pre data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;border-radius: 5px;box-shadow: rgba(0, 0, 0, 0.55) 0px 2px 10px;\"><code>THREADS&nbsp;=&nbsp;8</code>\n<code>LCM&nbsp;=&nbsp;72072000</code>\n<code>t&nbsp;=&nbsp;[]</code>\n<code></code>\n<code>(0...THREADS).each&nbsp;do&nbsp;|i|</code>\n<code>&nbsp;&nbsp;r&nbsp;=&nbsp;Ractor.new&nbsp;i&nbsp;do&nbsp;|j|</code>\n<code>&nbsp;&nbsp;&nbsp;&nbsp;dat&nbsp;=&nbsp;(((LCM/THREADS)*j)...((LCM/THREADS)*(j+1))).to_a</code>\n<code>&nbsp;&nbsp;&nbsp;&nbsp;dat.map{&nbsp;|a|&nbsp;a&nbsp;**&nbsp;2&nbsp;}.reduce(:+)</code>\n<code>&nbsp;&nbsp;end</code>\n<code>&nbsp;&nbsp;t&nbsp;&lt;&lt;&nbsp;r</code>\n<code>end</code>\n<code></code>\n<code>p&nbsp;t.map&nbsp;{&nbsp;|t|&nbsp;t.take&nbsp;}.reduce(:+)</code>\n<code></code></pre><p data-tool=\"mdnice编辑器\" style=\"font-size: 16px;padding-top: 8px;padding-bottom: 8px;margin: 0;line-height: 26px;color: black;\">其结果如何呢？我们根据不同的线程数量进行了测试。</p><section data-tool=\"mdnice编辑器\" style=\"overflow-x: auto;\"><table><thead><tr style=\"border-color: rgb(204, 204, 204) currentcolor currentcolor;border-style: solid none none;border-width: 1px 0px 0px;background-color: white;\"><th style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;font-weight: bold;background-color: rgb(240, 240, 240);min-width: 85px;\"><strong style=\"font-weight: bold;color: black;\">Threads</strong></th><th style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;font-weight: bold;background-color: rgb(240, 240, 240);min-width: 85px;\"><strong style=\"font-weight: bold;color: black;\">AMD Ryzen 7 2700x</strong></th><th style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;font-weight: bold;background-color: rgb(240, 240, 240);min-width: 85px;\"><strong style=\"font-weight: bold;color: black;\">Intel i7-6820HQ</strong></th></tr></thead><tbody style=\"border-color: currentcolor;border-style: none;border-width: 0px;\"><tr style=\"border-color: rgb(204, 204, 204) currentcolor currentcolor;border-style: solid none none;border-width: 1px 0px 0px;background-color: white;\"><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\"><strong style=\"font-weight: bold;color: black;\">1</strong></td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\">8.171</td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\">12.027</td></tr><tr style=\"border-color: rgb(204, 204, 204) currentcolor currentcolor;border-style: solid none none;border-width: 1px 0px 0px;background-color: rgb(248, 248, 248);\"><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\"><strong style=\"font-weight: bold;color: black;\">2</strong></td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\">4.483</td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\">6.913</td></tr><tr style=\"border-color: rgb(204, 204, 204) currentcolor currentcolor;border-style: solid none none;border-width: 1px 0px 0px;background-color: white;\"><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\"><strong style=\"font-weight: bold;color: black;\">3</strong></td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\">4.874</td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\">6.755</td></tr><tr style=\"border-color: rgb(204, 204, 204) currentcolor currentcolor;border-style: solid none none;border-width: 1px 0px 0px;background-color: rgb(248, 248, 248);\"><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\"><strong style=\"font-weight: bold;color: black;\">4</strong></td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\">2.353</td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\">6.188</td></tr><tr style=\"border-color: rgb(204, 204, 204) currentcolor currentcolor;border-style: solid none none;border-width: 1px 0px 0px;background-color: white;\"><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\"><strong style=\"font-weight: bold;color: black;\">5</strong></td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\">2.429</td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\">5.154</td></tr><tr style=\"border-color: rgb(204, 204, 204) currentcolor currentcolor;border-style: solid none none;border-width: 1px 0px 0px;background-color: rgb(248, 248, 248);\"><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\"><strong style=\"font-weight: bold;color: black;\">6</strong></td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\">2.259</td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\">5.320</td></tr><tr style=\"border-color: rgb(204, 204, 204) currentcolor currentcolor;border-style: solid none none;border-width: 1px 0px 0px;background-color: white;\"><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\"><strong style=\"font-weight: bold;color: black;\">7</strong></td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\">1.908</td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\">5.368</td></tr><tr style=\"border-color: rgb(204, 204, 204) currentcolor currentcolor;border-style: solid none none;border-width: 1px 0px 0px;background-color: rgb(248, 248, 248);\"><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\"><strong style=\"font-weight: bold;color: black;\">8</strong></td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\">2.156</td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\">5.754</td></tr><tr style=\"border-color: rgb(204, 204, 204) currentcolor currentcolor;border-style: solid none none;border-width: 1px 0px 0px;background-color: white;\"><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\"><strong style=\"font-weight: bold;color: black;\">9</strong></td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\">2.136</td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\"><br></td></tr><tr style=\"border-color: rgb(204, 204, 204) currentcolor currentcolor;border-style: solid none none;border-width: 1px 0px 0px;background-color: rgb(248, 248, 248);\"><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\"><strong style=\"font-weight: bold;color: black;\">10</strong></td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\">3.159</td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\"><br></td></tr><tr style=\"border-color: rgb(204, 204, 204) currentcolor currentcolor;border-style: solid none none;border-width: 1px 0px 0px;background-color: white;\"><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\"><strong style=\"font-weight: bold;color: black;\">11</strong></td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\">2.577</td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\"><br></td></tr><tr style=\"border-color: rgb(204, 204, 204) currentcolor currentcolor;border-style: solid none none;border-width: 1px 0px 0px;background-color: rgb(248, 248, 248);\"><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\"><strong style=\"font-weight: bold;color: black;\">12</strong></td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\">2.679</td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\"><br></td></tr><tr style=\"border-color: rgb(204, 204, 204) currentcolor currentcolor;border-style: solid none none;border-width: 1px 0px 0px;background-color: white;\"><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\"><strong style=\"font-weight: bold;color: black;\">13</strong></td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\">2.787</td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\"><br></td></tr><tr style=\"border-color: rgb(204, 204, 204) currentcolor currentcolor;border-style: solid none none;border-width: 1px 0px 0px;background-color: rgb(248, 248, 248);\"><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\"><strong style=\"font-weight: bold;color: black;\">14</strong></td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\">2.615</td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\"><br></td></tr><tr style=\"border-color: rgb(204, 204, 204) currentcolor currentcolor;border-style: solid none none;border-width: 1px 0px 0px;background-color: white;\"><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\"><strong style=\"font-weight: bold;color: black;\">15</strong></td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\">2.197</td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\"><br></td></tr><tr style=\"border-color: rgb(204, 204, 204) currentcolor currentcolor;border-style: solid none none;border-width: 1px 0px 0px;background-color: rgb(248, 248, 248);\"><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\"><strong style=\"font-weight: bold;color: black;\">16</strong></td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\">2.303</td><td style=\"font-size: 16px;border-color: rgb(204, 204, 204);border-style: solid;border-width: 1px;padding: 5px 10px;text-align: left;min-width: 85px;\"><br></td></tr></tbody></table></section><figure data-tool=\"mdnice编辑器\" style=\"margin: 0;margin-top: 10px;margin-bottom: 10px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><img data-ratio=\"0.7692307692307693\" data-src=\"https://mmbiz.qpic.cn/mmbiz_png/CJcVm4ThlNOeib5w5A6MYk4Eg9ErnzZ73hPT7MOHJ61N5icxauUfxxBsu2O6SdGga0WIEHr5onSmAKkEWZIY1Qbg/640?wx_fmt=png\" data-type=\"png\" data-w=\"442\" style=\"display: block; margin: 0px auto; max-width: 100%; width: 442px !important; height: 340.462px !important;\" _width=\"442px\" class=\"img_loading\" src=\"https://p3-juejin.byteimg.com/tos-cn-i-k3u1fbpfcp/0dd6487cafcb46ecb72275fa7d83ecb7~tplv-k3u1fbpfcp-zoom-1.image\" crossorigin=\"anonymous\" alt=\"图片\"></figure><p data-tool=\"mdnice编辑器\" style=\"font-size: 16px;padding-top: 8px;padding-bottom: 8px;margin: 0;line-height: 26px;color: black;\">Ractor 确实改善了多线程全局解释锁的问题。</p><h2 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;padding: 0px;font-weight: bold;color: black;font-size: 22px;\"><span style=\"display: none;\"></span>显微镜下的 Ractor</h2><p data-tool=\"mdnice编辑器\" style=\"font-size: 16px;padding-top: 8px;padding-bottom: 8px;margin: 0;line-height: 26px;color: black;\">我使用了 AMD uProf（对于 Intel CPU，可以使用 Intel VTune）进行 CPU 运算情况的统计。为了降低睿频对单线程性能的影响，我将 AMD Ryzen 7 2700x 全核心锁死 4.2GHz。</p><figure data-tool=\"mdnice编辑器\" style=\"margin: 0;margin-top: 10px;margin-bottom: 10px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><img data-ratio=\"0.4213615023474178\" data-src=\"https://mmbiz.qpic.cn/mmbiz_png/CJcVm4ThlNOeib5w5A6MYk4Eg9ErnzZ73hBMm0loiboURRpCVU3nc4aD6eyCc7TkIb37aic0Tq2zabOgMpQ95HBhA/640?wx_fmt=png\" data-type=\"png\" data-w=\"852\" style=\"display: block; margin: 0px auto; max-width: 100%; width: 677px !important; height: 286.419px !important;\" _width=\"677px\" class=\"img_loading\" src=\"https://p3-juejin.byteimg.com/tos-cn-i-k3u1fbpfcp/bdf74df4a7374eafb8f81accda76712d~tplv-k3u1fbpfcp-zoom-1.image\" crossorigin=\"anonymous\" alt=\"图片\"></figure><p data-tool=\"mdnice编辑器\" style=\"font-size: 16px;padding-top: 8px;padding-bottom: 8px;margin: 0;line-height: 26px;color: black;\">对于 AMD Ryzen 7 2700x，4 线程比单一线程快了 3 倍多。到 4 线程，比单一线程快了约 4 倍。AMD Ryzen 7 2700x 是一款 8 核心 16 线程的 CPU。同时，每 4 个核心组成一个 CCX，跨 CCX 的内存访问有额外的代价。这使得 4 线程内性能提升很显著，超过 4 线程后受限于 CCX 和 SMT，性能提升变得比较有限。其表现是随着线程数的增加，IPC（每时钟周期指令数）开始下降。在单线程运算时，每时钟周期 CPU 可以执行 2.42 个指令；但到了 16 线程运算时，每时钟周期 CPU 只能执行 1.40 个指令。同时，更多的线程意味着更复杂的操作系统的线程调度，使得多核的利用率越来越低。</p><p data-tool=\"mdnice编辑器\" style=\"font-size: 16px;padding-top: 8px;padding-bottom: 8px;margin: 0;line-height: 26px;color: black;\">同样，对于 Intel i7-6820HQ，我们得到了类似的结论。这是一款 4 核 8 线程的 CPU，由于第 5 个线程开始需要使用 HT，从而提升变得很有限。</p><h2 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;padding: 0px;font-weight: bold;color: black;font-size: 22px;\"><span style=\"display: none;\"></span>Ractor 如何改善现有 Ruby 程序的性能？</h2><p data-tool=\"mdnice编辑器\" style=\"font-size: 16px;padding-top: 8px;padding-bottom: 8px;margin: 0;line-height: 26px;color: black;\">Ractor 的引入除了可以改善计算密集场景下的运算效率，对于现有大型 Ruby Web 程序的内存占用也是有积极意义的。现有 Web 服务器，比如 puma，由于 I/O 多路复用性能极其低下，通常会使用多线程 + 多进程的形式来提升性能。由于 Web 服务器可以自由水平扩展，使用多进程的形式来管理，可以完全解开 GIL 锁的问题。</p><p data-tool=\"mdnice编辑器\" style=\"font-size: 16px;padding-top: 8px;padding-bottom: 8px;margin: 0;line-height: 26px;color: black;\">但是 fork 指令效率低下。微软在 2019 年 HOTOS 上给出了一篇论文：A fork() in the road，和 spawn 相比，fork 模式会导致启动速度变得非常慢。为了缓解这一问题，在 Ruby 2.7 引入 <code style=\"font-size: 14px;word-wrap: break-word;padding: 2px 4px;border-radius: 4px;margin: 0 2px;color: #1e6bb8;background-color: rgba(27,31,35,.05);font-family: Operator Mono, Consolas, Monaco, Menlo, monospace;word-break: break-all;\">GC.compact</code> 后，通常需要执行多次 <code style=\"font-size: 14px;word-wrap: break-word;padding: 2px 4px;border-radius: 4px;margin: 0 2px;color: #1e6bb8;background-color: rgba(27,31,35,.05);font-family: Operator Mono, Consolas, Monaco, Menlo, monospace;word-break: break-all;\">compact</code> 来降低 fork 启动的消耗。进一步地，使用 Ractor 来替代多进程管理，可以更容易地传递消息，复用可冻结的常量，从而降低内存占用。</p><h2 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;padding: 0px;font-weight: bold;color: black;font-size: 22px;\"><span style=\"display: none;\"></span>总结</h2><p data-tool=\"mdnice编辑器\" style=\"font-size: 16px;padding-top: 8px;padding-bottom: 8px;margin: 0;line-height: 26px;color: black;\">Ruby 3 打开了多线程的潘多拉盒子。我们可以更好利用多线程来改善性能。但是看着 CPU Profiler 下不同线程调用会导致 CPU IPC 下降和缓存命中下降，对程序调优也提出了更高的要求。</p><p data-tool=\"mdnice编辑器\" style=\"font-size: 16px;padding-top: 8px;padding-bottom: 8px;margin: 0;line-height: 26px;color: black;\">我们边走边看吧。</p></section><p><br></p>",
	"post_thumbnail": *************,
	"post_thumbnail_raw": {
		"id": *************,
		"object_key": *************,
		"url": "https://p3-juejin.byteimg.com/tos-cn-i-k3u1fbpfcp/8a6e200c9af24db5881c24b8b346edc8~tplv-k3u1fbpfcp-zoom-1.image"
	}
}
var markdown = turndownService.turndown(post.post_content)
console.log(markdown);


window.$syncer.magicCall({
    methodName: 'searchAccount',
    account: {
      type: 'weixin',
    },
	keyword: '云南'
}, res => {
	console.log(res)
})


window.$syncer.magicCall({
	methodName: 'listArticle',
	account: {
		type: 'weixin',
	},
	fakeid: 'MzUzMDk4NDQ2Mg=='
}, res => {
	console.log(res)
	})