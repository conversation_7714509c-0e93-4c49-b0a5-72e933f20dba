{"title": "基于Airtable的产品需求收集和分析", "content": "<section   style=\"line-height: 1.6;word-break: break-word;overflow-wrap: break-word;text-align: left;font-family: Optima-Regular, Optima, PingFangSC-light, PingFangTC-light, &quot;PingFang SC&quot;, Cambria, Cochin, Georgia, Times, &quot;Times New Roman&quot;, serif;padding-right: 10px;padding-left: 10px;font-size: 0.958em;color: rgb(53, 53, 53);word-spacing: 0.8px;letter-spacing: 0.8px;border-radius: 0.958em;\"><blockquote  style=\"border-width: initial;border-style: none;border-color: initial;font-size: 0.9em;overflow: auto;margin-bottom: 20px;margin-top: 20px;padding-top: 15px;padding-right: 10px;padding-bottom: 15px;line-height: 1.75;border-radius: 13px;color: rgb(53, 53, 53);background: rgb(245, 245, 245);\"><span style=\"display: block;font-size: 2em;color: #0087fc;font-family: Arial, serif;line-height: 1em;font-weight: 700;\">“</span><p style=\"padding-top: 8px;padding-bottom: 8px;line-height: 26px;font-size: 0.958em;margin-right: 10px;margin-left: 10px;\">年初盘点了下去年都做了些啥，发现文章同步助手仅靠自然流量用户增长了也有10倍之多了（投入的精力并不多的情况下）之前也有想好好完善下，增加更多的同步渠道。</p><span style=\"float: right;display: block;font-size: 2em;color: #0087fc;font-family: Arial, serif;line-height: 1em;font-weight: 700;\">”</span></blockquote><h2  style=\"font-weight: bold;color: black;font-size: 22px;margin-top: 20px;margin-right: 10px;\"><span style=\"display: none;\"></span><span style=\"font-size: 18px;color: rgb(34, 34, 34);display: inline-block;padding-left: 10px;border-left: 5px solid rgb(0, 135, 252);\">收集需求的需求</span></h2><p  style=\"padding-top: 8px;padding-bottom: 8px;line-height: 1.75;margin: 0.8em 0em;font-size: 0.958em;color: #666;\">对自媒体人士而言，除主流媒体之外，有很多细分的媒体也是很需要的，比如说【马蜂窝、穷游】这样的媒体对于一位旅游行业从业人员来讲，吸引力和需要程度远在主流媒体之上。</p><figure  style=\"margin-top: 10px;margin-bottom: 10px;display: flex;flex-direction: column;justify-content: center;align-items: center;border-radius: 0.958em;overflow: hidden;\"><img data-ratio=\"0.3703308431163287\" data-src=\"https://mmbiz.qpic.cn/mmbiz_png/VUsUpGDa4qcjdkWaVgxn094JdVexMj2PhcD0CVN8qy8Ca1g65WJBPYqurlibnNCVfYqEf3mkKAV1Qx6fbcFGU3g/640?wx_fmt=png\" data-type=\"png\" data-w=\"937\" style=\"border-radius: 6px; display: block; margin: 10px auto; object-fit: contain; box-shadow: rgba(170, 170, 170, 0.48) 0px 0px 6px 0px; max-width: 95% !important; width: 677px !important; height: auto !important; visibility: visible !important;\" _width=\"677px\" class=\"\" src=\"https://mmbiz.qpic.cn/mmbiz_png/VUsUpGDa4qcjdkWaVgxn094JdVexMj2PhcD0CVN8qy8Ca1g65WJBPYqurlibnNCVfYqEf3mkKAV1Qx6fbcFGU3g/640?wx_fmt=png&amp;tp=webp&amp;wxfrom=5&amp;wx_lazy=1&amp;wx_co=1\" crossorigin=\"anonymous\" alt=\"图片\" data-fail=\"0\"></figure><p  style=\"padding-top: 8px;padding-bottom: 8px;line-height: 1.75;margin: 0.8em 0em;font-size: 0.958em;color: #666;\">考虑到这些情况的存在，很有必要分行业来增加这些细分的渠道的支持，这样对用户整体而言、可用性价值就更大了。</p><p  style=\"padding-top: 8px;padding-bottom: 8px;line-height: 1.75;margin: 0.8em 0em;font-size: 0.958em;color: #666;\">于是乎：在知道要大量增加渠道的情况下，如何保证增加的渠道是用户所需要的呢？这个时候需求收集就很有用了。</p><h2  style=\"font-weight: bold;color: black;font-size: 22px;margin-top: 20px;margin-right: 10px;\"><span style=\"display: none;\"></span><span style=\"font-size: 18px;color: rgb(34, 34, 34);display: inline-block;padding-left: 10px;border-left: 5px solid rgb(0, 135, 252);\">人肉收集？</span></h2><p  style=\"padding-top: 8px;padding-bottom: 8px;line-height: 1.75;margin: 0.8em 0em;font-size: 0.958em;color: #666;\">起初我想在用户微信群，让大家【给自己的群名称】备注上自己的所在的【行业】，再人肉挨个统计行业，再按行业自行去支持这些行业的头部媒体。</p><p  style=\"padding-top: 8px;padding-bottom: 8px;line-height: 1.75;margin: 0.8em 0em;font-size: 0.958em;color: #666;\">但在具体实施的过程中我觉得人肉统计这件事可能太费劲了、其次用户也不一定有意愿去给自己备注行业（即便是你告诉了他你的动机是为了了解行业支持更多符合大家需要的渠道）。</p><h2  style=\"font-weight: bold;color: black;font-size: 22px;margin-top: 20px;margin-right: 10px;\"><span style=\"display: none;\"></span><span style=\"font-size: 18px;color: rgb(34, 34, 34);display: inline-block;padding-left: 10px;border-left: 5px solid rgb(0, 135, 252);\">新型文档应用</span></h2><figure  style=\"margin-top: 10px;margin-bottom: 10px;display: flex;flex-direction: column;justify-content: center;align-items: center;border-radius: 0.958em;overflow: hidden;\"><img data-ratio=\"0.4755492558469171\" data-src=\"https://mmbiz.qpic.cn/mmbiz_png/VUsUpGDa4qcjdkWaVgxn094JdVexMj2PTBqia1cBXF081Gf3ia74oyYibF4zW0DbPZVWXsP5BOKS8WwfTpxiam3tHQ/640?wx_fmt=png\" data-type=\"png\" data-w=\"1411\" style=\"border-radius: 6px; display: block; margin: 10px auto; object-fit: contain; box-shadow: rgba(170, 170, 170, 0.48) 0px 0px 6px 0px; max-width: 95% !important; width: 657px !important; height: 313.485px !important;\" _width=\"677px\" class=\"img_loading\" src=\"data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWNgYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg==\" crossorigin=\"anonymous\" alt=\"图片\"></figure><p  style=\"padding-top: 8px;padding-bottom: 8px;line-height: 1.75;margin: 0.8em 0em;font-size: 0.958em;color: #666;\">种种原因这让我想到了Coda，或许可以此定义一个文档、做一个小型的需求收集应用，定义基础表格字段，Button绑定自定义表单录入收据。加上投票计数。</p><p  style=\"padding-top: 8px;padding-bottom: 8px;line-height: 1.75;margin: 0.8em 0em;font-size: 0.958em;color: #666;\">这样用户可以自行录入一个渠道，其他用户可以给某渠道进行投票计数（计数能了解用户到底更需要哪个渠道、便于确定优先级），后面只需要把这个文档扔到群里就可以了！</p><figure  style=\"margin-top: 10px;margin-bottom: 10px;display: flex;flex-direction: column;justify-content: center;align-items: center;border-radius: 0.958em;overflow: hidden;\"><img data-ratio=\"0.5766037735849057\" data-src=\"https://mmbiz.qpic.cn/mmbiz_png/VUsUpGDa4qcjdkWaVgxn094JdVexMj2P9KH23ibulgQcf1gicqypOohtm5PwY6giaoI3NVp5iam40hazQskP0D4Uqg/640?wx_fmt=png\" data-type=\"png\" data-w=\"1325\" style=\"border-radius: 6px; display: block; margin: 10px auto; object-fit: contain; box-shadow: rgba(170, 170, 170, 0.48) 0px 0px 6px 0px; max-width: 95% !important; width: 657px !important; height: 379.675px !important;\" _width=\"677px\" class=\"img_loading\" src=\"data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWNgYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg==\" crossorigin=\"anonymous\" alt=\"图片\"></figure><p  style=\"padding-top: 8px;padding-bottom: 8px;line-height: 1.75;margin: 0.8em 0em;font-size: 0.958em;color: #666;\">但事实上Coda相当复杂，即便有很多模板作为开始，我还是感受到了恐慌（在你开始之前你得对Coda的文档和各种特性有一定基础了解，不然根本不知道到底下一步该干啥）</p><figure  style=\"margin-top: 10px;margin-bottom: 10px;display: flex;flex-direction: column;justify-content: center;align-items: center;border-radius: 0.958em;overflow: hidden;\"><img data-ratio=\"0.5978480161398789\" data-src=\"https://mmbiz.qpic.cn/mmbiz_png/VUsUpGDa4qcjdkWaVgxn094JdVexMj2P36TReHq1629JwyUyzvke1O5Q2plRCFnl31IZY8kJag70n3odcK9IPw/640?wx_fmt=png\" data-type=\"png\" data-w=\"1487\" style=\"border-radius: 6px; display: block; margin: 10px auto; object-fit: contain; box-shadow: rgba(170, 170, 170, 0.48) 0px 0px 6px 0px; max-width: 95% !important; width: 657px !important; height: 393.59px !important;\" _width=\"677px\" class=\"img_loading\" src=\"data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWNgYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg==\" crossorigin=\"anonymous\" alt=\"图片\"></figure><p  style=\"padding-top: 8px;padding-bottom: 8px;line-height: 1.75;margin: 0.8em 0em;font-size: 0.958em;color: #666;\">这个时候只能寻求更简便的产品了，比如说先前有调研过的Airtable。相对而言就简单很多了，以定好的数据表为基础，可以衍生到表单提交 ，按某个字段聚合的（看板、图表）视图！</p><h2  style=\"font-weight: bold;color: black;font-size: 22px;margin-top: 20px;margin-right: 10px;\"><span style=\"display: none;\"></span><span style=\"font-size: 18px;color: rgb(34, 34, 34);display: inline-block;padding-left: 10px;border-left: 5px solid rgb(0, 135, 252);\">信息的提交和公示</span></h2><figure  style=\"margin-top: 10px;margin-bottom: 10px;display: flex;flex-direction: column;justify-content: center;align-items: center;border-radius: 0.958em;overflow: hidden;\"><img data-ratio=\"0.5974222896133434\" data-src=\"https://mmbiz.qpic.cn/mmbiz_png/VUsUpGDa4qcjdkWaVgxn094JdVexMj2Px9PKEZxXCPgdXKcx44o7Epw7azJX9DNMDib25SErX7icGV9RBPNggdKg/640?wx_fmt=png\" data-type=\"png\" data-w=\"1319\" style=\"border-radius: 6px; display: block; margin: 10px auto; object-fit: contain; box-shadow: rgba(170, 170, 170, 0.48) 0px 0px 6px 0px; max-width: 95% !important; width: 657px !important; height: 393.312px !important;\" _width=\"677px\" class=\"img_loading\" src=\"data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWNgYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg==\" crossorigin=\"anonymous\" alt=\"图片\"></figure><p  style=\"padding-top: 8px;padding-bottom: 8px;line-height: 1.75;margin: 0.8em 0em;font-size: 0.958em;color: #666;\">按【状态】字段聚合表格，方便查看支持情况下的渠道列表</p><figure  style=\"margin-top: 10px;margin-bottom: 10px;display: flex;flex-direction: column;justify-content: center;align-items: center;border-radius: 0.958em;overflow: hidden;\"><img data-ratio=\"0.6348148148148148\" data-src=\"https://mmbiz.qpic.cn/mmbiz_png/VUsUpGDa4qcjdkWaVgxn094JdVexMj2PozLcrbDYHGYBQE0PSluMZNHicHRc3Vb6dbiaib72MjjfTb3mNFWSYRTfQ/640?wx_fmt=png\" data-type=\"png\" data-w=\"1350\" style=\"border-radius: 6px; display: block; margin: 10px auto; object-fit: contain; box-shadow: rgba(170, 170, 170, 0.48) 0px 0px 6px 0px; max-width: 95% !important; width: 657px !important; height: 417.804px !important;\" _width=\"677px\" class=\"img_loading\" src=\"data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWNgYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg==\" crossorigin=\"anonymous\" alt=\"图片\"></figure><p  style=\"padding-top: 8px;padding-bottom: 8px;line-height: 1.75;margin: 0.8em 0em;font-size: 0.958em;color: #666;\">新增一个表单视图，让用户填写要提交的媒体名称，网址以及大致的备注，在描述区域外链到状态聚合表格。</p><p  style=\"padding-top: 8px;padding-bottom: 8px;line-height: 1.75;margin: 0.8em 0em;font-size: 0.958em;color: #666;\">接着就可以把表单链接发到用户微信群里了、放到官网菜单上等。</p><figure  style=\"margin-top: 10px;margin-bottom: 10px;display: flex;flex-direction: column;justify-content: center;align-items: center;border-radius: 0.958em;overflow: hidden;\"><img data-ratio=\"0.7044072948328267\" data-src=\"https://mmbiz.qpic.cn/mmbiz_png/VUsUpGDa4qcjdkWaVgxn094JdVexMj2PySO9fTia58Z7VPfdeZRFa5CXw3lgqJxXzOA8esiay3VAEsjfTnTewhAw/640?wx_fmt=png\" data-type=\"png\" data-w=\"1316\" style=\"border-radius: 6px; display: block; margin: 10px auto; object-fit: contain; box-shadow: rgba(170, 170, 170, 0.48) 0px 0px 6px 0px; max-width: 95% !important; width: 657px !important; height: 463.387px !important;\" _width=\"677px\" class=\"img_loading\" src=\"data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWNgYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg==\" crossorigin=\"anonymous\" alt=\"图片\"></figure><p  style=\"padding-top: 8px;padding-bottom: 8px;line-height: 1.75;margin: 0.8em 0em;font-size: 0.958em;color: #666;\">在通过Airtable的Iframe嵌套功能，可以很方便的把数据表的状态公示到官网上！再加上提交按钮引导的数据表单上。</p><p  style=\"padding-top: 8px;padding-bottom: 8px;line-height: 1.75;margin: 0.8em 0em;font-size: 0.958em;color: #666;\">信息流动的效率从未如此之高！</p><h2  style=\"font-weight: bold;color: black;font-size: 22px;margin-top: 20px;margin-right: 10px;\"><span style=\"display: none;\"></span><span style=\"font-size: 18px;color: rgb(34, 34, 34);display: inline-block;padding-left: 10px;border-left: 5px solid rgb(0, 135, 252);\">总结</span></h2><figure  style=\"margin-top: 10px;margin-bottom: 10px;display: flex;flex-direction: column;justify-content: center;align-items: center;border-radius: 0.958em;overflow: hidden;\"><img data-ratio=\"0.18340611353711792\" data-src=\"https://mmbiz.qpic.cn/mmbiz_png/VUsUpGDa4qcjdkWaVgxn094JdVexMj2PGqM3WeseyiapKUgDq8bqX4W3lTlSUfnRib1RKYYbgiaRWibvKR6TfwqibrQ/640?wx_fmt=png\" data-type=\"png\" data-w=\"1145\" style=\"border-radius: 6px; display: block; margin: 10px auto; object-fit: contain; box-shadow: rgba(170, 170, 170, 0.48) 0px 0px 6px 0px; max-width: 95% !important; width: 657px !important; height: 122.131px !important;\" _width=\"677px\" class=\"img_loading\" src=\"data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWNgYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg==\" crossorigin=\"anonymous\" alt=\"图片\"></figure><p  style=\"padding-top: 8px;padding-bottom: 8px;line-height: 1.75;margin: 0.8em 0em;font-size: 0.958em;color: #666;\">果不其然，从1月22号加上到今天，已经收集了大概20多个待支持的渠道。这些积压的需求，进而引发了对提高新渠道对接效率的思考。从而有了新的改进需求，以及是否可以有效利用用户的力量来帮助对接更多的渠道等。</p><figure  style=\"margin-top: 10px;margin-bottom: 10px;display: flex;flex-direction: column;justify-content: center;align-items: center;border-radius: 0.958em;overflow: hidden;\"><img data-ratio=\"0.5560488346281909\" data-src=\"https://mmbiz.qpic.cn/mmbiz_png/VUsUpGDa4qcjdkWaVgxn094JdVexMj2P6FrT2phibCMBRURIHCOA6kLZR0WCKT3JLgL5pXwZ8gpW8BiaX23qezRw/640?wx_fmt=png\" data-type=\"png\" data-w=\"901\" style=\"border-radius: 6px; display: block; margin: 10px auto; object-fit: contain; box-shadow: rgba(170, 170, 170, 0.48) 0px 0px 6px 0px; max-width: 95% !important; width: 657px !important; height: 366.212px !important;\" _width=\"677px\" class=\"img_loading\" src=\"data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWNgYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg==\" crossorigin=\"anonymous\" alt=\"图片\"></figure><p  style=\"padding-top: 8px;padding-bottom: 8px;line-height: 1.75;margin: 0.8em 0em;font-size: 0.958em;color: #666;\">关于Airtable还有很多有趣好玩的地方比如说自动化，表格右边App，比如说表单提交后发送通知到你的IM上等等，这里面各种组合的可玩空间相当大的！</p><p  style=\"padding-top: 8px;padding-bottom: 8px;line-height: 1.75;margin: 0.8em 0em;font-size: 0.958em;color: #666;\">此外去年底看了下国内也已经有类似的产品了如Treelab、Hipa、Vika等。</p><p  style=\"padding-top: 8px;padding-bottom: 8px;line-height: 1.75;margin: 0.8em 0em;font-size: 0.958em;color: #666;\">总得来讲这是很有趣的一次尝试！</p></section><p><br></p>\n                "}