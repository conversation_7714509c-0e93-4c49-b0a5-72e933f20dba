:root {
  box-sizing: border-box;
}

*,
::before,
::after {
  box-sizing: inherit;
}

:root {
  --space-width: 0.5em;
  --h-padding-width: 1rem;
}

body {
  background-color: #eee;
  --font-primary-color: #1f2329;
  --font-secondary-color: #5f636d;
  --theme-primary-color: #00b0d5;
  --line-color: #dee0e3;
  --outline-color: #ddd;
  --icon-default-color: #5f636d;
  --icon-unselected-color: #b2b8c7;
  --icon-selected-color: #00b0d5;
  --foreground-color: #f6f7f9;
  --background-color: #fff;
  --shadow-color: rgba(0, 0, 0, 0.08);
  --toggle-bg-color: #0f1114;
  --toggle-icon-color: #f8d731;
}

body.dark {
  background-color: #17191e;
  --font-primary-color: #fff;
  --font-secondary-color: #c0c0c0;
  --theme-primary-color: #00b0d5;
  --line-color: #474a4f;
  --outline-color: #d0b284;
  --icon-default-color: #fff;
  --icon-unselected-color: #72807f;
  --icon-selected-color: #00b0d5;
  --foreground-color: #21252b;
  --background-color: #282c34;
  --shadow-color: rgba(0, 0, 0, 0.08);
  --toggle-bg-color: #0f1114;
  --toggle-icon-color: #f8d731;
}

body.dark .modal-container {
  background-color: #282d33;
}

body {
  font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI',
    Roboto, 'Helvetica Neue', Arial, sans-serif;
  color: var(--font-primary-color);
  user-select: none;
  line-height: 1.3;
}

* {
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

*:focus {
  outline-color: var(--outline-color);
  outline-width: 1px;
  outline-offset: -1px;
  outline-style: solid;
}

.clearfix::before,
.clearfix::after {
  display: block;
  content: '';
}
.clearfix::after {
  clear: both;
}

.border-invisible {
  border-color: transparent;
}
