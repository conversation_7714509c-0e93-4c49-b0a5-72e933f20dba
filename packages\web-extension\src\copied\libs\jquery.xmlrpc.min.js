!(function (e) {
  'use strict'
  var t = function () {
    Error.apply(this, arguments)
  }
  ;(t.prototype = new Error()), (t.prototype.type = 'XML-RPC fault')
  var n = (e.xmlrpc = function (t, i) {
    2 === arguments.length ? (i.url = t) : ((i = t), (t = i.url)),
      (i.dataType = 'xml json'),
      (i.type = 'POST'),
      (i.contentType = 'text/xml'),
      (i.converters = { 'xml json': n.parseDocument })
    var r = n.document(i.methodName, i.params || [])
    return (
      (i.data =
        'XMLSerializer' in window
          ? new window.XMLSerializer().serializeToString(r)
          : r.xml),
      e.ajax(i)
    )
  })
  ;(n.createXMLDocument = function () {
    if (document.implementation && 'createDocument' in document.implementation)
      return document.implementation.createDocument(null, null, null)
    var e,
      t,
      n = [
        'MSXML6.DomDocument',
        'MSXML3.DomDocument',
        'MSXML2.DomDocument',
        'MSXML.DomDocument',
        'Microsoft.XmlDom',
      ]
    for (e = 0, t = n.length; t > e; e++)
      try {
        return new ActiveXObject(n[e])
      } catch (i) {}
  }),
    (n.document = function (t, i) {
      var r = n.createXMLDocument(),
        a = function (t) {
          return e(r.createElement(t))
        },
        o = a('methodName').text(t),
        s = a('params').append(
          e.map(i, function (e) {
            var t = a('value').append(n.toXmlRpc(e, a))
            return a('param').append(t)
          })
        ),
        u = a('methodCall').append(o, s)
      return r.appendChild(u.get(0)), r
    })
  var i = function (e) {
    return e === parseInt(e, 10) && !isNaN(e)
  }
  ;(n.toXmlRpc = function (t, n) {
    if (t instanceof r) return t.toXmlRpc(n)
    var a = e.xmlrpc.types,
      o = e.type(t)
    switch (o) {
      case 'undefined':
      case 'null':
        return a.nil.encode(t, n)
      case 'date':
        return a['datetime.iso8601'].encode(t, n)
      case 'object':
        return t instanceof ArrayBuffer
          ? a.base64.encode(t, n)
          : a.struct.encode(t, n)
      case 'number':
        return i(t) ? a['int'].encode(t, n) : a['double'].encode(t, n)
      case 'array':
      case 'boolean':
      case 'string':
        return a[o].encode(t, n)
      default:
        throw new Error('Unknown type', t)
    }
  }),
    (n.parseDocument = function (i) {
      var r = e(i),
        a = r.children('methodresponse'),
        o = a.find('> fault')
      if (0 === o.length) {
        var s = a.find('> params > param > value > *'),
          u = s.toArray().map(n.parseNode)
        return u
      }
      var l = n.parseNode(o.find('> value > *').get(0)),
        c = new t(l.faultString)
      throw (
        ((c.msg = c.message = l.faultString),
        (c.type = c.code = l.faultCode),
        c)
      )
    }),
    (n.parseNode = function (e) {
      if (void 0 === e) return null
      var t = e.nodeName.toLowerCase()
      if (t in n.types) return n.types[t].decode(e)
      throw new Error('Unknown type ' + t)
    }),
    (n.parseValue = function (t) {
      var i = e(t).children()[0]
      return i ? n.parseNode(i) : e(t).text()
    })
  var r = function () {}
  ;(e.xmlrpc.types = {}),
    (n.makeType = function (t, i, a, o) {
      var s
      if (
        ((s = function (e) {
          this.value = e
        }),
        (s.prototype = new r()),
        (s.prototype.tagName = t),
        i)
      ) {
        var u = a,
          l = o
        ;(a = function (e, t) {
          var n = u(e)
          return t(s.tagName).text(n)
        }),
          (o = function (t) {
            return l(e(t).text(), t)
          })
      }
      ;(s.prototype.toXmlRpc = function (e) {
        return s.encode(this.value, e)
      }),
        (s.tagName = t),
        (s.encode = a),
        (s.decode = o),
        (n.types[t.toLowerCase()] = s)
    })
  var a = function (e) {
      return '' + Math.floor(e)
    },
    o = function (e) {
      return parseInt(e, 10)
    }
  n.makeType('int', !0, a, o),
    n.makeType('i4', !0, a, o),
    n.makeType('i8', !0, a, o),
    n.makeType('i16', !0, a, o),
    n.makeType('i32', !0, a, o),
    n.makeType('double', !0, String, function (e) {
      return parseFloat(e, 10)
    }),
    n.makeType('string', !0, String, String),
    n.makeType(
      'boolean',
      !0,
      function (e) {
        return e ? '1' : '0'
      },
      function (e) {
        return '1' === e
      }
    )
  var s = function (e) {
    return 10 > e ? '0' + e : e
  }
  n.makeType(
    'dateTime.iso8601',
    !0,
    function (e) {
      return [
        e.getUTCFullYear(),
        '-',
        s(e.getUTCMonth() + 1),
        '-',
        s(e.getUTCDate()),
        'T',
        s(e.getUTCHours()),
        ':',
        s(e.getUTCMinutes()),
        ':',
        s(e.getUTCSeconds()),
        'Z',
      ].join('')
    },
    function (e) {
      return new Date(e)
    }
  ),
    (n.binary = (function () {
      var e = '=',
        t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'.split(
          ''
        ),
        n = t.reduce(function (e, t, n) {
          return (e[t] = n), e
        }, {})
      return {
        toBase64: function (n) {
          for (
            var i, r = [], a = new Uint8Array(n), o = 0;
            o < a.length;
            o += 3
          )
            (i = (a[o + 0] << 16) + (a[o + 1] << 8) + (a[o + 2] << 0)),
              r.push(t[(i >> 18) % 64]),
              r.push(t[(i >> 12) % 64]),
              r.push(t[(i >> 6) % 64]),
              r.push(t[(i >> 0) % 64])
          for (var s = 3 - (n.byteLength % 3 || 3); s--; )
            r[r.length - s - 1] = e
          return r.join('')
        },
        fromBase64: function (t) {
          var i = t.length,
            r = 3 * (i / 4)
          t.charAt(i - 1) === e && r--, t.charAt(i - 2) === e && r--
          for (
            var a, o = new ArrayBuffer(r), s = new Uint8Array(o), u = 0, l = 0;
            i > u;
            u += 4, l += 3
          )
            (a =
              (n[t[u + 0]] << 18) +
              (n[t[u + 1]] << 12) +
              (n[t[u + 2]] << 6) +
              (n[t[u + 3]] << 0)),
              (s[l + 0] = (a >> 16) % 256),
              (s[l + 1] = (a >> 8) % 256),
              (s[l + 2] = (a >> 0) % 256)
          return o
        },
      }
    })()),
    n.makeType(
      'base64',
      !0,
      function (e) {
        return n.binary.toBase64(e)
      },
      function (e) {
        return n.binary.fromBase64(e)
      }
    ),
    n.makeType(
      'nil',
      !1,
      function (e, t) {
        return t('nil')
      },
      function () {
        return null
      }
    ),
    n.makeType(
      'struct',
      !1,
      function (t, i) {
        var r = i('struct')
        return (
          e.each(t, function (e, t) {
            var a = i('name').text(e),
              o = i('value').append(n.toXmlRpc(t, i))
            r.append(i('member').append(a, o))
          }),
          r
        )
      },
      function (t) {
        return e(t)
          .find('> member')
          .toArray()
          .reduce(function (t, i) {
            var r = e(i),
              a = r.find('> name').text(),
              o = n.parseValue(r.find('> value'))
            return (t[a] = o), t
          }, {})
      }
    ),
    n.makeType(
      'array',
      !1,
      function (t, i) {
        var r = i('array'),
          a = i('data')
        return (
          e.each(t, function (e, t) {
            a.append(i('value').append(n.toXmlRpc(t, i)))
          }),
          r.append(a),
          r
        )
      },
      function (t) {
        return e(t).find('> data > value').toArray().map(n.parseValue)
      }
    ),
    (n.force = function (e, t) {
      return new n.types[e](t)
    })
})(jQuery)
