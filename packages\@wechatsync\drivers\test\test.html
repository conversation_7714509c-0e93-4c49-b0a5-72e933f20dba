<!doctype html>
<div class="lake-content" typography="traditional">
  <div class="ne-quote">
    <p id="u25a386ee" class="ne-p" style="text-align: left"><span class="ne-text" style="color: #0087fc">“</span></p>
    <p id="u5324781f" class="ne-p" style="text-align: left"><span class="ne-text"
        style="color: rgb(53, 53, 53)">年初盘点了下去年都做了些啥，发现文章同步助手仅靠自然流量用户增长了也有10倍之多了（投入的精力并不多的情况下）之前也有想好好完善下，增加更多的同步渠道。</span>
    </p>
    <p id="u294f2b4e" class="ne-p" style="text-align: left"><span class="ne-text" style="color: #0087fc">”</span></p>
  </div>
  <h2 id="KO6lL" style="text-align: left"><span class="ne-text" style="color: rgb(34, 34, 34)">收集需求的需求</span></h2>
  <p id="u2baa5369" class="ne-p"><img
      src="https://cdn.nlark.com/yuque/0/2021/png/735745/1637808353770-4d3c5d3e-a47e-4193-a29f-849aafb78047.png"
      width="800" id="uec680fd5" class="ne-image"></p>
  <p id="u6bd4c0ae" class="ne-p" style="text-align: left"><span class="ne-text"
      style="color: #666">对自媒体人士而言，除主流媒体之外，有很多细分的媒体也是很需要的，比如说【马蜂窝、穷游】这样的媒体对于一位旅游行业从业人员来讲，吸引力和需要程度远在主流媒体之上。</span></p>
  <p id="uaae689d9" class="ne-p" style="text-align: left"><span class="ne-text"
      style="color: #666">考虑到这些情况的存在，很有必要分行业来增加这些细分的渠道的支持，这样对用户整体而言、可用性价值就更大了。</span></p>
  <p id="u27c20764" class="ne-p" style="text-align: left"><span class="ne-text"
      style="color: #666">于是乎：在知道要大量增加渠道的情况下，如何保证增加的渠道是用户所需要的呢？这个时候需求收集就很有用了。</span></p>
  <h2 id="kwsCn" style="text-align: left"><span class="ne-text" style="color: rgb(34, 34, 34)">人肉收集？</span></h2>
  <p id="u26ce1190" class="ne-p" style="text-align: left"><span class="ne-text"
      style="color: #666">起初我想在用户微信群，让大家【给自己的群名称】备注上自己的所在的【行业】，再人肉挨个统计行业，再按行业自行去支持这些行业的头部媒体。</span></p>
  <p id="u23223351" class="ne-p" style="text-align: left"><span class="ne-text"
      style="color: #666">但在具体实施的过程中我觉得人肉统计这件事可能太费劲了、其次用户也不一定有意愿去给自己备注行业（即便是你告诉了他你的动机是为了了解行业支持更多符合大家需要的渠道）。</span></p>
  <h2 id="b53IB" style="text-align: left"><span class="ne-text" style="color: rgb(34, 34, 34)">新型文档应用</span></h2>
  <p id="uac446f03" class="ne-p" style="text-align: left"><span class="ne-text"
      style="color: #666">种种原因这让我想到了Coda，或许可以此定义一个文档、做一个小型的需求收集应用，定义基础表格字段，Button绑定自定义表单录入收据。加上投票计数。</span></p>
  <p id="u246972a9" class="ne-p" style="text-align: left"><span class="ne-text"
      style="color: #666">这样用户可以自行录入一个渠道，其他用户可以给某渠道进行投票计数（计数能了解用户到底更需要哪个渠道、便于确定优先级），后面只需要把这个文档扔到群里就可以了！</span></p>
  <p id="u8d7feade" class="ne-p" style="text-align: left"><span class="ne-text"
      style="color: #666">但事实上Coda相当复杂，即便有很多模板作为开始，我还是感受到了恐慌（在你开始之前你得对Coda的文档和各种特性有一定基础了解，不然根本不知道到底下一步该干啥）</span></p>
  <p id="ufd8fbb34" class="ne-p" style="text-align: left"><span class="ne-text"
      style="color: #666">这个时候只能寻求更简便的产品了，比如说先前有调研过的Airtable。相对而言就简单很多了，以定好的数据表为基础，可以衍生到表单提交 ，按某个字段聚合的（看板、图表）视图！</span>
  </p>
  <h2 id="D3OzU" style="text-align: left"><span class="ne-text" style="color: rgb(34, 34, 34)">信息的提交和公示</span></h2>
  <p id="ubfacc5ed" class="ne-p" style="text-align: left"><span class="ne-text"
      style="color: #666">按【状态】字段聚合表格，方便查看支持情况下的渠道列表</span></p>
  <p id="uc63ef4ee" class="ne-p" style="text-align: left"><span class="ne-text"
      style="color: #666">新增一个表单视图，让用户填写要提交的媒体名称，网址以及大致的备注，在描述区域外链到状态聚合表格。</span></p>
  <p id="u4fc6c7dc" class="ne-p" style="text-align: left"><span class="ne-text"
      style="color: #666">接着就可以把表单链接发到用户微信群里了、放到官网菜单上等。</span></p>
  <p id="u16efb009" class="ne-p" style="text-align: left"><span class="ne-text"
      style="color: #666">在通过Airtable的Iframe嵌套功能，可以很方便的把数据表的状态公示到官网上！再加上提交按钮引导的数据表单上。</span></p>
  <p id="uac169879" class="ne-p" style="text-align: left"><span class="ne-text"
      style="color: #666">信息流动的效率从未如此之高！</span></p>
  <h2 id="m98z8" style="text-align: left"><span class="ne-text" style="color: rgb(34, 34, 34)">总结</span></h2>
  <p id="u9234d331" class="ne-p" style="text-align: left"><span class="ne-text"
      style="color: #666">果不其然，从1月22号加上到今天，已经收集了大概20多个待支持的渠道。这些积压的需求，进而引发了对提高新渠道对接效率的思考。从而有了新的改进需求，以及是否可以有效利用用户的力量来帮助对接更多的渠道等。</span>
  </p>
  <p id="u154fa755" class="ne-p" style="text-align: left"><span class="ne-text"
      style="color: #666">关于Airtable还有很多有趣好玩的地方比如说自动化，表格右边App，比如说表单提交后发送通知到你的IM上等等，这里面各种组合的可玩空间相当大的！</span></p>
  <p id="uffdf263d" class="ne-p" style="text-align: left"><span class="ne-text"
      style="color: #666">此外去年底看了下国内也已经有类似的产品了如Treelab、Hipa、Vika等。</span></p>
  <p id="ud47e27ef" class="ne-p" style="text-align: left"><span class="ne-text"
      style="color: #666">总得来讲这是很有趣的一次尝试！</span></p>
  <p id="ud6a526ee" class="ne-p"><br></p>
  <p id="u5db2a731" class="ne-p"><span class="ne-text"> </span></p>
</div>