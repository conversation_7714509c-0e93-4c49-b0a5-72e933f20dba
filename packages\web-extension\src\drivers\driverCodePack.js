window.driver = "var modules;\n/******/ (() => { // webpackBootstrap\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 150:\n/***/ (() => {\n\n\nclass Mp163 {\n  constructor() {\n    this.version = '0.0.1'\n        // modify origin headers\n    modifyRequestHeaders('mp.sohu.com/mpbp', {\n        Origin: 'https://mp.sohu.com',\n      Referer: 'https://mp.sohu.com/'\n    }, [\n        '*://mp.sohu.com/mpbp/*',\n    ])\n  }\n\n  async getMetaData() {\n    const { data } = await axios.get('https://mp.163.com/wemedia/navinfo.do?_=' + Date.now())\n    console.log(data)\n    if(data.code != 1) {\n        throw new Error('not found')\n    }\n    const accountInfo = data.data\n    _souhuCacheMeta = accountInfo\n    return {\n      uid: accountInfo.wemediaId,\n      title: accountInfo.tname,\n      avatar: accountInfo.icon,\n      type: '163',\n      displayName: '网易号',\n      supportTypes: ['html'],\n      home: 'https://mp.163.com/index.html#/article/manage',\n      icon: 'https://mp.163.com/favicon.ico',\n    }\n  }\n\n  async addPost(post, _instance) {\n    return {\n      status: 'success',\n      post_id: 0,\n    }\n  }\n\n  async editPost(post_id, post) {\n\n    // todo\n    return {\n      status: 'success',\n      post_id: post_id,\n      draftLink: 'https://mp.sohu.com/mpfe/v3/main/news/addarticle?spm=smmp.articlelist.0.0&contentStatus=2&id=' + post_id,\n    }\n  }\n\n  async uploadFile(file) {\n    var uploadUrl = 'https://upload.ws.126.net/picupload?_=*************&wemediaId='\n    var file = new File([file.bits], 'temp', {\n      type: file.type\n    });\n    var formdata = new FormData()\n    formdata.append('file', file)\n    formdata.append('from', 'neteasecode_mp')\n    var res = await axios({\n      url: uploadUrl,\n      method: 'post',\n      data: formdata,\n      headers: { 'Content-Type': 'multipart/form-data' },\n    })\n\n    return [\n      {\n        url: res.data.data.url,\n      },\n    ]\n  }\n\n  async preEditPost(post) {\n    var div = $('<div>')\n    $('body').append(div)\n\n    try {\n      div.html(post.content)\n      var doc = div\n      // var pres = doc.find(\"pre\");\n      tools.processDocCode(div)\n      tools.makeImgVisible(div)\n\n      var tempDoc = $('<div>').append(doc.clone())\n      post.content =\n        tempDoc.children('div').length == 1\n          ? tempDoc.children('div').html()\n          : tempDoc.html()\n\n      console.log('after.predEdit', post.content)\n    } catch (e) {\n      console.log('preEdit.error', e)\n    }\n  }\n}\n\n\n/***/ }),\n\n/***/ 258:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ _51CtoAdapter)\n/* harmony export */ });\nvar _cacheMeta = null\n\nclass _51CtoAdapter {\n  constructor(config) {\n    // this.skipReadImage = true\n    this.config = config\n    this.name = '51cto'\n  }\n\n  async getMetaData() {\n    var res = await $.get('https://blog.51cto.com/blogger/publish')\n    var parser = new DOMParser()\n    var htmlDoc = parser.parseFromString(res, 'text/html')\n    var img = htmlDoc.querySelector('li.more.user > a > img')\n    var link = img.parentNode.href\n    var pie = link.split('/')\n    // pie.pop()\n    var uid = pie.pop()\n    console.log(link)\n    var scrs = [].slice\n      .call(htmlDoc.querySelectorAll('script'))\n      .filter(_ => _.innerText.indexOf('sign') > -1)\n\n    var uploadSign = null;\n    if (scrs.length) {\n      try {\n        var dataStr = scrs[0].innerText\n        var rawStr = dataStr.substring(\n          dataStr.indexOf('sign'),\n          dataStr.indexOf('uploadUrl', dataStr.indexOf('sign'))\n        )\n        var signStr = rawStr\n          .replace('var', '')\n          .trim()\n          .replace(\"sign = '\", '')\n          .replace(\"';\", '')\n          .trim()\n          uploadSign = signStr\n      } catch (e) {\n        console.log('51cto', e)\n      }\n    }\n      _cacheMeta = {\n        rawStr: rawStr,\n        uploadSign: uploadSign,\n        csrf: htmlDoc\n          .querySelector('meta[name=csrf-token]')\n          .getAttribute('content'),\n      }\n    console.log('51cto', _cacheMeta)\n    return {\n      uid: uid,\n      title: uid,\n      avatar: img.src,\n      type: '51cto',\n      displayName: '51CTO',\n      supportTypes: ['markdown', 'html'],\n      home: 'https://blog.51cto.com/blogger/publish',\n      icon: 'https://blog.51cto.com/favicon.ico',\n    }\n  }\n\n  async addPost(post) {\n    return {\n      status: 'success',\n      post_id: 0,\n    }\n  }\n\n  async editPost(post_id, post) {\n    var postStruct = {}\n\n    if (post.markdown) {\n      postStruct = {\n        title: post.post_title,\n        copy_code: 1,\n        is_old: 0,\n        content: post.markdown,\n        _csrf: _cacheMeta.csrf,\n      }\n    } else {\n      postStruct = {\n        blog_type: null,\n        title: post.post_title,\n        copy_code: 1,\n        content: post.post_content,\n        pid: '',\n        cate_id: '',\n        custom_id: '',\n        tag: '',\n        abstract:'',\n        is_hide: 0,\n        did: '',\n        blog_id: '',\n        is_old: 1,\n        _csrf: _cacheMeta.csrf,\n        // editorValue: null,\n      }\n    }\n\n    var res = await $.ajax({\n      url: 'https://blog.51cto.com/blogger/draft',\n      type: 'POST',\n      dataType: 'JSON',\n      data: postStruct,\n    })\n\n    if (!res.data) {\n      throw new Error(res.message)\n    }\n\n    return {\n      status: 'success',\n      post_id: res.data.did,\n      draftLink: 'https://blog.51cto.com/blogger/draft/' + res.data.did,\n    }\n  }\n\n  async uploadFile(file) {\n    var src = file.src\n    // var csrf = this.config.state.csrf\n    var uploadUrl = 'https://upload.51cto.com/index.php?c=upload&m=upimg&orig=b'\n    var file = new File([file.bits], 'temp', {\n      type: file.type,\n    })\n    var formdata = new FormData()\n\n    formdata.append('sign', _cacheMeta.uploadSign)\n    // formdata.append('file', file)\n    formdata.append('file', file, new Date().getTime() + '.jpg')\n    formdata.append('type', file.type)\n    formdata.append('id', 'WU_FILE_1')\n    formdata.append('fileid', `uploadm-` + Math.floor(Math.random() * 1000000))\n    // formdata.append('name', new Date().getTime() + '.jpg')\n    formdata.append('lastModifiedDate', new Date().toString())\n    formdata.append('size', file.size)\n    var res = await axios({\n      url: uploadUrl,\n      method: 'post',\n      data: formdata,\n      headers: { 'Content-Type': 'multipart/form-data' },\n    })\n\n    if (res.data.status === false) {\n      throw new Error('图片上传失败 ' + src)\n    }\n    // http only\n    console.log('uploadFile', res)\n    var id = Math.floor(Math.random() * 100000)\n    return [\n      {\n        id: id,\n        object_key: id,\n        url: `https://s4.51cto.com/` + res.data.data,\n        // size: res.data.data.size,\n        // images: [res.data],\n      },\n    ]\n  }\n\n  async preEditPost(post) {\n    var div = $('<div>')\n    $('body').append(div)\n\n    try {\n      div.html(post.content)\n      var doc = div\n      // var pres = doc.find(\"pre\");\n      tools.processDocCode(div)\n      tools.makeImgVisible(div)\n\n      var tempDoc = $('<div>').append(doc.clone())\n      post.content =\n        tempDoc.children('div').length == 1\n          ? tempDoc.children('div').html()\n          : tempDoc.html()\n\n      console.log('after.predEdit', post.content)\n    } catch (e) {\n      console.log('preEdit.error', e)\n    }\n  }\n\n  // editImg(img, source) {\n  //   img.attr('size', source.size)\n  // }\n  //   <img class=\"\" src=\"http://p2.pstatp.com/large/pgc-image/bc0a9fc8e595453083d85deb947c3d6e\" data-ic=\"false\" data-ic-uri=\"\" data-height=\"1333\" data-width=\"1000\" image_type=\"1\" web_uri=\"pgc-image/bc0a9fc8e595453083d85deb947c3d6e\" img_width=\"1000\" img_height=\"1333\"></img>\n}\n\n\n/***/ }),\n\n/***/ 174:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BaseAdapter)\n/* harmony export */ });\nclass BaseAdapter {\n\n  async getMetaData () {\n    // 组装元数据：调用平台 api 获取用户信息和平台信息，并返回组装数据\n\n  }\n\n  async preEditPost(post) {\n    // 内容预处理：预处理平台无法兼容的文本内容\n  }\n\n  async addPost(post) {\n    // 创建文章：调用平台 api 创建草稿\n  }\n\n  async uploadFile(file) {\n    // 上传图片：调用平台 api 上传图片\n  }\n\n  async editPost (postId, post) {\n    // 更新文章：调用平台 api 更新文章（同步助手内部通过该接口替换文章内图片地址）\n  }\n}\n\n\n/***/ }),\n\n/***/ 617:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CSDNAdapter)\n/* harmony export */ });\n\n\n// const TurndownService = turndown\n\nfunction createUuid() {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n    var r = Math.random() * 16 | 0;\n    var v = c === 'x' ? r : r & 0x3 | 0x8;\n    return v.toString(16);\n  });\n};\n\nfunction signCSDN(apiPath, contentType = 'application/json') {\n\tvar once = createUuid()\n  var signStr = `POST\n*/*\n\napplication/json\n\nx-ca-key:203803574\nx-ca-nonce:${once}\n${apiPath}`\n\tvar hash = CryptoJS.HmacSHA256(signStr, \"9znpamsyl2c7cdrr9sas0le9vbc3r6ba\");\n\tvar hashInBase64 = CryptoJS.enc.Base64.stringify(hash);\n  return {\n    accept: '*/*',\n    'content-type': contentType,\n    'x-ca-key': 203803574,\n    'x-ca-nonce': once,\n    'x-ca-signature': hashInBase64,\n    'x-ca-signature-headers':'x-ca-key,x-ca-nonce'\n  }\n}\n\nfunction validateFileExt(ext) {\n  switch (ext.toLowerCase()) {\n    case 'jpg':\n    case 'png':\n    case 'jpeg':\n    case 'gif':\n      return true\n    default:\n      return false\n  }\n}\n\nclass CSDNAdapter {\n  constructor() {\n    this.name = 'csdn'\n    modifyRequestHeaders('bizapi.csdn.net/', {\n    \tOrigin: 'https://editor.csdn.net',\n      Referer: 'https://editor.csdn.net/'\n    }, [\n    \t'*://bizapi.csdn.net/*',\n    ])\n  }\n\n  async getMetaData() {\n    var res = await $.get('https://me.csdn.net/api/user/show')\n    return {\n      uid: res.data.csdnid,\n      title: res.data.username,\n      avatar: res.data.avatarurl,\n      type: 'csdn',\n      displayName: 'CSDN',\n      supportTypes: ['markdown', 'html'],\n      home: 'https://mp.csdn.net/',\n      icon: 'https://g.csdnimg.cn/static/logo/favicon32.ico',\n    }\n  }\n\n  async requestUpload(filename) {\n    const api = 'https://imgservice.csdn.net/direct/v1.0/image/upload?watermark=&type=blog&rtype=markdown'\n    const fileExt = file.name.split('.').pop()\n    if (!validateFileExt(fileExt)) {\n      return null\n    }\n\n    var res = await axios({\n      url: api,\n      method: 'get',\n      headers: {\n        'x-image-app': 'direct_blog',\n        'x-image-suffix': fileExt,\n        'x-image-dir': 'direct'\n      },\n    })\n    if (res.status !== 200 || res.data.code !== 200) {\n      console.log(res)\n      return null\n    }\n    return res.data.data\n  }\n\n  async uploadFile(file) {\n    const uploadData = await requestUpload(file.name)\n    if (!uploadData) {\n      return [{url: file.src}]\n    }\n\n    const uploadUrl = uploadData.host\n    const form = new FormData()\n    form.append('key', uploadData.filePath)\n    form.append('policy', uploadData.policy)\n    form.append('OSSAccessKeyId', uploadData.accessId)\n    form.append('success_action_status', '200')\n    form.append('signature', uploadData.signature)\n    form.append('callback', uploadData.callbackUrl)\n\n    const f = new File([file.bits], 'temp', {\n      type: file.type\n    });\n    form.append('file', f)\n\n    var res = await axios({\n      url: uploadUrl,\n      method: 'post',\n      data: form\n    })\n    if (res.status !== 200 || res.data.code !== 200) {\n      console.log(res)\n      return [{url: file.src}]\n    }\n    return [{url: res.data.data.imageUrl}]\n  }\n\n  async addPost(post) {\n    return {\n      status: 'success',\n      post_id: 0,\n    }\n  }\n  async editPost(post_id, post) {\n\t\t// 支持HTML\n    if(!post.markdown) {\n      var turndownService = new turndown()\n    \tturndownService.use(tools.turndownExt)\n    \tvar markdown = turndownService.turndown(post.post_content)\n    \tconsole.log(markdown);\n    \tpost.markdown = markdown\n    }\n\n\t\tvar postStruct = {\n    \t    content: post.post_content,\n          markdowncontent: post.markdown,\n          not_auto_saved: \"1\",\n          readType: \"public\",\n          source: \"pc_mdeditor\",\n          status: 2,\n          title: post.post_title,\n    }\n\t\tvar headers = signCSDN('/blog-console-api/v3/mdeditor/saveArticle')\n    var res = await axios.post(\n      'https://bizapi.csdn.net/blog-console-api/v3/mdeditor/saveArticle',\n      postStruct,\n      {\n        headers: headers\n    })\n  \tpost_id = res.data.data.id\n    console.log(res)\n    return {\n      status: 'success',\n      post_id: post_id,\n      draftLink: 'https://editor.csdn.net/md?articleId=' + post_id,\n    }\n  }\n\n  async preEditPost(post) {\n    var div = $('<div>')\n    $('body').append(div)\n    try {\n      div.html(post.content)\n      var doc = div\n      tools.processDocCode(div)\n      tools.makeImgVisible(div)\n      var tempDoc = $('<div>').append(doc.clone())\n      post.content =\n        tempDoc.children('div').length == 1\n          ? tempDoc.children('div').html()\n          : tempDoc.html()\n\n      console.log('after.predEdit', post.content)\n    } catch (e) {\n      console.log('preEdit.error', e)\n    }\n  }\n}\n\n\n/***/ }),\n\n/***/ 308:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CnblogAdapter)\n/* harmony export */ });\n\nfunction getCookie(name, cookieStr) {\n  let arr,\n    reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)')\n  if ((arr = cookieStr.match(reg))) {\n    return unescape(arr[2])\n  } else {\n    return ''\n  }\n}\n\nfunction parseTokenAndToHeaders(details, cookieKey, headerKey) {\n  var cookieHeader = details.requestHeaders.filter(h => {\n    return h.name.toLowerCase() == 'cookie'\n  })\n  if (cookieHeader.length) {\n    var cookieStr = cookieHeader[0].value\n    var _xsrf = getCookie(cookieKey, cookieStr)\n    if (_xsrf) {\n      details.requestHeaders.push({\n        name: headerKey,\n        value: _xsrf,\n      })\n    }\n  }\n}\n\n\nclass CnblogAdapter {\n  constructor() {\n    this.name = 'cnblog'\n    //\n    this.skipReadImage = true\n    modifyRequestHeaders('i.cnblogs.com/', {\n    \tOrigin: 'https://i.cnblogs.com',\n      Referer: 'https://i.cnblogs.com/'\n    }, [\n    \t'*://i.cnblogs.com/*',\n    ], function(details) {\n\t\t\t// parse token from cookie inject to headers\n      if (details.url.indexOf('i.cnblogs.com/api') > -1) {\n        parseTokenAndToHeaders(details, 'XSRF-TOKEN', 'x-xsrf-token')\n      }\n    })\n  }\n\n  async getMetaData() {\n    var res = await $.get('https://home.cnblogs.com/user/CurrentUserInfo')\n    var parser = new DOMParser()\n    var htmlDoc = parser.parseFromString(res, 'text/html')\n    var img = htmlDoc.getElementsByClassName('pfs')[0]\n    var link = img.parentNode.href\n    var pie = link.split('/')\n    pie.pop()\n    var uid = pie.pop()\n    console.log(link)\n    return {\n      uid: uid,\n      title: uid,\n      avatar: img.src,\n      type: 'cnblog',\n      displayName: 'CnBlog',\n      supportTypes: ['markdown', 'html'],\n      home: 'https://i.cnblogs.com/EditArticles.aspx?IsDraft=1',\n      icon: 'https://i.cnblogs.com/favicon.ico',\n    }\n  }\n\n  async uploadFile(file) {\n    //\n    return [\n      {\n        url: file.src,\n      }\n    ]\n  }\n\n  async addPost(post) {\n    if(!post.markdown) {\n      var turndownService = new turndown()\n      turndownService.use(tools.turndownExt)\n    \tvar markdown = turndownService.turndown(post.post_content)\n    \tconsole.log(markdown);\n    \tpost.markdown = markdown\n    }\n    var postId = null\n    try {\n      var res = await axios.post('https://i.cnblogs.com/api/posts', {\n          \"id\": null,\n          \"postType\": 2,\n          \"accessPermission\": 0,\n          \"title\": post.post_title,\n          \"url\": null,\n          \"postBody\": post.markdown,\n          \"categoryIds\": null,\n          \"inSiteCandidate\": false,\n          \"inSiteHome\": false,\n          \"siteCategoryId\": null,\n          \"blogTeamIds\": null,\n          \"isPublished\": false,\n          \"displayOnHomePage\": false,\n          \"isAllowComments\": true,\n          \"includeInMainSyndication\": true,\n          \"isPinned\": false,\n          \"isOnlyForRegisterUser\": false,\n          \"isUpdateDateAdded\": false,\n          \"entryName\": null,\n          \"description\": null,\n          \"tags\": null,\n          \"password\": null,\n          \"datePublished\": new Date().toISOString(),\n          \"isMarkdown\": true,\n          \"isDraft\": true,\n          \"autoDesc\": null,\n          \"changePostType\": false,\n          \"blogId\": 0,\n          \"author\": null,\n          \"removeScript\": false,\n          \"clientInfo\": null,\n          \"changeCreatedTime\": false,\n          \"canChangeCreatedTime\": false\n        })\n      console.log('CNBLOG addPost', res)\n      postId = res.data.id\n    } catch (e) {\n    }\n\n    return {\n      status: 'success',\n      post_id: postId,\n    }\n  }\n\n  async editPost(post_id, post) {\n    return {\n      status: 'success',\n      post_id: post_id,\n      draftLink:\n        'https://i.cnblogs.com/articles/edit;postId=' + post_id,\n    }\n  }\n\n  async preEditPost(post) {\n    var div = $('<div>')\n    $('body').append(div)\n\n    try {\n      div.html(post.content)\n      var doc = div\n      // var pres = doc.find(\"pre\");\n      tools.processDocCode(div)\n      tools.makeImgVisible(div)\n\n      var tempDoc = $('<div>').append(doc.clone())\n      post.content =\n        tempDoc.children('div').length == 1\n          ? tempDoc.children('div').html()\n          : tempDoc.html()\n\n      console.log('after.predEdit', post.content)\n    } catch (e) {\n      console.log('preEdit.error', e)\n    }\n  }\n}\n\n\n\n/***/ }),\n\n/***/ 159:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DiscuzAdapter)\n/* harmony export */ });\n// https://www.51hanghai.com/portal.php?mod=portalcp&ac=article\nvar _cacheMeta = null;\n\nclass DiscuzAdapter {\n  constructor(config) {\n    this.config = config || {}\n    var url = this.config.url\n    this.pubUrl = `${url}/portal.php?mod=portalcp&ac=article`\n    // this.upUrl = `${url}/misc.php?mod=swfupload&action=swfupload&operation=portal`\n    this.upUrl = `${url}/misc.php?mod=swfupload&action=swfupload&operation=upload`\n    this.name = 'discuz'\n\n    // this.skipReadImage = true\n  }\n\n  async getMetaData() {\n    var url = this.config.url;\n    console.log('disduz', this.config)\n    var postUrl = `${url}/portal.php?mod=portalcp&ac=article`\n    var favIcon = `${url}/favicon.ico`\n    var res = await $.get(postUrl)\n    var parser = new DOMParser()\n    var htmlDoc = parser.parseFromString(res, 'text/html')\n    var nickname = htmlDoc.querySelector('.vwmy').innerText\n    var img = htmlDoc.querySelector('.avt img').src\n\n    _cacheMeta = {\n      uid: img.split('uid=')[1].split('&size')[0],\n      title: nickname,\n      avatar: img,\n      type: 'discuz',\n      displayName: 'Discuz',\n      supportTypes: ['html'],\n      config: this.config,\n      home: postUrl,\n      icon: favIcon,\n    }\n\n    var uploadSrciptBlocks = [].slice.apply(htmlDoc.querySelectorAll('script')).filter(_ => _.innerText.indexOf('SWFUpload') > -1);\n    if(uploadSrciptBlocks.length) {\n      var scripText = uploadSrciptBlocks[0].innerText\n      var startTag = 'post_params:'\n      var strIndex =  scripText.indexOf(startTag)\n      var dataStr = scripText.substring(strIndex + startTag.length, scripText.indexOf('},', strIndex) + 1);\n      var post_params = new Function(\n        'var config = ' +\n        dataStr +\n          '; return config;'\n      )();\n      _cacheMeta.uploadToken = post_params.hash\n      _cacheMeta.raw = post_params\n    }\n    // var parser = new DOMParser()\n    // var htmlDoc = parser.parseFromString(res, 'text/html')\n    // var img = htmlDoc.querySelector('li.more.user > a > img')\n    return _cacheMeta\n  }\n\n  async addPost(post) {\n    return {\n      status: 'success',\n      post_id: 0,\n    }\n  }\n\n  async editPost(post_id, post) {\n    var postStruct = {}\n\n    if (post.markdown) {\n      postStruct = {\n        title: post.post_title,\n        catid: 24,\n        content: post.markdown,\n        romotepic: 1,\n      }\n    } else {\n      postStruct = {\n        title: post.post_title,\n        catid: 24,\n        romotepic: 1,\n        content: post.post_content,\n      }\n    }\n\n    // title: test\n    // highlight_style[0]:\n    // highlight_style[1]:\n    // highlight_style[2]:\n    // highlight_style[3]:\n    // htmlname:\n    // oldhtmlname:\n    // pagetitle:\n    // catid: 24\n    // from:\n    postStruct.fromurl = null\n    postStruct.dateline = null\n    postStruct.from_idtyp = `tid`\n    postStruct.from_id = 0\n    postStruct.id = 0\n    postStruct.idtype = `tid`;\n    postStruct.url = null;\n    postStruct.author = null;\n    // conver: a:3:{s:3:\"pic\";s:0:\"\";s:5:\"thumb\";i:0;s:6:\"remote\";i:0;}\n    // file: (binary)\n    // file: (binary)\n    // content: test\n    // romotepic: 1\n    // summary:\n    // aid:\n    // cid:\n    // attach_ids:\n    // articlesubmit: true\n    postStruct.articlesubmit = true;\n    postStruct.formhash = `caa4c6cb`\n    postStruct.conver = `a:3:{s:3:\"pic\";s:0:\"\";s:5:\"thumb\";i:0;s:6:\"remote\";i:0;}`;\n    // var res = await $.ajax({\n    //   url: this.pubUrl,\n    //   type: 'POST',\n    //   dataType: 'JSON',\n    //   data: postStruct,\n    // })\n    // if (!res.data) {\n    //   throw new Error(res.message)\n    // }\n    setCache('discuz_cache', JSON.stringify(postStruct))\n\n    return {\n      status: 'success',\n      post_id: 0,\n      draftLink: `${this.config.url}/forum.php?mod=guide&view=my&loaddraft`,\n    }\n  }\n\n  async uploadFile(file) {\n    var id = Date.now() + Math.floor(Math.random()* 1000);\n    return [\n      {\n        id: id,\n        object_key: id,\n        url: file.src,\n      },\n    ]\n    var src = file.src\n    // var file = new File([file.bits], 'temp', {\n    //   type: file.type,\n    // })\n    var blob = new Blob([file.bits], {\n      type: file.type,\n    })\n    var formdata = new FormData()\n\n    formdata.append('uid', _cacheMeta.uid)\n    formdata.append('hash', _cacheMeta.uploadToken)\n    formdata.append('filetype', '.jpg')\n    formdata.append('type', 'image')\n    formdata.append('aid', '0')\n    // formdata.append('catid', '19')\n    // formdata.append('Filedata', blob)\n    formdata.append('Filedata', blob, new Date().getTime() + '.jpg')\n    formdata.append('size', blob.size)\n    formdata.append('id', 'WU_FILE_1')\n    var res = await axios({\n      url: this.upUrl,\n      method: 'post',\n      data: formdata,\n      headers: { 'Content-Type': 'multipart/form-data' },\n    })\n\n    var imageHtmlRes = await axios.get(`${this.config.url}/forum.php?mod=ajax&action=imagelist&type=single&pid=0&aids=` + res.data)\n    var parser = new DOMParser()\n    var htmlDoc = parser.parseFromString(imageHtmlRes.data, 'text/html')\n    var imgSrc = `${this.config.url}/` + htmlDoc.querySelector(\"img\").getAttribute('src')\n    console.log('upload.res', imageHtmlRes)\n    var imageId = Date.now() +Math.floor( Math.random() * 10);\n    // if (res.data.aid === false) {\n    //   throw new Error('图片上传失败 ' + src)\n    // }\n    // http only\n    console.log('uploadFile', res)\n    return [\n       {\n        id: imageId,\n        object_key: imageId,\n        url: imgSrc\n      },\n      // {\n      //   id: res.data.aid,\n      //   object_key: res.data.aid,\n      //   url: res.data.bigimg,\n      //   // size: res.data.data.size,\n      //   // images: [res.data],\n      // },\n    ]\n  }\n\n  async preEditPost(post) {\n    var div = $('<div>')\n    $('body').append(div)\n\n    div.html(post.content)\n    // var org = $(post.content);\n    // var doc = $('<div>').append(org.clone());\n    var doc = div\n    var pres = doc.find('a')\n    for (let mindex = 0; mindex < pres.length; mindex++) {\n      const pre = pres.eq(mindex)\n      try {\n        pre.after(pre.html()).remove()\n      } catch (e) {}\n    }\n\n    var pres = doc.find('iframe')\n    for (let mindex = 0; mindex < pres.length; mindex++) {\n      const pre = pres.eq(mindex)\n      try {\n        pre.remove()\n      } catch (e) {}\n    }\n\n    try {\n      const images = doc.find('img')\n      for (let index = 0; index < images.length; index++) {\n        const image = images.eq(index)\n        const imgSrc = image.attr('src')\n        if (imgSrc && imgSrc.indexOf('.svg') > -1) {\n          console.log('remove svg Image')\n          image.remove()\n        }\n      }\n      const qqm = doc.find('qqmusic')\n      qqm.next().remove()\n      qqm.remove()\n    } catch (e) {}\n\n    post.content = $('<div>')\n      .append(doc.clone())\n      .html()\n    console.log('post', post)\n  }\n\n  editImg(img, source) {\n    img.removeAttr('crossorigin')\n    img.attr('referrerpolicy', \"no-referrer\")\n  }\n}\n\n\n/***/ }),\n\n/***/ 341:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ JuejinAdapter)\n/* harmony export */ });\nclass JuejinAdapter {\n  constructor(ac) {\n    this.version = '0.0.2'\n    this.name = 'juejin'\n\n\t\t// modify origin headers\n    modifyRequestHeaders('api.juejin.cn', {\n    \tOrigin: 'https://juejin.cn',\n      Referer: 'https://juejin.cn/'\n    }, [\n    \t'*://api.juejin.cn/*',\n    ])\n  }\n\n  async getMetaData() {\n    var data = await $.get('https://api.juejin.cn/user_api/v1/user/get')\n    console.log(data)\n    return {\n      uid: data.data.user_id,\n      title: data.data.user_name,\n      avatar: data.data.avatar_large,\n      type: 'juejin',\n      displayName: '掘金',\n      raw: data.data,\n      supportTypes: ['markdown', 'html'],\n      home: 'https://juejin.cn/editor/drafts',\n      icon: 'https://gold-cdn.xitu.io/favicons/favicon.ico',\n    }\n  }\n\n  async addPost(post, _instance) {\n    return {\n      status: 'success',\n      post_id: 0,\n    }\n  }\n\n  async editPost(post_id, post) {\n    console.log('TurndownService', turndown)\n    var turndownService = new turndown()\n    turndownService.use(tools.turndownExt)\n    var markdown = turndownService.turndown(post.post_content)\n    const { data } = await axios.post('https://api.juejin.cn/content_api/v1/article_draft/create', {\n        brief_content: '',\n      \tcategory_id: '0',\n        cover_image: '',\n      \tedit_type: 10,\n      \thtml_content: \"deprecated\",\n      \tlink_url: \"\",\n      \tmark_content: markdown,\n      \ttag_ids: [],\n      \ttitle: post.post_title\n    })\n    var post_id = data.data.id\n    console.log(data)\n    return {\n      status: 'success',\n      post_id: post_id,\n      draftLink: 'https://juejin.cn/editor/drafts/' + post_id,\n    }\n  }\n\n  async uploadFile(file) {\n    var src = file.src\n    var imageId = Date.now() + Math.floor(Math.random() * 1000)\n    const { data } = await axios.post('https://juejin.cn/image/urlSave', {\n    \turl: src\n    })\n    return [\n      {\n        id: imageId,\n        object_key: imageId,\n        url: data.data,\n      },\n    ]\n  }\n\n  async preEditPost(post) {\n    var div = $('<div>')\n    $('body').append(div)\n    try {\n      console.log('zihu.Juejin')\n      div.html(post.content)\n      var doc = div\n      tools.processDocCode(div)\n      tools.makeImgVisible(div)\n\n      var tempDoc = $('<div>').append(doc.clone())\n      post.content =\n        tempDoc.children('div').length == 1\n          ? tempDoc.children('div').html()\n          : tempDoc.html()\n\n      console.log('after.predEdit', post.content)\n    } catch (e) {\n      console.log('preEdit.error', e)\n    }\n  }\n\n  addPromotion(post) {\n    var sharcode = `<blockquote><p>本文使用 <a href=\"https://juejin.cn/post/6940875049587097631\" class=\"internal\">文章同步助手</a> 同步</p></blockquote>`\n    post.content = post.content.trim() + `${sharcode}`\n  }\n}\n\n\n/***/ }),\n\n/***/ 920:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SegmentfaultAdapter)\n/* harmony export */ });\nclass SegmentfaultAdapter {\n  constructor() {\n    this.name = 'segmentfault'\n    modifyRequestHeaders('gateway.segmentfault.com/', {\n    \tOrigin: 'https://segmentfault.com',\n      Referer: 'https://segmentfault.com/'\n    }, [\n    \t'*://gateway.segmentfault.com/*',\n    ])\n  }\n\n  async getMetaData() {\n    var res = await $.get('https://segmentfault.com/user/settings')\n    var parser = new DOMParser()\n    var htmlDoc = parser.parseFromString(res, 'text/html')\n    var link = htmlDoc.getElementsByClassName('user-avatar')[0]\n    if (!link) {\n      throw Error('not found')\n    }\n\n    var uid = link.href.split('/').pop()\n    var avatar = link.style['background-image']\n      .replace('url(\"', '')\n      .replace('\")', '')\n    console.log(\n      link.href,\n      link.style['background-image'].replace('url(\"', '').replace('\")', '')\n    )\n\n    return {\n      uid: uid,\n      title: uid,\n      avatar: avatar,\n      type: 'segmentfault',\n      displayName: 'Segmentfault',\n      supportTypes: ['markdown', 'html'],\n      home: 'https://segmentfault.com/user/draft',\n      icon:\n        'https://imgcache.iyiou.com/Company/2016-05-11/cf-segmentfault.jpg',\n    }\n  }\n\n  async addPost(post) {\n    return {\n      status: 'success',\n      post_id: 0\n    }\n  }\n\n  async editPost(post_id, post) {\n    if(!post.markdown) {\n      var turndownService = new turndown()\n      turndownService.use(tools.turndownExt)\n      var markdown = turndownService.turndown(post.post_content)\n      post.markdown = markdown\n      console.log(markdown)\n    }\n\n    const pageHtml = await $.get('https://segmentfault.com/write')\n    const markStr = 'window.g_initialProps = '\n    const authIndex = pageHtml.indexOf(markStr)\n    if(authIndex == -1) {\n    \tthrow new Error('登录失效 authIndex=' + authIndex)\n    }\n\n    const authTokenStr = pageHtml.substring(authIndex + markStr.length, pageHtml.indexOf(`;\n\t</script>`, authIndex))\n\n    const pageConfig = new Function(\n      'var config = ' +\n        authTokenStr +\n        '; return config;'\n    )();\n\n    const token = pageConfig.global.sessionInfo.key\n\n    var postStruct = {\n      \"title\":post.post_title,\n      \"tags\":[],\n      \"text\": post.markdown,\n      \"object_id\":\"\",\n      \"type\":\"article\"\n    }\n\n    var res = await axios.post('https://gateway.segmentfault.com/draft', postStruct, {\n    \theaders: {\n      \ttoken: token,\n        accept: '*/*',\n    \t\t'content-type': 'application/json',\n      }\n    })\n    post_id = res.data.id\n    return {\n      status: 'success',\n      post_id: post_id,\n      draftLink: 'https://segmentfault.com/write?draftId=' + post_id,\n    }\n  }\n\n  async uploadFile(file) {\n    var formdata = new FormData()\n    var blob = new Blob([file.bits])\n    formdata.append('image', blob)\n    var res = await axios({\n      url: 'https://segmentfault.com/img/upload/image',\n      method: 'post',\n      data: formdata,\n      headers: { 'Content-Type': 'multipart/form-data' },\n    })\n    if(res.data[0] == 1) {\n      throw new Error(res.data[1])\n    }\n    var url = res.data[1]\n    return [\n      {\n        id: res.data[2],\n        object_key: res.data[2],\n        url: url,\n      },\n    ]\n  }\n\n  async preEditPost(post) {\n    var div = $('<div>')\n    $('body').append(div)\n\n    try {\n      // post.content = post.content.replace(/\\>\\s+\\</g,'');\n      console.log('zihu.Juejin')\n      div.html(post.content)\n      // var org = $(post.content);\n      // var doc = $('<div>').append(org.clone());\n      var doc = div\n      // var pres = doc.find(\"pre\");\n      tools.processDocCode(div)\n      tools.makeImgVisible(div)\n\n      var tempDoc = $('<div>').append(doc.clone())\n      post.content =\n        tempDoc.children('div').length == 1\n          ? tempDoc.children('div').html()\n          : tempDoc.html()\n\n      console.log('after.predEdit', post.content)\n    } catch (e) {\n      console.log('preEdit.error', e)\n    }\n  }\n\n  async uploadFileByForm($file) {\n    var formdata = new FormData()\n    formdata.append('image', $file)\n    var res = await axios({\n      url: 'https://segmentfault.com/img/upload/image',\n      method: 'post',\n      data: formdata,\n      headers: { 'Content-Type': 'multipart/form-data' },\n    })\n    var url = 'https://image-static.segmentfault.com/' + res.data[2]\n    return url\n  }\n}\n\n\n/***/ }),\n\n/***/ 836:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WeiboAdapter)\n/* harmony export */ });\nvar cacheWeiboUser = null\n// var Readability = require(\"../reader/Readability\");\n\n// fetch(\"https://card.weibo.com/article/v3/aj/editor/draft/save?uid=1820387812&id=402\", { \"credentials\": \"include\", \"headers\": { \"accept\": \"application/json, text/plain, */*\", \"accept-language\": \"zh-CN,zh;q=0.9\", \"content-type\": \"application/x-www-form-urlencoded\" }, \"referrer\": \"https://card.weibo.com/article/v3/editor\", \"referrerPolicy\": \"no-referrer-when-downgrade\", \"body\": \"id=402&title=aaaaaaaaaaa&updated=2019-10-10%2016%3A06%3A43&subtitle=&type=&status=0&publish_at=&error_msg=&error_code=0&collection=%5B%5D&free_content=&content=%3Cp%20align%3D%22justify%22%3Eaaaaaaaaaaaaa%3C%2Fp%3E&cover=https%3A%2F%2Fwx3.sinaimg.cn%2Flarge%2F6c80e9e4ly1g7t62jq7uzj202s01kdfz.jpg&summary=aaa&writer=&extra=null&is_word=0&article_recommend=%5B%5D&follow_to_read=1&isreward=1&pay_setting=%7B%22ispay%22%3A0%2C%22isvclub%22%3A0%7D&source=0&action=1&save=1\", \"method\": \"POST\", \"mode\": \"cors\" });\n\nclass WeiboAdapter {\n  constructor() {\n    this.name = 'weibo'\n  }\n\n  async getMetaData() {\n    var html = await $.get('https://card.weibo.com/article/v3/editor')\n    var configIndx = html.indexOf('$CONFIG')\n    var lastIndex = html.indexOf('</script>', configIndx)\n    var configStr = html.substring(configIndx - 12, lastIndex)\n\n    if (configStr.indexOf('CONFIG') > -1) {\n      var res = new Function(configStr + ' return $CONFIG;')()\n      cacheWeiboUser = res\n      return {\n        uid: res.uid,\n        title: res.nick,\n        avatar: res.avatar_large,\n        supportTypes: ['html'],\n        displayName: '微博',\n        type: 'weibo',\n        home: 'https://card.weibo.com/article/v3/editor',\n        icon: 'https://weibo.com/favicon.ico',\n      }\n    } else {\n      throw new Error('not found')\n    }\n  }\n\n  async addPost(post) {\n    var res = await $.post(\n      'https://card.weibo.com/article/v3/aj/editor/draft/create?uid=' +\n        cacheWeiboUser.uid\n    )\n    if (res.code != 100000) {\n      throw new Error(res.msg)\n      return\n    }\n\n    console.log(res)\n    var post_id = res.data.id\n\n    var res = await $.ajax({\n      url:\n        'https://card.weibo.com/article/v3/aj/editor/draft/save?uid=' +\n        cacheWeiboUser.uid +\n        '&id=' +\n        post_id,\n      type: 'POST',\n      dataType: 'JSON',\n      headers: {\n        accept: 'application/json',\n      },\n      data: {\n        id: post_id,\n        title: post.post_title,\n        subtitle: '',\n        type: '',\n        status: '0',\n        publish_at: '',\n        error_msg: '',\n        error_code: '0',\n        collection: '[]',\n        free_content: '',\n        content: post.post_content,\n        cover: '',\n        summary: '',\n        writer: '',\n        extra: 'null',\n        is_word: '0',\n        article_recommend: '[]',\n        follow_to_read: '1',\n        isreward: '1',\n        pay_setting: '{\"ispay\":0,\"isvclub\":0}',\n        source: '0',\n        action: '1',\n        content_type: '0',\n        save: '1',\n      },\n      // data: {\n      //   id: post_id,\n      //   title: post.post_title,\n      //   status: 0,\n      //   error_code: 0,\n      //   content: post.post_content,\n      //   cover: \"\",\n      //   // summary: 'aaaab',\n      //   writer: \"\",\n      //   is_word: 0,\n      //   article_recommend: [],\n      //   follow_to_read: 1,\n      //   isreward: 1,\n      //   pay_setting: JSON.stringify({ ispay: 0, isvclub: 0 }),\n      //   source: 0,\n      //   action: 1,\n      //   save: 1\n      // }\n    })\n    console.log(res)\n    return {\n      status: 'success',\n      post_id: post_id,\n    }\n  }\n\n  async preEditPost(post) {\n    var div = $('<div>')\n    $('body').append(div)\n    try {\n      div.html(post.content)\n      var doc = div\n      tools.processDocCode(div)\n      tools.makeImgVisible(div)\n\n      var tempDoc = $('<div>').append(doc.clone())\n      post.content =\n        tempDoc.children('div').length == 1\n          ? tempDoc.children('div').html()\n          : tempDoc.html()\n\n      console.log('after.predEdit', post.content)\n    } catch (e) {\n      console.log('preEdit.error', e)\n    }\n\n    var rexp = new RegExp('>[\\ts ]*<', 'g')\n    var result = post.content.replace(rexp, '><')\n\n    post.content = result\n  }\n\n\n  async editPost(post_id, post) {\n    var res = await $.ajax({\n      url:\n        'https://card.weibo.com/article/v3/aj/editor/draft/save?uid=' +\n        cacheWeiboUser.uid +\n        '&id=' +\n        post_id,\n      type: 'POST',\n      dataType: 'JSON',\n      headers: {\n        accept: 'application/json',\n      },\n      data: {\n        id: post_id,\n        title: post.post_title,\n        subtitle: '',\n        type: '',\n        status: '0',\n        publish_at: '',\n        error_msg: '',\n        error_code: '0',\n        collection: '[]',\n        free_content: '',\n        content: post.post_content,\n        cover: post.post_thumbnail_raw ? post.post_thumbnail_raw.url : '',\n        summary: '',\n        writer: '',\n        extra: 'null',\n        is_word: '0',\n        article_recommend: '[]',\n        follow_to_read: '1',\n        isreward: '1',\n        pay_setting: '{\"ispay\":0,\"isvclub\":0}',\n        source: '0',\n        action: '1',\n        content_type: '0',\n        save: '1',\n      },\n      // data: {\n      //   id: post_id,\n      //   title: post.post_title,\n      //   status: 0,\n      //   error_code: 0,\n      //   content: post.post_content,\n      //   cover: post.post_thumbnail_raw ? post.post_thumbnail_raw.url : \"\",\n      //   // summary: 'aaaab',\n      //   writer: \"\",\n      //   is_word: 0,\n      //   article_recommend: [],\n      //   follow_to_read: 1,\n      //   isreward: 1,\n      //   pay_setting: JSON.stringify({ ispay: 0, isvclub: 0 }),\n      //   source: 0,\n      //   action: 1,\n      //   save: 1\n      // }\n    })\n\n    if(res.code == '111006') {\n      throw new Error(res.msg)\n    }\n    console.log(res)\n    return {\n      status: 'success',\n      post_id: post_id,\n      draftLink: 'https://card.weibo.com/article/v3/editor#/draft/' + post_id,\n    }\n  }\n\n  untiImageDone(src) {\n    return new Promise((resolve, reject) => {\n      ;(async function loop() {\n        var res = await $.ajax({\n          url:\n            'https://card.weibo.com/article/v3/aj/editor/plugins/asyncimginfo?uid=' +\n            cacheWeiboUser.uid,\n          type: 'POST',\n          headers: {\n            accept: '*/*',\n            'x-requested-with': 'fetch',\n          },\n          data: {\n            'urls[0]': src,\n          },\n        })\n\n        var done = res.data[0].task_status_code == 1\n        if (done) {\n          resolve(res.data[0])\n        } else {\n          setTimeout(loop, 1000)\n        }\n      })()\n    })\n  }\n\n  async uploadFileByUrl(file) {\n    var src = file.src\n    var res = await $.ajax({\n      url:\n        'https://card.weibo.com/article/v3/aj/editor/plugins/asyncuploadimg?uid=' +\n        cacheWeiboUser.uid,\n      type: 'POST',\n      headers: {\n        accept: '*/*',\n        'x-requested-with': 'fetch',\n      },\n      data: {\n        'urls[0]': src,\n      },\n    })\n\n    // https://card.weibo.com/article/v3/aj/editor/plugins/asyncuploadimg?uid=1820387812\n    var imgDetail = await this.untiImageDone(src)\n    return [\n      {\n        id: imgDetail.pid,\n        object_key: imgDetail.pid,\n        url: imgDetail.url,\n      },\n    ]\n  }\n\n  async uploadFile(file) {\n    var blob = new Blob([file.bits])\n    console.log('uploadFile', file, blob)\n    var uploadurl1 = `https://picupload.weibo.com/interface/pic_upload.php?app=miniblog&s=json&p=1&data=1&url=&markpos=1&logo=0&nick=&file_source=4`\n    var uploadurl2 = 'https://picupload.weibo.com/interface/pic_upload.php?app=miniblog&s=json&p=1&data=1&url=&markpos=1&logo=0&nick='\n    var fileResp = await $.ajax({\n      url:\n      uploadurl1,\n      type: 'POST',\n      processData: false,\n      data: new Blob([file.bits]),\n    })\n    console.log(file, fileResp)\n    return [\n      {\n        id: fileResp.data.pics.pic_1.pid,\n        object_key: fileResp.data.pics.pic_1.pid,\n        url:\n          'https://wx3.sinaimg.cn/large/' +\n          fileResp.data.pics.pic_1.pid +\n          '.jpg',\n      },\n    ]\n  }\n\n\n  addPromotion(post) {\n    var sharcode = `<blockquote>本文使用 <a href=\"https://zhuanlan.zhihu.com/p/358098152\" class=\"internal\">文章同步助手</a> 同步</blockquote>`\n    post.content = post.content.trim() + `${sharcode}`\n  }\n}\n\n\n/***/ }),\n\n/***/ 522:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ YiDianAdapter)\n/* harmony export */ });\nclass YiDianAdapter {\n  constructor() {\n    this.skipReadImage = true\n  }\n\n  async getMetaData() {\n    var res = await $.ajax({ url: 'https://mp.yidianzixun.com' })\n    var innerDoc = $(res)\n    var doc = $('<div>').append(innerDoc.clone())\n    var code = doc.find('#__val_').text()\n    console.log('YiDian', code)\n    // code = code.substring(code.indexOf(\"window.mpuser\"));\n    // eval(code);\n    var mpuser = new Function(code + '; return window.mpuser;')()\n    var commonData = Object.assign({}, mpuser)\n    console.log(commonData)\n    if (!commonData.id) {\n      throw new Error('未登录')\n    }\n    var metadata = {\n      uid: commonData.id,\n      title: commonData.media_name,\n      commonData: commonData,\n      avatar: commonData.media_pic,\n      type: 'yidian',\n      supportTypes: ['html'],\n      home: 'https://mp.yidianzixun.com',\n      icon: 'https://www.yidianzixun.com/favicon.ico',\n    }\n    return metadata\n  }\n\n  async addPost(post) {\n    return {\n      status: 'success',\n      post_id: 0,\n    }\n  }\n\n  async editPost(post_id, post) {\n    var res = await $.ajax({\n      url: 'https://mp.yidianzixun.com/model/Article',\n      type: 'POST',\n      dataType: 'JSON',\n      data: {\n        title: post.post_title,\n        cate: '',\n        cateB: '',\n        coverType: 'default',\n        covers: [],\n        content: post.post_content,\n        hasSubTitle: 0,\n        subTitle: '',\n        original: 0,\n        reward: 0,\n        videos: [],\n        audios: [],\n        votes: {\n          vote_id: '',\n          vote_options: [],\n          vote_end_time: '',\n          vote_title: '',\n          vote_type: 1,\n          isAdded: false,\n        },\n        images: [],\n        goods: [],\n        is_mobile: 0,\n        status: 0,\n        import_url: '',\n        import_hash: '',\n        image_urls: {},\n        minTimingHour: 3,\n        maxTimingDay: 7,\n        tags: [],\n        isPubed: false,\n        lastSaveTime: '',\n        dirty: false,\n        editorType: 'articleEditor',\n        activity_id: 0,\n        join_activity: 0,\n        notSaveToStore: true,\n      },\n    })\n\n    if (!res.id) {\n      throw new Error('同步错误' + JSON.stringify(res))\n    }\n    return {\n      status: 'success',\n      post_id: res.id,\n      draftLink: 'https://mp.yidianzixun.com/#/Writing/' + res.id,\n    }\n  }\n\n  async uploadFile(file) {\n    var src = file.src\n    var res = await $.get(\n      'https://mp.yidianzixun.com/api/getImageFromUrl?src=' +\n        encodeURIComponent(src)\n    )\n    // throw new Error('fuck');\n    if (res.status != 'success') {\n      throw new Error('图片上传失败 ' + src)\n    }\n    // http only\n    console.log('uploadFile', res)\n    return [\n      {\n        id: '',\n        object_key: '',\n        url: res.inner_addr,\n      },\n    ]\n  }\n\n  async preEditPost(post) {\n    // var div = $(\"<div>\");\n    // $(\"body\").append(div);\n    // div.html(post.content);\n    // // var org = $(post.content);\n    // // var doc = $('<div>').append(org.clone());\n    // var doc = div;\n    // var pres = doc.find(\"a\");\n    // for (let mindex = 0; mindex < pres.length; mindex++) {\n    //   const pre = pres.eq(mindex);\n    //   try {\n    //     pre.after(pre.html()).remove();\n    //   } catch (e) {}\n    // }\n    // var pres = doc.find(\"iframe\");\n    // for (let mindex = 0; mindex < pres.length; mindex++) {\n    //   const pre = pres.eq(mindex);\n    //   try {\n    //     pre.remove();\n    //   } catch (e) {}\n    // }\n    // post.content = $(\"<div>\")\n    //   .append(doc.clone())\n    //   .html();\n    // console.log(\"post\", post);\n  }\n\n  //   editImg(img, source) {\n  //     img.attr(\"web_uri\", source.images[0].origin_web_uri);\n  //   }\n  //   <img class=\"\" src=\"http://p2.pstatp.com/large/pgc-image/bc0a9fc8e595453083d85deb947c3d6e\" data-ic=\"false\" data-ic-uri=\"\" data-height=\"1333\" data-width=\"1000\" image_type=\"1\" web_uri=\"pgc-image/bc0a9fc8e595453083d85deb947c3d6e\" img_width=\"1000\" img_height=\"1333\"></img>\n}\n\n\n/***/ }),\n\n/***/ 386:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BaiJiaHaoAdapter)\n/* harmony export */ });\n\nclass BaiJiaHaoAdapter {\n  constructor() {\n    this.version = '0.0.1'\n    modifyRequestHeaders('baijiahao.baidu.com/', {\n    \tOrigin: 'https://baijiahao.baidu.com',\n      Referer: 'https://baijiahao.baidu.com/'\n    }, [\n    \t'*://baijiahao.baidu.com/*',\n    ])\n  }\n\n  async getMetaData() {\n    const { data } = await axios.get('https://baijiahao.baidu.com/builder/app/appinfo?_=' + Date.now())\n    console.log(data)\n    if(data.errmsg != 'success') {\n    \tthrow new Error('not found')\n    }\n    const accountInfo = data.data.user\n    return {\n      uid: accountInfo.userid,\n      title: accountInfo.name,\n      avatar: accountInfo.avatar,\n      type: 'baijiahao',\n      displayName: '百家号',\n      supportTypes: ['html'],\n      home: 'https://baijiahao.baidu.com/',\n      icon: 'https://www.baidu.com/favicon.ico?t=********',\n    }\n  }\n\n  async addPost(post, _instance) {\n    return {\n      status: 'success',\n      post_id: 0,\n    }\n  }\n\n  async editPost(post_id, post) {\n    // todo\n    const pageHtml = await $.get('https://baijiahao.baidu.com/builder/rc/edit')\n    const markStr = 'window.__BJH__INIT__AUTH__=\"'\n    const authIndex = pageHtml.indexOf(markStr)\n    if(authIndex == -1) {\n    \tthrow new Error('登录失效')\n    }\n\n    const authToken = pageHtml.substring(authIndex + markStr.length, pageHtml.indexOf('\",window.user_id', authIndex))\n\n\n\n\n    const postStruct = {\n      title: post.post_title,\n      content: post.post_content,\n      feed_cat: 1,\n      len: post.post_content.length,\n      activity_list: [\n        {\n        \tid: 408,\n          is_checked: 0\n        }\n      ],\n      source_reprinted_allow: 0,\n      original_status: 0,\n      original_handler_status: 1,\n      isBeautify: false,\n      subtitle: '',\n      bjhtopic_id: '',\n      bjhtopic_info: '',\n      type: 'news',\n    }\n    const res = await $.ajax({\n    \turl: 'https://baijiahao.baidu.com/pcui/article/save?callback=_SAVE_DRAFT_',\n      type: 'POST',\n      dataType: 'JSON',\n      headers: {\n        token: authToken\n      },\n      data: postStruct,\n    })\n    if(res.errmsg != 'success') {\n    \tthrow new Error(res.errmsg)\n    }\n    post_id = res.ret.article_id\n    return {\n      status: 'success',\n      post_id: post_id,\n      draftLink: 'https://baijiahao.baidu.com/builder/rc/edit?type=news&article_id=' + post_id,\n    }\n  }\n\n  async uploadFile(file) {\n    var uploadUrl = 'https://baijiahao.baidu.com/builderinner/api/content/file/upload'\n    var file = new File([file.bits], 'temp', {\n      type: file.type\n    });\n    var formdata = new FormData()\n    formdata.append('media', file)\n    formdata.append('type', 'image')\n    formdata.append('app_id', '1589639493090963')\n    formdata.append('is_waterlog', '1')\n    formdata.append('save_material', '1')\n    formdata.append('no_compress', '0')\n    formdata.append('is_events', '')\n    formdata.append('article_type', 'news')\n\n    var res = await axios({\n      url: uploadUrl,\n      method: 'post',\n      data: formdata,\n      headers: { 'Content-Type': 'multipart/form-data' },\n    })\n\n    return [\n      {\n        url: res.data.ret.https_url,\n      },\n    ]\n  }\n\n  async preEditPost(post) {\n    var div = $('<div>')\n    $('body').append(div)\n\n    try {\n      div.html(post.content)\n      var doc = div\n      // var pres = doc.find(\"pre\");\n      tools.processDocCode(div)\n      tools.makeImgVisible(div)\n\n      var tempDoc = $('<div>').append(doc.clone())\n      post.content =\n        tempDoc.children('div').length == 1\n          ? tempDoc.children('div').html()\n          : tempDoc.html()\n\n      console.log('after.predEdit', post.content)\n    } catch (e) {\n      console.log('preEdit.error', e)\n    }\n  }\n}\n\n\n/***/ }),\n\n/***/ 536:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BilibiliAdapter)\n/* harmony export */ });\nclass BilibiliAdapter {\n  constructor(config) {\n    // this.skipReadImage = true\n    this.config = config\n    this.name = 'bilibili'\n  }\n\n  async getMetaData() {\n    var res = await $.ajax({\n      url: 'https://api.bilibili.com/x/web-interface/nav?build=0&mobi_app=web',\n    })\n    if(!res.data.isLogin) {\n      throw new Error('not login')\n    }\n    // console.log(res);\n    return {\n      uid: res.data.mid,\n      title: res.data.uname,\n      avatar: res.data.face,\n      supportTypes: ['html'],\n      type: 'bilibili',\n      displayName: '哔哩哔哩',\n      home: 'https://member.bilibili.com/platform/upload/text',\n      icon: 'https://www.bilibili.com/favicon.ico',\n    }\n  }\n\n  async addPost(post) {\n    return {\n      status: 'success',\n      post_id: 0,\n    }\n  }\n\n  async editPost(post_id, post) {\n    // var pgc_feed_covers = []\n    // if (post.post_thumbnail_raw && post.post_thumbnail_raw.images) {\n    //   pgc_feed_covers.push({\n    //     id: 0,\n    //     url: post.post_thumbnail_raw.url,\n    //     uri: post.post_thumbnail_raw.images[0].origin_web_uri,\n    //     origin_uri: post.post_thumbnail_raw.images[0].origin_web_uri,\n    //     ic_uri: '',\n    //     thumb_width: post.post_thumbnail_raw.images[0].width,\n    //     thumb_height: post.post_thumbnail_raw.images[0].height,\n    //   })\n    // }\n\n    var csrf = this.config.state.csrf;\n    var res = await $.ajax({\n      url: 'https://api.bilibili.com/x/article/creative/draft/addupdate',\n      type: 'POST',\n      dataType: 'JSON',\n      data: {\n        tid: 4,\n        title: post.post_title,\n        save: 0,\n        pgc_id: 0,\n        content: post.post_content,\n        csrf: csrf,\n        // pgc_feed_covers: JSON.stringify(pgc_feed_covers),\n      },\n    })\n\n    if (!res.data) {\n      throw new Error(res.message)\n    }\n\n    return {\n      status: 'success',\n      post_id: res.data.aid,\n      draftLink:\n        'https://member.bilibili.com/platform/upload/text/edit?aid=' +\n        res.data.aid,\n    }\n  }\n\n  async uploadFile(file) {\n    var src = file.src\n    var csrf = this.config.state.csrf\n\n    var uploadUrl ='https://api.bilibili.com/x/article/creative/article/upcover'\n    var file = new File([file.bits], 'temp', {\n      type: file.type,\n    })\n    var formdata = new FormData()\n    formdata.append('binary', file)\n    formdata.append('csrf', csrf)\n    var res = await axios({\n      url: uploadUrl,\n      method: 'post',\n      data: formdata,\n      headers: { 'Content-Type': 'multipart/form-data' },\n    })\n\n    if (res.data.code != 0) {\n      throw new Error('图片上传失败 ' + src)\n    }\n    // http only\n    console.log('uploadFile', res)\n    var id = Math.floor(Math.random() * 100000)\n    return [\n      {\n        id: id,\n        object_key: id,\n        url: res.data.data.url,\n        size: res.data.data.size,\n        // images: [res.data],\n      },\n    ]\n  }\n\n  async preEditPost(post) {\n    var div = $('<div>')\n    $('body').append(div)\n\n    div.html(post.content)\n    // var org = $(post.content);\n    // var doc = $('<div>').append(org.clone());\n    var doc = div\n    var pres = doc.find('a')\n    for (let mindex = 0; mindex < pres.length; mindex++) {\n      const pre = pres.eq(mindex)\n      try {\n        pre.after(pre.html()).remove()\n      } catch (e) {}\n    }\n\n    tools.processDocCode(div)\n    tools.makeImgVisible(div)\n\n    var pres = doc.find('iframe')\n    for (let mindex = 0; mindex < pres.length; mindex++) {\n      const pre = pres.eq(mindex)\n      try {\n        pre.remove()\n      } catch (e) {}\n    }\n\n    try {\n      const images = doc.find('img')\n      for (let index = 0; index < images.length; index++) {\n        const image = images.eq(index)\n        const imgSrc = image.attr('src')\n        if (imgSrc && imgSrc.indexOf('.svg') > -1) {\n          console.log('remove svg Image')\n          image.remove()\n        }\n      }\n      const qqm = doc.find('qqmusic')\n      qqm.next().remove()\n      qqm.remove()\n    } catch (e) {}\n\n    post.content = $('<div>')\n      .append(doc.clone())\n      .html()\n    console.log('post', post)\n  }\n\n  editImg(img, source) {\n    img.attr('size', source.size)\n  }\n\n  addPromotion(post) {\n    var sharcode = `<blockquote><p>本文使用 <a href=\"https://www.bilibili.com/read/cv10352009\" class=\"internal\">文章同步助手</a> 同步</p></blockquote>`\n    post.content = post.content.trim() + `${sharcode}`\n  }\n  //   <img class=\"\" src=\"http://p2.pstatp.com/large/pgc-image/bc0a9fc8e595453083d85deb947c3d6e\" data-ic=\"false\" data-ic-uri=\"\" data-height=\"1333\" data-width=\"1000\" image_type=\"1\" web_uri=\"pgc-image/bc0a9fc8e595453083d85deb947c3d6e\" img_width=\"1000\" img_height=\"1333\"></img>\n}\n\n\n/***/ }),\n\n/***/ 637:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DaYuAdapter)\n/* harmony export */ });\nvar _cacheMeta = null\n\nclass DaYuAdapter {\n  constructor() {\n    // this.skipReadImage = true\n    this.version = '0.0.1'\n    this.name = 'dayu'\n    this.images = []\n    modifyRequestHeaders(\n      'mp.dayu.com/',\n      {\n        Origin: 'https://mp.dayu.com',\n        Referer: 'https://mp.dayu.com/',\n      },\n      ['*://mp.dayu.com/dashboard/*']\n    )\n    // https://mp.dayu.com\n  }\n\n  async getMetaData() {\n    const res = await axios.get('https://mp.dayu.com/dashboard/index')\n    var pageHtml = res.data\n    var markStr = 'var globalConfig = '\n    var authIndex = pageHtml.indexOf(markStr)\n    if (authIndex == -1) {\n      throw new Error('登录失效 authIndex=' + authIndex)\n    }\n\n    var authTokenStr = pageHtml.substring(\n      authIndex + markStr.length,\n      pageHtml.indexOf(`var G = {`, authIndex)\n    )\n\n    var pageConfig = new Function(\n      'var config = ' + authTokenStr + '; return config;'\n    )()\n\n    _cacheMeta = {\n      utoken: pageConfig.utoken,\n      uploadSign: pageConfig.nsImageUploadSign,\n      uid: pageConfig.wmid,\n      title: pageConfig.weMediaName,\n      avatar:\n        pageConfig.wmAvator.indexOf('http') > -1\n          ? pageConfig.wmAvator\n          : pageConfig.wmAvator.replace('//', 'https://'),\n      supportTypes: ['html'],\n      type: 'dayu',\n      displayName: '大鱼号',\n      home: 'https://mp.dayu.com/dashboard/account/profile',\n      icon: 'https://image.uc.cn/s/uae/g/1v/images/index/favicon.ico',\n    }\n\n    return _cacheMeta\n  }\n\n  async addPost(post) {\n    return {\n      status: 'success',\n      post_id: 0,\n    }\n  }\n\n  async editPost(post_id, post) {\n    _cacheMeta = _cacheMeta ? _cacheMeta : await this.getMetaData()\n    console.log('editPost', post)\n    var res = await $.ajax({\n      url: 'https://mp.dayu.com/dashboard/save-draft',\n      type: 'POST',\n      headers: {\n        utoken: _cacheMeta.utoken,\n      },\n      data: {\n        title: post.post_title,\n        content: post.post_content,\n        author: _cacheMeta.title,\n        coverImg: this.images[0].org_url,\n        article_type: 1,\n        utoken: _cacheMeta.utoken,\n        cover_from: 'auto',\n      },\n    })\n\n    if (res.error) {\n      throw new Error(res.error)\n    }\n\n    post_id = res.data._id\n    console.log(res)\n    return {\n      status: 'success',\n      post_id: post_id,\n      draftLink:\n        'https://mp.dayu.com/dashboard/article/write?draft_id=' + post_id,\n    }\n  }\n\n  async uploadFile(file) {\n    _cacheMeta = _cacheMeta ? _cacheMeta : await this.getMetaData()\n    var uploadUrl =\n      'https://ns.dayu.com/article/imageUpload?appid=website&fromMaterial=0&wmid=' +\n      _cacheMeta.uid +\n      '&wmname=' +\n      encodeURIComponent(_cacheMeta.title) +\n      '&sign=' +\n      _cacheMeta.uploadSign\n    var file = new File([file.bits], 'temp', {\n      type: file.type,\n    })\n    var formdata = new FormData()\n    formdata.append('upfile', file, new Date().getTime() + '.jpg')\n    formdata.append('type', file.type)\n    formdata.append('id', 'WU_FILE_1')\n    formdata.append('fileid', `uploadm-` + Math.floor(Math.random() * 1000000))\n    formdata.append('name', new Date().getTime() + '.jpg')\n    formdata.append('lastModifiedDate', new Date().toString())\n    formdata.append('size', file.size)\n    var res = await axios({\n      url: uploadUrl,\n      method: 'post',\n      data: formdata,\n      headers: { 'Content-Type': 'multipart/form-data' },\n    })\n\n    var image = {\n      org_url: res.data.data.imgInfo.org_url,\n      url: res.data.data.imgInfo.url,\n    }\n\n    this.images.push(image)\n\n    return [image]\n  }\n\n  async preEditPost(post) {\n    var div = $('<div>')\n    $('body').append(div)\n\n    // post.content = post.content.replace(/\\>\\s+\\</g,'');\n    div.html(post.content)\n\n    // var org = $(post.content);\n    // var doc = $('<div>').append(org.clone());\n    var doc = div\n    var pres = doc.find('pre')\n    console.log('find code blocks', pres.length, post)\n    for (let mindex = 0; mindex < pres.length; mindex++) {\n      const pre = pres.eq(mindex)\n      try {\n        var newHtml = CodeBlockToPlainText(pre, 0)\n        if (newHtml) {\n          console.log(newHtml)\n          pre.html(newHtml)\n        }\n      } catch (e) {}\n    }\n\n    var processEmptyLine = function(idx, el) {\n      var $obj = $(this)\n      var originalText = $obj.text()\n      var img = $obj.find('img')\n      var brs = $obj.find('br')\n      if (originalText == '') {\n        ;(function() {\n          if (img.length) return\n          if (!brs.length) return\n          $obj.remove()\n        })()\n      }\n\n      // try to replace as h2;\n      var strongTag = $obj.find('strong').eq(0)\n      var childStrongText = strongTag.text()\n      if (originalText == childStrongText) {\n        var strongSize = null\n        var tagStart = strongTag\n        var align = null\n        for (let index = 0; index < 4; index++) {\n          var fontSize = tagStart.css('font-size')\n          var textAlign = tagStart.css('text-align')\n          if (fontSize) {\n            strongSize = fontSize\n          }\n          if (textAlign) {\n            align = textAlign\n          }\n          if (align && strongSize) break\n          if (tagStart == $obj) {\n            console.log('near top')\n            break\n          }\n          tagStart = tagStart.parent()\n        }\n        if (strongSize) {\n          var theFontSize = parseInt(strongSize)\n          if (theFontSize > 17 && align == 'center') {\n            var newTag = $('<h2></h2>').append($obj.html())\n            $obj.after(newTag).remove()\n          }\n        }\n      }\n    }\n\n    // remove empty break line\n    doc.find('p').each(processEmptyLine)\n    doc.find('section').each(processEmptyLine)\n\n    // for break line\n    doc.find('section').replaceWith(function() {\n      return $('<p />').append($(this).contents())\n    })\n\n    var processBr = function(idx, el) {\n      var $obj = $(this)\n      if (!$obj.next().length) {\n        $obj.remove()\n      }\n    }\n    doc.find('br').each(processBr)\n    // table {\n    //     margin-bottom: 10px;\n    //     border-collapse: collapse;\n    //     display: table;\n    //     width: 100%!important;\n    // }\n    // td, th {\n    //     word-wrap: break-word;\n    //     word-break: break-all;\n    //     padding: 5px 10px;\n    //     border: 1px solid #DDD;\n    // }\n\n    // console.log('found table', doc.find('table'))\n    var tempDoc = $('<div>').append(doc.clone())\n    post.content =\n      tempDoc.children('div').length == 1\n        ? tempDoc.children('div').html()\n        : tempDoc.html()\n    // div.remove();\n  }\n}\n\n\n/***/ }),\n\n/***/ 247:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DoubanAdapter)\n/* harmony export */ });\nvar metaCache = null\n\n\nconst ImageRegexp = /^!\\[([^\\]]*)]\\s*\\(([^)\"]+)( \"([^)\"]+)\")?\\)/\nconst imageBlock = (remarkable) => {\n  remarkable.block.ruler.before('paragraph', 'image', (state, startLine, endLine, silent) => {\n    const pos = state.bMarks[startLine] + state.tShift[startLine]\n    const max = state.eMarks[startLine]\n\n    if (pos >= max) {\n      return false\n    }\n    if (!state.src) {\n      return false\n    }\n    if (state.src[pos] !== '!') {\n      return false\n    }\n\n    var match = ImageRegexp.exec(state.src.slice(pos))\n    if (!match) {\n      return false\n    }\n\n    // in silent mode it shouldn't output any tokens or modify pending\n    if (!silent) {\n      state.tokens.push({\n        type: 'image_open',\n        src: match[2],\n        alt: match[1],\n        lines: [ startLine, state.line ],\n        level: state.level\n      })\n\n      state.tokens.push({\n        type: 'image_close',\n        level: state.level\n      })\n    }\n\n    state.line = startLine + 1\n\n    return true\n  })\n}\n\n\n\nfunction getFormData(obj) {\n    var map = {};\n    obj.find('input').each(function() {\n        map[$(this).attr(\"name\")] = $(this).val();\n    });\n    return map\n}\n\n\nclass DoubanAdapter {\n  constructor(config) {\n    this.config = config\n    this.meta = metaCache\n    this.name = 'douban'\n\n    modifyRequestHeaders('www.douban.com/', {\n    \tOrigin: 'https://www.douban.com',\n      Referer: 'https://www.douban.com'\n    }, [\n    \t'*://www.douban.com/*',\n    ])\n  }\n\n  async getMetaData() {\n    var res = await $.ajax({ url: 'https://www.douban.com/note/create' })\n    var innerDoc = $(res)\n    var doc = $('<div>').append(innerDoc.clone())\n    var configScript = innerDoc.filter(function(index, el){ return $(el).text().indexOf('_POST_PARAMS') > -1 });\n    if(configScript.length == 0) {\n        throw new Error('未登录')\n    }\n    var code = configScript.text()\n    var wx = new Function(\n        'Do ={}; Do.add = function() {} '+ code +\n        '; return {_USER_AVATAR: _USER_AVATAR, _USER_NAME: _USER_NAME, _NOTE_ID: _NOTE_ID, _TAGS: _TAGS, _POST_PARAMS: _POST_PARAMS};'\n    )();\n    console.log(code, wx)\n\n    var metadata = {\n      uid: wx._USER_NAME,\n      title: wx._USER_NAME,\n      commonData: wx,\n      avatar: wx._USER_AVATAR,\n      type: 'douban',\n      supportTypes: ['html'],\n      home: 'https://www.douban.com/note/create',\n      icon: 'https://img3.doubanio.com/favicon.ico',\n      form: getFormData(doc.find('#note-editor-form')),\n      _POST_PARAMS: wx._POST_PARAMS\n    }\n    metaCache = metadata\n    this.meta = metaCache\n    console.log('metaCache', metaCache)\n    return metadata\n  }\n\n  async addPost(post) {\n    return {\n      status: 'success',\n      post_id: 0,\n    }\n  }\n\n  async editPost(post_id, post) {\n    var turndownService = new turndown()\n    turndownService.use(tools.turndownExt)\n    var markdown = turndownService.turndown(post.post_content)\n    console\n      .log(markdown)\n\n    // 保证图片换行\n    markdown = markdown.split(\"\\n\").map(_ => {\n      const imageBlocks = _.split('![]');\n      return imageBlocks.length > 1 ? imageBlocks.join('\\n![]') : _\n    }).join(\"\\n\");\n\n    const draftjsState = JSON.stringify(tools.markdownToDraft(markdown, {\n      remarkablePlugins: [imageBlock],\n      blockTypes: {\n        image_open: function(item, generateUniqueKey) {\n          console.log('image_open', 'blockTypes', item)\n          var key = generateUniqueKey()\n          var blockEntities = {}\n          // ?#\n          var sourcePair =  item.src.split(\"?#\")\n          var rawSrc = sourcePair[0]\n          var sourceId = sourcePair[1]\n          if(sourcePair.length) {\n            item.src = rawSrc\n          }\n          var imageTemplate = {\n            id: sourceId,\n            src:  item.src,\n            thumb: item.src,\n            url: item.src,\n          }\n\n          blockEntities[key] = {\n            type: 'IMAGE',\n            mutability: 'IMMUTABLE',\n            data: imageTemplate,\n          }\n          return {\n            type: 'atomic',\n            blockEntities: blockEntities,\n            inlineStyleRanges: [],\n            // \"data\": {\n            //     \"page\": 0\n            // },\n            entityRanges: [\n              {\n                offset: 0,\n                length: 1,\n                key: key,\n              },\n            ],\n            text: ' ',\n          }\n        }\n      },\n      blockEntities: {\n        image: function (item) {\n          var sourcePair =  item.src.split(\"?#\")\n          if(sourcePair.length) {\n            var rawSrc = sourcePair[0]\n            var sourceId = sourcePair[1]\n            item.id = sourceId\n            item.src = rawSrc\n          }\n          console.log('image_open', 'blockEntities', item)\n          return {\n            type: 'IMAGE',\n            mutability: 'IMMUTABLE',\n            data: item\n          }\n        }\n      }\n    }));\n    console.log(draftjsState)\n\n    var state = this.config.state;\n    var requestUrl = 'https://www.douban.com/j/note/autosave';\n    var draftLink = 'https://www.douban.com/note/create';\n    var requestBody = {\n      is_rich: 1,\n      note_id: this.meta.form.note_id,\n      note_title: post.post_title,\n      note_text: draftjsState,\n      introduction: '',\n      note_privacy: 'P',\n      cannot_reply: null,\n      author_tags: null,\n      accept_donation: null,\n      donation_notice: null,\n      is_original: null,\n      ck: this.meta.form.ck\n    }\n\n    // https://music.douban.com/subject/24856133/new_review\n    // music review\n    // https://music.douban.com/j/review/create\n    // is_rich: 1\n    // topic_id:\n    // review[subject_id]: 24856133\n    // review[title]: aaa\n    // review[introduction]:\n    // review[text]: {\"entityMap\":{},\"blocks\":[{\"key\":\"9riq1\",\"text\":\"aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa\",\"type\":\"unstyled\",\"depth\":0,\"inlineStyleRanges\":[],\"entityRanges\":[],\"data\":{\"page\":0}}]}\n    // review[rating]:\n    // review[spoiler]:\n    // review[donate]:\n    // review[original]:\n    // ck: O4jk\n    if(state && state.is_review) {\n      if(state.subject == 'music') {\n        draftLink = state.url;\n        requestUrl = 'https://music.douban.com/j/review/create'\n        requestBody = {\n          is_rich: 1,\n          topic_id: '',\n          review: {\n            subject_id: state.id,\n            title:  post.post_title,\n            introduction: '',\n            text: draftjsState,\n            rating: '',\n            spoiler: '',\n            donate: '',\n            original: ''\n          },\n          ck: this.meta.form.ck\n        }\n      }\n    }\n    console.log('state', requestBody)\n    var res = await $.ajax({\n      url: requestUrl,\n      type: 'POST',\n      dataType: 'JSON',\n      data: requestBody,\n    })\n\n    if(res.url) {\n      draftLink = `https://www.douban.com/note/${requestBody.note_id}/`\n    }\n\n    return {\n      status: 'success',\n      post_id: this.meta.form.note_id,\n      draftLink: draftLink,\n    }\n  }\n\n  editImg(img, source) {\n    img.attr('raw-data', JSON.stringify(source.raw))\n  }\n\n  async preEditPost(post) {\n    var div = $('<div>')\n    $('body').append(div)\n\n    try {\n      div.html(post.content)\n      var doc = div\n      // var pres = doc.find(\"pre\");\n      tools.processDocCode(div)\n      tools.makeImgVisible(div)\n\n      var tempDoc = $('<div>').append(doc.clone())\n      post.content =\n        tempDoc.children('div').length == 1\n          ? tempDoc.children('div').html()\n          : tempDoc.html()\n\n      console.log('after.predEdit', post.content)\n    } catch (e) {\n      console.log('preEdit.error', e)\n    }\n  }\n\n  addPromotion(post) {\n    var sharcode = `<blockquote><p>本文使用 <a href=\"https://zhuanlan.zhihu.com/p/358098152\" class=\"internal\">文章同步助手</a> 同步</p></blockquote>`\n    post.content = post.content.trim() + `${sharcode}`\n  }\n\n  async uploadFile(file) {\n\n    // https://music.douban.com/j/review/upload_image\n    var requestUrl = 'https://www.douban.com/j/note/add_photo';\n    var state = this.config.state;\n    var formdata = new FormData()\n    var blob = new Blob([file.bits], {\n      type: file.type\n    });\n\n    if(state && state.is_review) {\n      if(state.subject == 'music') {\n        requestUrl =  'https://music.douban.com/j/review/upload_image';\n        formdata.append('review_id', '')\n        formdata.append('picfile', blob)\n      }\n    } else {\n      formdata.append('note_id', this.meta.form.note_id)\n      formdata.append('image_file', blob)\n    }\n\n    formdata.append('ck', this.meta.form.ck)\n    formdata.append('upload_auth_token', this.meta._POST_PARAMS.siteCookie.value)\n\n    var res = await axios({\n      url: requestUrl,\n      method: 'post',\n      data: formdata,\n      headers: { 'Content-Type': 'multipart/form-data' },\n    })\n\n    var url = res.data.photo.url\n    if(!res.data.photo) {\n        console.log(res.data);\n        throw new Error('upload failed')\n    }\n    //  return url;\n    return [\n      {\n        id: res.data.photo.id,\n        object_key: res.data.photo.id,\n        url: url + \"?#\" + res.data.photo.id,\n        raw: res.data\n      },\n    ]\n  }\n}\n\n\n/***/ }),\n\n/***/ 754:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FocusAdapter)\n/* harmony export */ });\n\nclass FocusAdapter {\n  constructor() {\n    this.name = 'weibo'\n  }\n\n  async getMetaData() {\n    var res = await $.get('https://mp-fe-pc.focus.cn//user/status?')\n    return {\n        uid: res.data.uid,\n        title: res.data.accountName,\n        avatar: null,\n        supportTypes: ['html'],\n        displayName: '搜狐焦点',\n        type: 'sohufocus',\n        home: 'https://mp.focus.cn/fe/index.html#/info/draft',\n        icon: 'https://mp.focus.cn/favicon.ico',\n      }\n  }\n\n  async addPost(post) {\n    return {\n      status: 'success',\n      post_id: 0,\n    }\n  }\n\n\n  async preEditPost(post) {\n    var rexp = new RegExp('>[\\ts ]*<', 'g')\n    var result = post.content.replace(rexp, '><')\n    post.content = result\n  }\n\n  async editPost(post_id, post) {\n    var res = await axios.post('https://mp-fe-pc.focus.cn/news/info/publishNewsInfo', {\n        \"projectIds\": [],\n        \"newsBasic\": {\n          \"id\": \"\",\n          \"cityId\": 0,\n          \"title\": post.post_title,\n          \"category\": 1,\n          \"headImg\": \"\",\n          \"newsAbstract\": \"\",\n          \"isGuide\": 0,\n          \"status\": 4\n        },\n        \"newsContent\": {\n          \"content\": post.post_content\n        },\n        \"videoIds\": []\n    })\n    // console.log(res)\n    var aId = res.data.data.id\n    return {\n      status: 'success',\n      post_id: aId,\n      draftLink: 'https://mp.focus.cn/fe/index.html#/info/subinfo/' + aId,\n    }\n  }\n\n  async uploadFile(file) {\n    var formdata = new FormData()\n    var blob = new Blob([file.bits], {\n        type: file.type\n    });\n\n    formdata.append('image', blob, new Date().getTime() + '.jpg')\n    var res = await axios({\n      url: `https://mp-fe-pc.focus.cn/common/image/upload?type=2`,\n      method: 'post',\n      data: formdata,\n      headers: { 'Content-Type': 'multipart/form-data' },\n    })\n\n    if(res.data.code != 200) {\n      console.log(res.data);\n      throw new Error('upload failed')\n    }\n    var url = `https://t-img.51f.com/sh740wsh${res.data.data}`\n    return [\n      {\n        id: res.data.data,\n        object_key: res.data.data,\n        url: url,\n      },\n    ]\n  }\n\n}\n\n\n/***/ }),\n\n/***/ 355:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImoocAdapter)\n/* harmony export */ });\nclass ImoocAdapter {\n  constructor() {\n    this.name = 'imooc'\n    // cors\n    modifyRequestHeaders(\n      'www.imooc.com/article',\n      {\n        Origin: 'https://www.imooc.com',\n        Referer: 'https://www.imooc.com/',\n      },\n      ['*://www.imooc.com/article/*']\n    )\n  }\n\n  async getMetaData() {\n    var res = await $.get('https://www.imooc.com/u/card')\n    var dataStr = res.replace('jsonpcallback(', '').replace('})', '}')\n    var result = JSON.parse(dataStr)\n    console.log(result)\n    if (result.result != 0) {\n      throw new Error(result.msg)\n    }\n    return {\n      uid: result.data.uid,\n      title: result.data.nickname,\n      avatar: result.data.img,\n      type: 'imooc',\n      displayName: '慕课手记',\n      supportTypes: ['markdown', 'html'],\n      home: 'https://www.imooc.com/article',\n      icon: 'https://www.imooc.com/favicon.ico',\n    }\n  }\n\n  async addPost(post) {\n    return {\n      status: 'success',\n      post_id: 0,\n    }\n  }\n\n  async editPost(post_id, post) {\n    if (!post.markdown) {\n      var turndownService = new turndown()\n      turndownService.use(tools.turndownExt)\n      var markdown = turndownService.turndown(post.post_content)\n      post.markdown = markdown\n      console.log(markdown)\n    }\n\n    var postStruct = {\n      editor: 0,\n      draft_id: 0,\n      title: post.post_title,\n      content: post.markdown,\n    }\n\n    var res = await $.ajax({\n      url: 'https://www.imooc.com/article/savedraft',\n      type: 'POST',\n      dataType: 'JSON',\n      data: postStruct,\n    })\n    console.log(res)\n    post_id = res.data\n    return {\n      status: 'success',\n      post_id: post_id,\n      draftLink: 'https://www.imooc.com/article/draft/id/' + post_id,\n    }\n  }\n\n  async uploadFile(file) {\n    var uploadUrl = 'https://www.imooc.com/article/ajaxuploadimg'\n    var file = new File([file.bits], 'temp', {\n      type: file.type,\n    })\n    var formdata = new FormData()\n    formdata.append('photo', file, new Date().getTime() + '.jpg')\n    formdata.append('type', file.type)\n    formdata.append('id', 'WU_FILE_0')\n    formdata.append('name', new Date().getTime() + '.jpg')\n    formdata.append('lastModifiedDate', new Date().toString())\n    formdata.append('size', file.size)\n    var res = await axios({\n      url: uploadUrl,\n      method: 'post',\n      data: formdata,\n      headers: { 'Content-Type': 'multipart/form-data' },\n    })\n\n    if (res.data.result != 0) {\n      throw new Error(res.data.msg)\n    }\n\n    var url = res.data.data.imgpath\n    return [\n      {\n        url: url,\n      },\n    ]\n  }\n\n  async preEditPost(post) {\n    var div = $('<div>')\n    $('body').append(div)\n\n    try {\n      div.html(post.content)\n      var doc = div\n      // var pres = doc.find(\"pre\");\n      tools.processDocCode(div)\n      tools.makeImgVisible(div)\n\n      var tempDoc = $('<div>').append(doc.clone())\n      post.content =\n        tempDoc.children('div').length == 1\n          ? tempDoc.children('div').html()\n          : tempDoc.html()\n\n      console.log('after.predEdit', post.content)\n    } catch (e) {\n      console.log('preEdit.error', e)\n    }\n  }\n}\n\n\n/***/ }),\n\n/***/ 839:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst template = `<!DOCTYPE html>\n<html>\n<head>\n    <meta name=\"wechat-enable-text-zoom-em\" content=\"true\">\n    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n    <meta name=\"color-scheme\" content=\"light dark\">\n    <meta name=\"viewport\"\n        content=\"width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0,viewport-fit=cover\">\n    <link rel=\"shortcut icon\" type=\"image/x-icon\" href=\"//res.wx.qq.com/a/wx_fed/assets/res/NTI4MWU5.ico\"\n        reportloaderror>\n    <link rel=\"mask-icon\" href=\"//res.wx.qq.com/a/wx_fed/assets/res/MjliNWVm.svg\" color=\"#4C4C4C\" reportloaderror>\n    <link rel=\"apple-touch-icon-precomposed\" href=\"//res.wx.qq.com/a/wx_fed/assets/res/OTE0YTAw.png\" reportloaderror>\n    <meta name=\"apple-mobile-web-app-capable\" content=\"yes\">\n    <meta name=\"apple-mobile-web-app-status-bar-style\" content=\"black\">\n    <meta name=\"format-detection\" content=\"telephone=no\">\n    <meta property=\"og:title\" content=\"TITLE\" />\n    <meta property=\"og:type\" content=\"article\" />\n    <title>TITLE</title>\n    <link rel=\"stylesheet\"\n        href=\"https://res.wx.qq.com/mmbizwap/zh_CN/htmledition/js/../mmbizappmsg/assets/appmsg.c9d06de2.css\"\n        reportloaderror>\n    <link rel=\"stylesheet\"\n        href=\"https://res.wx.qq.com/mmbizwap/zh_CN/htmledition/js/../mmbizappmsg/assets/sprite.dcee1002.css\"\n        reportloaderror>\n    <link rel=\"stylesheet\"\n        href=\"https://res.wx.qq.com/mmbizwap/zh_CN/htmledition/js/../mmbizappmsg/assets/report.5e0fdfbf.css\"\n        reportloaderror>\n    <link rel=\"stylesheet\"\n        href=\"https://res.wx.qq.com/mmbizwap/zh_CN/htmledition/js/../mmbizappmsg/assets/wxwork_hidden.96d6e8be.css\"\n        reportloaderror>\n    <link rel=\"stylesheet\"\n        href=\"https://res.wx.qq.com/mmbizwap/zh_CN/htmledition/js/../mmbizappmsg/assets/controller.5417a6ee.css\"\n        reportloaderror>\n</head>\n<body id=\"activity-detail\" class=\"zh_CN wx_wap_page\n\tmm_appmsg\n\tcomment_feature\n\tdiscuss_tab appmsg_skin_default appmsg_style_default\">\n    <div id=\"js_article\" class=\"rich_media\">\n        <div id=\"js_top_ad_area\" class=\"top_banner\">\n        </div>\n        <div class=\"rich_media_inner\">\n            <div id=\"page-content\" class=\"rich_media_area_primary\">\n                <div class=\"rich_media_area_primary_inner\">\n                    <div id=\"img-content\" class=\"rich_media_wrp\">\n                        <h1 class=\"rich_media_title \" id=\"activity-name\">\n                           TITLE\n                        </h1>\n                        <div id=\"meta_content\" class=\"rich_media_meta_list\">\n                            <span class=\"rich_media_meta rich_media_meta_nickname\" id=\"profileBt\">\n                                <a href=\"javascript:void(0);\" class=\"wx_tap_link js_wx_tap_highlight weui-wa-hotarea\"\n                                    id=\"js_name\">\n                                    AUTHOR\n                                </a>\n                            </span>\n                            <em id=\"publish_time\" class=\"rich_media_meta rich_media_meta_text\">\n                                PUBLISH_TIME\n                            </em>\n                        </div>\n                        <div class=\"rich_media_content\" id=\"js_content\">\n                            CONTENT\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n    // saved from:ORIGINAL_LINK\n</body>\n</html>`\n\nclass IPFSAdapter {\n  constructor() {\n    this.version = '0.0.1'\n    this.name = 'ipfs'\n    this.endpoint = 'https://ipfs.infura.io:5001'\n    this.gatewayList = [\n      'infura-ipfs.io',\n    ]\n  }\n\n  async getMetaData() {\n    return {\n      uid: 'ipfs',\n      title: '备份到IPFS',\n      avatar: 'https://ipfs.io/favicon.ico',\n      supportTypes: ['html'],\n      type: 'ipfs',\n      displayName: '备份到IPFS',\n      home: 'https://ipfs.io/',\n      icon: 'https://ipfs.io/favicon.ico',\n    }\n  }\n\n  async addPost(post) {\n    return {\n      status: 'success',\n      post_id: 0,\n    }\n  }\n\n  async editPost(post_id, post) {\n    const pubTime = moment.unix(post.publish_time).format('YYYY-MM-DD HH:mm')\n    const content = template\n      .replace('TITLE', post.post_title)\n      .replace('TITLE', post.post_title)\n      .replace('AUTHOR', post.nickname)\n      .replace('PUBLISH_TIME', pubTime)\n      .replace('ORIGINAL_LINK', post.link)\n      .replace('CONTENT', post.post_content)\n    console.log(content)\n    const res = await this.uploadFile({\n      bits: content,\n      type: 'text/html',\n    })\n    return {\n      status: 'success',\n      post_id: res[0].id,\n      draftLink: res[0].url,\n    }\n  }\n\n  async uploadFile(file) {\n    const { src, post_id } = file\n    var uploadUrl = `${this.endpoint}/api/v0/add?stream-channels=true&progress=false`\n    var file = new File([file.bits], 'temp.jpg', {\n      type: file.type,\n    })\n    var formdata = new FormData()\n    formdata.append('file', file)\n    var res\n    try {\n      res = await axios({\n        url: uploadUrl,\n        method: 'post',\n        data: formdata,\n        headers: { 'Content-Type': 'multipart/form-data' },\n      })\n    } catch (e) {\n      // wait\n      await wait(5000)\n    }\n\n    await wait(3000)\n    if (!res.data.Hash) {\n      throw new Error('图片上传失败 ' + src)\n    }\n    // http only\n    const endpoint =\n      this.gatewayList.length > 1\n        ? this.gatewayList[Math.round(Math.random() % this.gatewayList.length)]\n        : this.gatewayList[0]\n    const url = `https://${endpoint}/ipfs/${res.data.Hash}`\n    return [\n      {\n        id: res.data.Hash,\n        object_key: res.data.Hash,\n        url: url,\n        images: [res.data],\n      },\n    ]\n  }\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (IPFSAdapter);\n\n\n/***/ }),\n\n/***/ 172:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ JianShuAdapter)\n/* harmony export */ });\nvar NoteVersionCaches = {}\nvar defaultNoteBookId\n\nclass JianShuAdapter {\n  constructor() {\n    this.name = 'jianshu'\n    // chrome.cookies.getAll({ domain: \"zhihu.com\"},  function(cookies){\n    //     console.log(cookies)\n    // })\n  }\n\n  async getMetaData() {\n    var res = await $.ajax({\n      url: 'https://www.jianshu.com/settings/basic.json',\n    })\n    var notebooks = await $.get('https://www.jianshu.com/author/notebooks')\n    // console.log(res);\n    // https://upload.jianshu.io/users/upload_avatars/12192974/d02c5033-7f82-458f-9b3e-f4c4dbaa1221?imageMogr2/auto-orient/strip|imageView2/1/w/96/h/96\n    return {\n      uid: res.data.avatar.split('/')[5],\n      title: res.data.nickname,\n      avatar: res.data.avatar,\n      type: 'jianshu',\n      displayName: '简书',\n      supportTypes: ['html'],\n      home: 'https://www.jianshu.com/settings/basic',\n      icon: 'https://www.jianshu.com/favicon.ico',\n      notebooks: notebooks,\n    }\n  }\n\n  async addPost(post) {\n    var notebooks = await $.get('https://www.jianshu.com/author/notebooks')\n    var firstNoteBook = notebooks[0]\n    defaultNoteBookId = firstNoteBook.id\n    var res = await $.ajax({\n      url: 'https://www.jianshu.com/author/notes',\n      type: 'POST',\n      dataType: 'JSON',\n      headers: {\n        accept: 'application/json',\n      },\n      contentType: 'application/json',\n      data: JSON.stringify({\n        at_bottom: false,\n        notebook_id: firstNoteBook.id,\n        title: post.post_title,\n      }),\n    })\n    console.log(res)\n    return {\n      status: 'success',\n      post_id: res.id,\n      notebook_id: firstNoteBook.id,\n    }\n  }\n\n  async editPost(post_id, post) {\n    var cacheVerions = NoteVersionCaches[post_id]\n    var notebook_id = post.notebook_id ? post.notebook_id : defaultNoteBookId\n\n    if (!cacheVerions) {\n      var bookNotes = await $.get(\n        'https://www.jianshu.com/author/notebooks/' + notebook_id + '/notes'\n      )\n      var currentNote = bookNotes.filter((t) => {\n        return t.id == post_id\n      })[0]\n\n      console.log(post_id, bookNotes)\n      NoteVersionCaches[post_id] = currentNote.autosave_control\n      NoteVersionCaches[post_id]++\n      cacheVerions = NoteVersionCaches[post_id]\n    } else {\n      NoteVersionCaches[post_id]++\n      cacheVerions = NoteVersionCaches[post_id]\n    }\n\n    console.log('currentNote', cacheVerions)\n    var requestData = {\n      autosave_control: cacheVerions,\n    }\n\n    if (post.post_content) {\n      requestData.content = post.post_content\n    }\n\n    if (post_id) {\n      requestData.id = post_id\n    }\n\n    if (post.post_title) {\n      requestData.title = post.post_title\n    }\n\n    // https://www.jianshu.com/author/notebooks/108908/notes\n    var res = await $.ajax({\n      url: 'https://www.jianshu.com/author/notes/' + post_id,\n      type: 'PUT',\n      dataType: 'JSON',\n      contentType: 'application/json',\n      headers: {\n        accept: 'application/json',\n      },\n      data: JSON.stringify(requestData),\n    })\n\n    return {\n      status: 'success',\n      notebook_id: notebook_id,\n      post_id: post_id,\n      draftLink:\n        'https://www.jianshu.com/writer#/notebooks/' +\n        notebook_id +\n        '/notes/' +\n        post_id,\n    }\n  }\n\n  async uploadFile(file) {\n    const tokenReq = await axios.get('https://www.jianshu.com/upload_images/token.json?filename='+ new Date().getTime() +'.png')\n    if(tokenReq.data.token) {\n      var blob = new Blob([file.bits], {\n        type: file.type\n      });\n      var formdata = new FormData()\n      formdata.append('token', tokenReq.data.token)\n      formdata.append('key', tokenReq.data.key)\n      formdata.append('x:protocol', 'https')\n      formdata.append('file', blob, new Date().getTime() + '.jpg')\n      var res = await axios({\n        url: 'https://upload.qiniup.com/',\n        method: 'post',\n        data: formdata,\n        headers: { 'Content-Type': 'multipart/form-data' },\n      })\n\n      if(!res.data.url) {\n        console.log(res.data);\n        throw new Error('upload failed')\n      }\n      var url = res.data.url\n      return [\n        {\n          id: tokenReq.data.key,\n          object_key: tokenReq.data.key,\n          url: url\n        }\n      ]\n    }\n    throw new Error('upload failed')\n  }\n\n  async uploadFileBySrc(file) {\n    var src = file.src\n    try {\n      // jianshu not support webp\n      if (src.indexOf('xitu.io') > -1) {\n        src = src.replace('webp', 'png')\n      }\n\n      var res = await $.ajax({\n        url: 'https://www.jianshu.com/upload_images/fetch',\n        type: 'POST',\n        contentType: 'application/json',\n        xhrFields: {\n          withCredentials: true,\n        },\n        headers: {\n          accept: 'application/json',\n        },\n        data: JSON.stringify({\n          url: src,\n        }),\n      })\n\n      // http only\n      console.log('uploadFile', res)\n      return [res]\n    } catch (e) {\n      console.log('JianShuDriver.uploadFile', e)\n      var error = e.responseJSON.error[0].message\n      throw new Error(error)\n    }\n  }\n\n  async preEditPost(post) {\n    var div = $('<div>')\n    $('body').append(div)\n    div.html(post.content)\n    var doc = div\n    var processEmptyLine = function (idx, el) {\n      var $obj = $(this)\n      var originalText = $obj.text()\n      var img = $obj.find('img')\n      var brs = $obj.find('br')\n      if (originalText == '') {\n        ;(function () {\n          if (img.length) return\n          if (!brs.length) return\n          $obj.remove()\n        })()\n      }\n\n      // try to replace as h2;\n      var strongTag = $obj.find('strong').eq(0)\n      var childStrongText = strongTag.text()\n      if (originalText == childStrongText) {\n        var strongSize = null\n        var tagStart = strongTag\n        var align = null\n        for (let index = 0; index < 4; index++) {\n          var fontSize = tagStart.css('font-size')\n          var textAlign = tagStart.css('text-align')\n          if (fontSize) {\n            strongSize = fontSize\n          }\n          if (textAlign) {\n            align = textAlign\n          }\n          if (align && strongSize) break\n          if (tagStart == $obj) {\n            console.log('near top')\n            break\n          }\n          tagStart = tagStart.parent()\n        }\n        if (strongSize) {\n          var theFontSize = parseInt(strongSize)\n          if (theFontSize > 17 && align == 'center') {\n            var newTag = $('<h2></h2>').append($obj.html())\n            $obj.after(newTag).remove()\n          }\n        }\n      }\n    }\n\n    // remove empty break line\n    doc.find('p').each(processEmptyLine)\n    doc.find('section').each(processEmptyLine)\n\n    var processBr = function (idx, el) {\n      var $obj = $(this)\n      if (!$obj.next().length) {\n        $obj.remove()\n      }\n    }\n    doc.find('br').each(processBr)\n    // table {\n    //     margin-bottom: 10px;\n    //     border-collapse: collapse;\n    //     display: table;\n    //     width: 100%!important;\n    // }\n    // td, th {\n    //     word-wrap: break-word;\n    //     word-break: break-all;\n    //     padding: 5px 10px;\n    //     border: 1px solid #DDD;\n    // }\n\n    // console.log('found table', doc.find('table'))\n    var tempDoc = $('<div>').append(doc.clone())\n    post.content =\n      tempDoc.children('div').length == 1\n        ? tempDoc.children('div').html()\n        : tempDoc.html()\n    // div.remove();\n  }\n\n  addPromotion(post) {\n    var sharcode = `<blockquote><p>本文使用 <a href=\"https://www.jianshu.com/p/5709df6fb58d\" class=\"internal\">文章同步助手</a> 同步</p></blockquote>`\n    post.content = post.content.trim() + `${sharcode}`\n  }\n}\n\n\n/***/ }),\n\n/***/ 47:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OsChinaAdapter)\n/* harmony export */ });\n\nfunction getDocumentByHTML(html, type = 'text/html') {\n\tconst parser = new DOMParser()\n\tconst htmlDoc = parser.parseFromString(html, type)\n  return htmlDoc\n}\n\nlet _cacheMeta = null\n\nclass OsChinaAdapter {\n  constructor() {\n    this.name = 'oschina'\n    modifyRequestHeaders('my.oschina.net/u', {\n    \tOrigin: 'https://my.oschina.net',\n      Referer: 'https://my.oschina.net/'\n    }, [\n    \t'*://my.oschina.net/u/*',\n    ])\n  }\n\n  async getMetaData() {\n    var res = await axios.get('https://www.oschina.net/blog')\n    var parser = new DOMParser()\n    var htmlDoc = parser.parseFromString(res.data, 'text/html')\n    var link = htmlDoc.querySelector('.user-info .current-user-avatar')\n    if (!link) {\n      throw Error('not found')\n    }\n\n    var uid = link.getAttribute('data-user-id')\n    var avatar = link.querySelector('img').src\n    var nickname = link.getAttribute('title')\n\n    const meta = {\n      uid: uid,\n      title: nickname,\n      avatar: avatar,\n      type: 'oschina',\n      displayName: '开源中国',\n      supportTypes: ['markdown', 'html'],\n      home: 'https://my.oschina.net/u/4227050/admin/drafts',\n      icon:\n        'https://www.oschina.net/favicon.ico',\n    }\n\n    _cacheMeta = meta\n    return meta\n  }\n\n  async addPost(post) {\n    return {\n      status: 'success',\n      post_id: 0\n    }\n  }\n\n  async editPost(post_id, post) {\n    if(!post.markdown) {\n      var turndownService = new turndown()\n      turndownService.use(tools.turndownExt)\n      var markdown = turndownService.turndown(post.post_content)\n      post.markdown = markdown\n      console.log(markdown)\n    }\n\n    const meta = _cacheMeta ? _cacheMeta : await this.getMetaData()\n    const writePageRes = await axios.get(`https://my.oschina.net/u/${meta.uid}/blog/write`)\n    const docPage = getDocumentByHTML(writePageRes.data)\n    const userTokenEl = docPage.querySelector('[data-name=g_user_code]')\n\n    if(!userTokenEl) {\n    \tthrow new Error('可能未登录？')\n    }\n\n    const userToken = userTokenEl.getAttribute('data-value')\n    const postStruct = {\n    \tdraft: 0,\n      id: null,\n      user_code: userToken,\n      title: post.post_title,\n      content: post.markdown,\n      content_type: 3, // 4=html\n      catalog: 6680617,\n      groups: 28,\n      type: 1,\n      origin_url: null,\n      privacy: 0,\n      deny_comment: 0,\n      as_top: 0,\n      downloadImg: 1,\n      isRecommend: 0,\n  \t}\n\n    const res = await $.ajax({\n    \turl: `https://my.oschina.net/u/${meta.uid}/blog/save_draft`,\n      type: 'POST',\n      dataType: 'JSON',\n      data: postStruct,\n    })\n\n    if(res.code != 1) {\n    \tthrow new Error(res.message)\n    }\n\n    post_id = res.result.draft\n    return {\n      status: 'success',\n      post_id: post_id,\n      draftLink: `https://my.oschina.net/u/${meta.uid}/blog/write/draft/${post_id}`,\n    }\n  }\n\n  async uploadFile(file) {\n    const meta = _cacheMeta ? _cacheMeta : await this.getMetaData()\n    var formdata = new FormData()\n    var blob = new Blob([file.bits])\n    formdata.append(\"editormd-image-file\", blob)\n    var res = await axios({\n      url: `https://my.oschina.net/u/${meta.uid}/space/markdown_img_upload`,\n      method: 'post',\n      data: formdata,\n      headers: { 'Content-Type': 'multipart/form-data' },\n    })\n    var url = res.data.url\n    return [\n      {\n\n        url: url,\n      },\n    ]\n  }\n\n  async preEditPost(post) {\n    var div = $('<div>')\n    $('body').append(div)\n\n    try {\n      // post.content = post.content.replace(/\\>\\s+\\</g,'');\n      console.log('zihu.Juejin')\n      div.html(post.content)\n      // var org = $(post.content);\n      // var doc = $('<div>').append(org.clone());\n      var doc = div\n      // var pres = doc.find(\"pre\");\n      tools.processDocCode(div)\n      tools.makeImgVisible(div)\n\n      var tempDoc = $('<div>').append(doc.clone())\n      post.content =\n        tempDoc.children('div').length == 1\n          ? tempDoc.children('div').html()\n          : tempDoc.html()\n\n      console.log('after.predEdit', post.content)\n    } catch (e) {\n      console.log('preEdit.error', e)\n    }\n  }\n}\n\n\n/***/ }),\n\n/***/ 192:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SoHuAdapter)\n/* harmony export */ });\nvar _souhuCacheMeta = null\n\nclass SoHuAdapter {\n  constructor() {\n    this.version = '0.0.1'\n    this.name = 'sohu'\n\n    // modify origin headers\n    modifyRequestHeaders('mp.sohu.com/mpbp', {\n        Origin: 'https://mp.sohu.com',\n        Referer: 'https://mp.sohu.com/'\n    }, [\n    \t'*://mp.sohu.com/mpbp/*',\n    ])\n  }\n\n  async getMetaData() {\n    const { data } = await axios.get('https://mp.sohu.com/mpbp/bp/account/register-info?_=' + Date.now())\n    console.log(data)\n    if(data.code != 2000000) {\n    \tthrow new Error('not found')\n    }\n    const accountInfo = data.data.account\n    _souhuCacheMeta = accountInfo\n    return {\n      uid: accountInfo.id,\n      title: accountInfo.nickName,\n      avatar: accountInfo.avatar,\n      type: 'sohu',\n      displayName: '搜狐号',\n      supportTypes: ['html'],\n      home: 'https://mp.sohu.com/mpfe/v3/main/first/page?newsType=1',\n      icon: 'https://mp.sohu.com/favicon.ico',\n    }\n  }\n\n  async addPost(post, _instance) {\n    return {\n      status: 'success',\n      post_id: 0,\n    }\n  }\n\n  async editPost(post_id, post) {\n\n    var postStruct = {\n        title: post.post_title,\n        brief: '',\n        content: post.post_content,\n        channelId: 39,\n        categoryId: -1,\n        id: 0,\n        userColumnId: 0,\n        businessCode: 0,\n        isOriginal: false,\n        cover: '',\n        attrIds: '',\n        topicIds: '',\n        isAd: 0,\n        reprint: false,\n        accountId: _souhuCacheMeta.id\n    }\n\n    var res = await $.ajax({\n      url: 'https://mp.sohu.com/mpbp/bp/news/v4/news/draft?accountId='+ _souhuCacheMeta.id,\n      type: 'POST',\n      dataType: 'JSON',\n      data: postStruct,\n    })\n\n    if(!res.success) throw new Error(res.msg)\n    var post_id = res.data\n    console.log(res)\n    return {\n      status: 'success',\n      post_id: post_id,\n      draftLink: 'https://mp.sohu.com/mpfe/v3/main/news/addarticle?spm=smmp.articlelist.0.0&contentStatus=2&id=' + post_id,\n    }\n  }\n\n  async uploadFile(file) {\n    var uploadUrl = 'https://mp.sohu.com/commons/front/outerUpload/image/file'\n    var file = new File([file.bits], 'temp', {\n      type: file.type\n    });\n    var formdata = new FormData()\n    formdata.append('file', file)\n    if (_souhuCacheMeta) {\n      formdata.append('accountId', _souhuCacheMeta.id)\n    }\n    var res = await axios({\n      url: uploadUrl,\n      method: 'post',\n      data: formdata,\n      headers: { 'Content-Type': 'multipart/form-data' },\n    })\n\n    return [\n      {\n        url: res.data.url,\n      },\n    ]\n  }\n\n  async preEditPost(post) {\n    var div = $('<div>')\n    $('body').append(div)\n    try {\n      console.log('zihu.Juejin')\n      div.html(post.content)\n      var doc = div\n      processDocCode(div)\n      makeImgVisible(div)\n\n      var tempDoc = $('<div>').append(doc.clone())\n      post.content =\n        tempDoc.children('div').length == 1\n          ? tempDoc.children('div').html()\n          : tempDoc.html()\n\n      console.log('after.predEdit', post.content)\n    } catch (e) {\n      console.log('preEdit.error', e)\n    }\n  }\n}\n\n\n/***/ }),\n\n/***/ 968:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ToutiaoAdapter)\n/* harmony export */ });\n\nclass ToutiaoAdapter {\n  constructor() {\n    // this.skipReadImage = true\n    this.name = 'toutiao'\n  }\n\n  async getMetaData() {\n    var res = await $.ajax({\n      url: 'https://mp.toutiao.com/mp/agw/media/get_media_info',\n    })\n    res = JSON.parse(res)\n    return {\n      uid: res.data.user.id,\n      title: res.data.user.screen_name,\n      avatar: res.data.user.https_avatar_url,\n      supportTypes: ['html'],\n      type: 'toutiao',\n      displayName: '头条',\n      home: 'https://mp.toutiao.com/profile_v3/graphic/publish',\n      icon: 'https://sf1-ttcdn-tos.pstatp.com/obj/ttfe/pgcfe/sz/mp_logo.png',\n    }\n  }\n\n  async addPost(post) {\n    return {\n      status: 'success',\n      post_id: 0,\n    }\n  }\n\n  async editPost(post_id, post) {\n    var pgc_feed_covers = []\n    if (post.post_thumbnail_raw && post.post_thumbnail_raw.images) {\n      pgc_feed_covers.push({\n        id: 0,\n        url: post.post_thumbnail_raw.url,\n        uri: post.post_thumbnail_raw.images[0].origin_web_uri,\n        origin_uri: post.post_thumbnail_raw.images[0].origin_web_uri,\n        ic_uri: '',\n        thumb_width: post.post_thumbnail_raw.images[0].width,\n        thumb_height: post.post_thumbnail_raw.images[0].height,\n      })\n    }\n\n    await $.get('https://mp.toutiao.com/profile_v3/graphic/publish')\n    var res = await $.ajax({\n      // url:'https://mp.toutiao.com/core/article/edit_article_post/?source=mp&type=article',\n      url: 'https://mp.toutiao.com/mp/agw/article/publish?source=mp&type=article',\n      type: 'POST',\n      dataType: 'JSON',\n      data: {\n        title: post.post_title,\n        article_ad_type: 2,\n        article_type: 0,\n        from_diagnosis: 0,\n        origin_debut_check_pgc_normal: 0,\n        tree_plan_article: 0,\n        save: 0,\n        pgc_id: 0,\n        content: post.post_content,\n        pgc_feed_covers: JSON.stringify(pgc_feed_covers),\n      },\n    })\n\n    if (!res.data) {\n      throw new Error(res.message)\n    }\n\n    return {\n      status: 'success',\n      post_id: res.data.pgc_id,\n      draftLink:\n        'https://mp.toutiao.com/profile_v3/graphic/publish?pgc_id=' +\n        res.data.pgc_id,\n    }\n  }\n\n  async uploadFileBySrc(file) {\n    var src = file.src\n    var res = await $.ajax({\n      url: 'https://mp.toutiao.com/tools/catch_picture/',\n      type: 'POST',\n      headers: {\n        accept: '*/*',\n      },\n      data: {\n        upfile: src,\n        version: 2,\n      },\n    })\n\n    // throw new Error('fuck');\n    if (res.images && !res.images.length) {\n      throw new Error('图片上传失败 ' + src)\n    }\n\n    // http only\n    console.log('uploadFile', res)\n    return [res]\n  }\n\n  async uploadFile(file) {\n    var src = file.src\n    var uploadUrl = 'https://mp.toutiao.com/mp/agw/article_material/photo/upload_picture?type=ueditor&pgc_watermark=1&action=uploadimage&encode=utf-8'\n    // var blob = new Blob([file.bits], {\n    //   type: file.type\n    // });\n    var file = new File([file.bits], 'temp', {\n      type: file.type\n    });\n    var formdata = new FormData()\n    formdata.append('upfile', file)\n    var res = await axios({\n      url: uploadUrl,\n      method: 'post',\n      data: formdata,\n      headers: { 'Content-Type': 'multipart/form-data' },\n    })\n\n    if (res.data.state != 'SUCCESS') {\n      throw new Error('图片上传失败 ' + src)\n    }\n    // http only\n    console.log('uploadFile', res)\n    return [{\n      id: res.data.original,\n      object_key: res.data.original,\n      url: res.data.url,\n      images: [\n        res.data\n      ]\n    }]\n  }\n\n  async preEditPost(post) {\n    var div = $('<div>')\n    $('body').append(div)\n\n    div.html(post.content)\n\n    // var org = $(post.content);\n    // var doc = $('<div>').append(org.clone());\n\n    var doc = div\n    var pres = doc.find('a')\n    for (let mindex = 0; mindex < pres.length; mindex++) {\n      const pre = pres.eq(mindex)\n      try {\n        pre.after(pre.html()).remove()\n      } catch (e) { }\n    }\n\n    var pres = doc.find('iframe')\n    for (let mindex = 0; mindex < pres.length; mindex++) {\n      const pre = pres.eq(mindex)\n      try {\n        pre.remove()\n      } catch (e) { }\n    }\n\n    try {\n      const images = doc.find('img')\n      for (let index = 0; index < images.length; index++) {\n        const image = images.eq(index)\n        const imgSrc = image.attr('src')\n        if (imgSrc && imgSrc.indexOf('.svg') > -1) {\n          console.log('remove svg Image')\n          image.remove()\n        }\n      }\n      const qqm = doc.find('qqmusic')\n      qqm.next().remove()\n      qqm.remove()\n    } catch (e) { }\n\n    post.content = $('<div>').append(doc.clone()).html()\n    console.log('post', post)\n  }\n\n  editImg(img, source) {\n    img.attr('web_uri', source.images[0].origin_web_uri)\n  }\n  //   <img class=\"\" src=\"http://p2.pstatp.com/large/pgc-image/bc0a9fc8e595453083d85deb947c3d6e\" data-ic=\"false\" data-ic-uri=\"\" data-height=\"1333\" data-width=\"1000\" image_type=\"1\" web_uri=\"pgc-image/bc0a9fc8e595453083d85deb947c3d6e\" img_width=\"1000\" img_height=\"1333\"></img>\n}\n\n\n/***/ }),\n\n/***/ 476:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WeixinAdapter)\n/* harmony export */ });\nvar weixinMetaCache = null\n\nclass WeixinAdapter {\n  constructor() {\n    this.meta = weixinMetaCache\n    this.name = 'weixin'\n  }\n\n  async getMetaData() {\n    var res = await $.ajax({ url: 'https://mp.weixin.qq.com/' })\n    var innerDoc = $(res)\n    var doc = $('<div>').append(innerDoc.clone())\n    // console.log('WeixinDriver', res);\n    var code = doc.find('script').eq(0).text()\n    code = code.substring(code.indexOf('window.wx.commonData'))\n    var wx = new Function(\n      'window.wx = {}; window.handlerNickname = function(){};' +\n        code +\n        '; return window.wx;'\n    )()\n    console.log(code, wx)\n    var commonData = Object.assign({}, wx.commonData)\n    delete window.wx\n    if (!commonData.data.t) {\n      throw new Error('未登录')\n    }\n    var metadata = {\n      uid: commonData.data.user_name,\n      title: commonData.data.nick_name,\n      token: commonData.data.t,\n      commonData: commonData,\n      avatar: doc.find('.weui-desktop-account__thumb').eq(0).attr('src'),\n      type: 'weixin',\n      supportTypes: ['html'],\n      home: 'https://mp.weixin.qq.com',\n      icon: 'https://mp.weixin.qq.com/favicon.ico',\n    }\n    weixinMetaCache = metadata\n    console.log('weixinMetaCache', weixinMetaCache)\n    return metadata\n  }\n\n  async addPost(post) {\n    return {\n      status: 'success',\n      post_id: 0,\n    }\n  }\n  \n  async searchAccount({ keyword, begin=0, count=5 }) {\n    var token = weixinMetaCache.token || '*********'\n    const apiURL = `https://mp.weixin.qq.com/cgi-bin/searchbiz?action=search_biz&begin=${begin}&count=${count}&query=${encodeURIComponent(keyword)}&token=${token}&lang=zh_CN&f=json&ajax=1`\n    const response = await $.get(apiURL)\n    return response\n  }\n  \n  async listArticle({ fakeid = '', begin=0, count=5}) {\n  \tvar token = weixinMetaCache.token || '*********'\n    const apiURL = `https://mp.weixin.qq.com/cgi-bin/appmsg?action=list_ex&begin=${begin}&count=${count}&fakeid=${fakeid}&type=9&query=&token=${token}&lang=zh_CN&f=json&ajax=1`\n    const response = await $.get(apiURL)\n    return response\n  }\n\n  async getArticle(data) {\n    var token = weixinMetaCache.token || '*********'\n    const tempRespone = await $.get(\n      `https://mp.weixin.qq.com/cgi-bin/appmsg?action=get_temp_url&appmsgid=${data.msgId}&itemidx=1&token=${token}&lang=zh_CN&f=json&ajax=1`\n    )\n    const { temp_url } = tempRespone\n    const htmlData = await $.get(temp_url)\n    const doc = $(htmlData)\n    console.log('htmlData', htmlData)\n    var post = {}\n\n    const allMetas = doc\n      .filter(function(index, el) {\n        return $(el).attr('property') && $(el).attr('content')\n      })\n      .map(function() {\n        return {\n          name: $(this).attr('property'),\n          content: $(this).attr('content'),\n        }\n      })\n      .toArray()\n\n    const metaObj = {}\n    allMetas.forEach(obj => {\n      metaObj[obj.name] = obj.content\n    })\n\n    post.title = metaObj['og:title']\n    post.content = doc.find('#js_content').html()\n    post.thumb = metaObj['og:image']\n    post.desc = metaObj['og:description']\n    post.link = metaObj['og:url'];\n    console.log('post', post, doc)\n    return post\n  }\n\n  async editPost(post_id, post) {\n    console.log('editPost', post.post_thumbnail)\n    var res = await $.ajax({\n      url:\n        'https://mp.weixin.qq.com/cgi-bin/operate_appmsg?t=ajax-response&sub=create&type=10&token=' +\n        weixinMetaCache.token +\n        '&lang=zh_CN',\n      type: 'POST',\n      dataType: 'JSON',\n      data: {\n        token: weixinMetaCache.token,\n        lang: 'zh_CN',\n        f: 'json',\n        ajax: '1',\n        random: Math.random(),\n        AppMsgId: '',\n        count: '1',\n        data_seq: '0',\n        operate_from: 'Chrome',\n        isnew: '0',\n        ad_video_transition0: '',\n        can_reward0: '0',\n        related_video0: '',\n        is_video_recommend0: '-1',\n        title0: post.post_title,\n        author0: '',\n        writerid0: '0',\n        fileid0: '',\n        digest0: post.post_title,\n        auto_gen_digest0: '1',\n        content0: post.post_content,\n        sourceurl0: '',\n        need_open_comment0: '1',\n        only_fans_can_comment0: '0',\n        cdn_url0: '',\n        cdn_235_1_url0: '',\n        cdn_1_1_url0: '',\n        cdn_url_back0: '',\n        crop_list0: '',\n        music_id0: '',\n        video_id0: '',\n        voteid0: '',\n        voteismlt0: '',\n        supervoteid0: '',\n        cardid0: '',\n        cardquantity0: '',\n        cardlimit0: '',\n        vid_type0: '',\n        show_cover_pic0: '0',\n        shortvideofileid0: '',\n        copyright_type0: '0',\n        releasefirst0: '',\n        platform0: '',\n        reprint_permit_type0: '',\n        allow_reprint0: '',\n        allow_reprint_modify0: '',\n        original_article_type0: '',\n        ori_white_list0: '',\n        free_content0: '',\n        fee0: '0',\n        ad_id0: '',\n        guide_words0: '',\n        is_share_copyright0: '0',\n        share_copyright_url0: '',\n        source_article_type0: '',\n        reprint_recommend_title0: '',\n        reprint_recommend_content0: '',\n        share_page_type0: '0',\n        share_imageinfo0: '{\"list\":[]}',\n        share_video_id0: '',\n        dot0: '{}',\n        share_voice_id0: '',\n        insert_ad_mode0: '',\n        categories_list0: '[]',\n        sections0:\n          '[{\"section_index\":1000000,\"text_content\":\"​kkk\",\"section_type\":9,\"ad_available\":false}]',\n        compose_info0:\n          '{\"list\":[{\"blockIdx\":1,\"content\":\"<p>​kkk<br></p>\",\"width\":574,\"height\":27,\"topMargin\":0,\"blockType\":9,\"background\":\"rgba(0, 0, 0, 0)\",\"text\":\"kkk\",\"textColor\":\"rgb(51, 51, 51)\",\"textFontSize\":\"17px\",\"textBackGround\":\"rgba(0, 0, 0, 0)\"}]}',\n      },\n    })\n\n    if (!res.appMsgId) {\n      var err = formatError(res)\n      console.log('error', err)\n      throw new Error(\n        '同步失败 错误内容：' + (err && err.errmsg ? err.errmsg : res.ret)\n      )\n    }\n    return {\n      status: 'success',\n      post_id: res.appMsgId,\n      draftLink:\n        'https://mp.weixin.qq.com/cgi-bin/appmsg?t=media/appmsg_edit&action=edit&type=10&appmsgid=' +\n        res.appMsgId +\n        '&token=' +\n        weixinMetaCache.token +\n        '&lang=zh_CN',\n    }\n    // https://zhuanlan.zhihu.com/api/articles/68769713/draft\n  }\n\n  async uploadFile(file) {\n    var formdata = new FormData()\n    var blob = new Blob([file.bits], {\n        type: file.type\n    });\n\n    formdata.append('type', blob.type)\n    formdata.append('id', new Date().getTime())\n    formdata.append('name', new Date().getTime() + '.jpg')\n    formdata.append('lastModifiedDate', new Date().toString())\n    formdata.append('size', blob.size)\n    formdata.append('file', blob, new Date().getTime() + '.jpg')\n\n    var ticket_id = this.meta.commonData.data.user_name,\n      ticket = this.meta.commonData.data.ticket,\n      svr_time =  this.meta.commonData.data.time,\n      token = this.meta.commonData.data.t,\n      seq = new Date().getTime();\n\n    var res = await axios({\n      url: `https://mp.weixin.qq.com/cgi-bin/filetransfer?action=upload_material&f=json&scene=8&writetype=doublewrite&groupid=1&ticket_id=${ticket_id}&ticket=${ticket}&svr_time=${svr_time}&token=${token}&lang=zh_CN&seq=${seq}&t=` + Math.random(),\n      method: 'post',\n      data: formdata,\n      headers: { 'Content-Type': 'multipart/form-data' },\n    })\n    var url = res.data.cdn_url\n    if(res.data.base_resp.err_msg != 'ok') {\n      console.log(res.data);\n      throw new Error('upload failed')\n    }\n    //  return url;\n    return [\n      {\n        id: res.data.content,\n        object_key: res.data.content,\n        url: url,\n      },\n    ]\n  }\n\n  async uploadFileBySource(file) {\n    var src = file.src\n    var res = await $.ajax({\n      url:\n        'https://mp.weixin.qq.com/cgi-bin/uploadimg2cdn?lang=zh_CN&token=' +\n        weixinMetaCache.token +\n        '&t=' +\n        Math.random(),\n      type: 'POST',\n      dataType: 'JSON',\n      data: {\n        imgurl: src,\n        t: 'ajax-editor-upload-img',\n        token: weixinMetaCache.token,\n        lang: 'zh_CN',\n        f: 'json',\n        ajax: '1',\n      },\n    })\n\n    if (res.errcode != 0) {\n      throw new Error('图片上传失败' + src)\n    }\n    console.log(res)\n    return [\n      {\n        id: 'aaa',\n        object_key: 'aaa',\n        url: res.url,\n      },\n    ]\n  }\n\n  async preEditPost(post) {\n    var div = $('<div>')\n    $('body').append(div)\n\n    if (post.inline_content) {\n      post.content = post.inline_content\n    }\n\n    div.html(post.content)\n\n    var doc = div\n    var tags = doc.find('p')\n    for (let mindex = 0; mindex < tags.length; mindex++) {\n      const tag = tags.eq(mindex)\n      try {\n        var nextHasImage = tag.next().children('img').length\n        var span = $('<span></span>')\n        span.html(tag.html())\n        tag.html('')\n        tag.append(span)\n        // if (!tag.children(\"br\").length) tag.css(\"margin-bottom\", \"20px\");\n        // tag.after(\"<p><br></p>\");\n        // span.css(\"color\", \"rgb(68, 68, 68)\");\n        // span.css(\"font-size\", \"16px\");\n      } catch (e) {}\n    }\n\n    var tags = doc.find('img')\n    for (let mindex = 0; mindex < tags.length; mindex++) {\n      const tag = tags.eq(mindex)\n      const wraperTag = tag.parent()\n      try {\n        tag.removeAttr('_src')\n        tag.attr('style', '')\n        wraperTag.replaceWith('<p>' + wraperTag.html() + '</p>')\n      } catch (e) {}\n    }\n\n    var pres = doc.find('a')\n    for (let mindex = 0; mindex < pres.length; mindex++) {\n      const pre = pres.eq(mindex)\n      try {\n        pre.after(pre.html()).remove()\n      } catch (e) {}\n    }\n\n    var processEmptyLine = function (idx, el) {\n      var $obj = $(this)\n      var originalText = $obj.text()\n      var img = $obj.find('img')\n      var brs = $obj.find('br')\n      if (originalText == '') {\n        ;(function () {\n          if (img.length) return\n          if (!brs.length) return\n          $obj.remove()\n        })()\n      }\n    }\n\n    var processListItem = function (idx, el) {\n      var $obj = $(this)\n      $obj.html($('<p></p>').append($obj.html()))\n    }\n\n    doc.find('li').each(processListItem)\n    // remove empty break line\n    doc.find('p').each(processEmptyLine)\n\n    var processBr = function (idx, el) {\n      var $obj = $(this)\n      if (!$obj.next().length) {\n        $obj.remove()\n      }\n    }\n\n    doc.find('br').each(processBr)\n    post.content = $('<div>')\n      .append(\n        \"<section style='margin-left: 6px;margin-right: 6px;line-height: 1.75em;'>\" +\n          doc.clone().html() +\n          '</section>'\n      )\n      .html()\n\n    console.log('post.content', post.content)\n    var inlineCssHTML = juice.inlineContent(\n      post.content,\n      `\n    /**\n    * common style\n    */\n\n   html, address,\n   blockquote,\n   body, dd, div,\n   dl, dt, fieldset, form,\n   frame, frameset,\n   h1, h2, h3, h4,\n   h5, h6, noframes,\n   ol, p, ul, center,\n   dir, hr, menu, pre   { display: block; unicode-bidi: embed }\n   li              { display: list-item }\n   head            { display: none }\n   table           { display: table }\n   tr              { display: table-row }\n   thead           { display: table-header-group }\n   tbody           { display: table-row-group }\n   tfoot           { display: table-footer-group }\n   col             { display: table-column }\n   colgroup        { display: table-column-group }\n   td, th          { display: table-cell }\n   caption         { display: table-caption }\n   th              { font-weight: bolder; text-align: center }\n   caption         { text-align: center }\n   body            { margin: 8px }\n   h1              { font-size: 2em; margin: .67em 0 }\n   h2              { font-size: 1.5em; margin: .75em 0 }\n   h3              { font-size: 1.17em; margin: .83em 0 }\n   h4, p,\n   blockquote, ul,\n   fieldset, form,\n   ol, dl, dir,\n   menu            { margin: 1.12em 0 }\n   h5              { font-size: .83em; margin: 1.5em 0 }\n   h6              { font-size: .75em; margin: 1.67em 0 }\n   h1, h2, h3, h4,\n   h5, h6, b,\n   strong          { font-weight: bolder }\n   blockquote      { margin-left: 40px; margin-right: 40px }\n   i, cite, em,\n   var, address    { font-style: italic }\n   pre, tt, code,\n   kbd, samp       { font-family: monospace }\n   pre             { white-space: pre }\n   button, textarea,\n   input, select   { display: inline-block }\n   big             { font-size: 1.17em }\n   small, sub, sup { font-size: .83em }\n   sub             { vertical-align: sub }\n   sup             { vertical-align: super }\n   table           { border-spacing: 2px; }\n   thead, tbody,\n   tfoot           { vertical-align: middle }\n   td, th, tr      { vertical-align: inherit }\n   s, strike, del  { text-decoration: line-through }\n   hr              { border: 1px inset }\n   ol, ul, dir,\n   menu, dd        { margin-left: 40px }\n   ol              { list-style-type: decimal }\n   ol ul, ul ol,\n   ul ul, ol ol    { margin-top: 0; margin-bottom: 0 }\n   u, ins          { text-decoration: underline }\n   br:before       { content: \"\\A\"; white-space: pre-line }\n   center          { text-align: center }\n   :link, :visited { text-decoration: underline }\n   :focus          { outline: thin dotted invert }\n\n   /* Begin bidirectionality settings (do not change) */\n   BDO[DIR=\"ltr\"]  { direction: ltr; unicode-bidi: bidi-override }\n   BDO[DIR=\"rtl\"]  { direction: rtl; unicode-bidi: bidi-override }\n\n   *[DIR=\"ltr\"]    { direction: ltr; unicode-bidi: embed }\n   *[DIR=\"rtl\"]    { direction: rtl; unicode-bidi: embed }\n\n   @media print {\n     h1            { page-break-before: always }\n     h1, h2, h3,\n     h4, h5, h6    { page-break-after: avoid }\n     ul, ol, dl    { page-break-before: avoid }\n   }\n   h1,\n   h2,\n   h3,\n   h4,\n   h5,\n   h6 {\n     font-weight: bold;\n   }\n\n   h1 {\n     font-size: 1.25em;\n     line-height: 1.4em;\n   }\n\n   h2 {\n     font-size: 1.125em;\n   }\n\n   h3 {\n     font-size: 1.05em;\n   }\n\n   h4,\n   h5,\n   h6 {\n     font-size: 1em;\n     margin: 1em 0;\n   }\n\n    p {\n      color: rgb(51, 51, 51);\n      font-size: 15px;\n    }\n\n    li p {\n      margin: 0;\n    }\n   `\n    )\n    console.log('inlineCssHTML new', inlineCssHTML)\n    post.content = inlineCssHTML\n  }\n}\n\nfunction formatError(e) {\n  var r,\n    a = {\n      errmsg: '',\n      index: !1,\n    }\n  switch (\n    ('undefined' != typeof e.ret\n      ? (r = 1 * e.ret)\n      : e.base_resp &&\n        'undefined' != typeof e.base_resp.ret &&\n        (r = 1 * e.base_resp.ret),\n    1 * r)\n  ) {\n    case -8:\n    case -6:\n      ;(e.ret = '-6'), (a.errmsg = '请输入验证码')\n      break\n\n    case 62752:\n      a.errmsg = '可能含有具备安全风险的链接，请检查'\n      break\n\n    case 64505:\n      a.errmsg = '发送预览失败，请稍后再试'\n      break\n\n    case 64504:\n      a.errmsg = '保存图文消息发送错误，请稍后再试'\n      break\n\n    case 64518:\n      a.errmsg = '正文只能包含一个投票'\n      break\n\n    case 10704:\n    case 10705:\n      a.errmsg = '该素材已被删除'\n      break\n\n    case 10701:\n      a.errmsg = '用户已被加入黑名单，无法向其发送消息'\n      break\n\n    case 10703:\n      a.errmsg = '对方关闭了接收消息'\n      break\n\n    case 10700:\n    case 64503:\n      a.errmsg =\n        '1.接收预览消息的微信尚未关注公众号，请先扫码关注<br /> 2.如果已经关注公众号，请查看微信的隐私设置（在手机微信的“我->设置->隐私->添加我的方式”中），并开启“可通过以下方式找到我”的“手机号”、“微信号”、“QQ号”，否则可能接收不到预览消息'\n      break\n\n    case 64502:\n      a.errmsg = '你输入的微信号不存在，请重新输入'\n      break\n\n    case 64501:\n      a.errmsg = '你输入的帐号不存在，请重新输入'\n      break\n\n    case 412:\n      a.errmsg = '图文中含非法外链'\n      break\n\n    case 64515:\n      a.errmsg = '当前素材非最新内容，请重新打开并编辑'\n      break\n\n    case 320001:\n      a.errmsg = '该素材已被删除，无法保存'\n      break\n\n    case 64702:\n      a.errmsg = '标题超出64字长度限制'\n      break\n\n    case 64703:\n      a.errmsg = '摘要超出120字长度限制'\n      break\n\n    case 64704:\n      a.errmsg = '推荐语超出300字长度限制'\n      break\n\n    case 64708:\n      a.errmsg = '推荐语超出140字长度限制'\n      break\n\n    case 64515:\n      a.errmsg = '当前素材非最新内容'\n      break\n\n    case 200041:\n      a.errmsg = '此素材有文章存在违规，无法编辑'\n      break\n\n    case 64506:\n      a.errmsg = '保存失败,链接不合法'\n      break\n\n    case 64507:\n      a.errmsg =\n        '内容不能包含外部链接，请输入http://或https://开头的公众号相关链接'\n      break\n\n    case 64510:\n      a.errmsg = '内容不能包含音频，请调整'\n      break\n\n    case 64511:\n      a.errmsg = '内容不能包多个音频，请调整'\n      break\n\n    case 64512:\n      a.errmsg = '文章中音频错误,请使用音频添加按钮重新添加。'\n      break\n\n    case 64508:\n      a.errmsg = '查看原文链接可能具备安全风险，请检查'\n      break\n\n    case 64550:\n      a.errmsg = '请勿插入不合法的图文消息链接'\n      break\n\n    case 64558:\n      a.errmsg = '请勿插入图文消息临时链接，链接会在短期失效'\n      break\n\n    case 64559:\n      a.errmsg = '请勿插入未群发的图文消息链接'\n      break\n\n    case -99:\n      a.errmsg = '内容超出字数，请调整'\n      break\n\n    case 64705:\n      a.errmsg = '内容超出字数，请调整'\n      break\n\n    case -1:\n      a.errmsg = '系统错误，请注意备份内容后重试'\n      break\n\n    case -2:\n    case 200002:\n      a.errmsg = '参数错误，请注意备份内容后重试'\n      break\n\n    case 64509:\n      a.errmsg = '正文中不能包含超过3个视频，请重新编辑正文后再保存。'\n      break\n\n    case -5:\n      a.errmsg = '服务错误，请注意备份内容后重试。'\n      break\n\n    case 64513:\n      a.errmsg = '请从正文中选择封面，再尝试保存。'\n      break\n\n    case -206:\n      a.errmsg = '目前，服务负荷过大，请稍后重试。'\n      break\n\n    case 10801:\n      ;(a.errmsg =\n        '标题不能有违反公众平台协议、相关法律法规和政策的内容，请重新编辑。'),\n        (a.index = 1 * e.msg)\n      break\n\n    case 10802:\n      ;(a.errmsg =\n        '作者不能有违反公众平台协议、相关法律法规和政策的内容，请重新编辑。'),\n        (a.index = 1 * e.msg)\n      break\n\n    case 10803:\n      ;(a.errmsg = '敏感链接，请重新添加。'), (a.index = 1 * e.msg)\n      break\n\n    case 10804:\n      ;(a.errmsg =\n        '摘要不能有违反公众平台协议、相关法律法规和政策的内容，请重新编辑。'),\n        (a.index = 1 * e.msg)\n      break\n\n    case 10806:\n      ;(a.errmsg =\n        '正文不能有违反公众平台协议、相关法律法规和政策的内容，请重新编辑。'),\n        (a.index = 1 * e.msg)\n      break\n\n    case 10808:\n      ;(a.errmsg =\n        '推荐语不能有违反公众平台协议、相关法律法规和政策的内容，请重新编辑。'),\n        (a.index = 1 * e.msg)\n      break\n\n    case 10807:\n      a.errmsg = '内容不能违反公众平台协议、相关法律法规和政策，请重新编辑。'\n      break\n\n    case 200003:\n      a.errmsg = '登录态超时，请重新登录。'\n      break\n\n    case 64513:\n      a.errmsg = '封面必须存在正文中，请检查封面'\n      break\n\n    case 64551:\n      a.errmsg = '请检查图文消息中的微视链接后重试。'\n      break\n\n    case 64552:\n      a.errmsg = '请检查阅读原文中的链接后重试。'\n      break\n\n    case 64553:\n      a.errmsg = '请不要在图文消息中插入超过5张卡券。请删减卡券后重试。'\n      break\n\n    case 64554:\n      a.errmsg = '在当前情况下不允许在图文消息中插入卡券，请删除卡券后重试。'\n      break\n\n    case 64555:\n      a.errmsg = '请检查图文消息卡片跳转的链接后重试。'\n      break\n\n    case 64556:\n      a.errmsg = '卡券不属于该公众号，请删除后重试'\n      break\n\n    case 64557:\n      a.errmsg = '卡券无效，请删除后重试。'\n      break\n\n    case 13002:\n      ;(a.errmsg = '该广告卡片已过期，删除后才可保存成功'),\n        (a.index = 1 * e.msg)\n      break\n\n    case 13003:\n      ;(a.errmsg = '已有文章插入过该广告卡片，一个广告卡片仅可插入一篇文章'),\n        (a.index = 1 * e.msg)\n      break\n\n    case 13004:\n      ;(a.errmsg = '该广告卡片与图文消息位置不一致'), (a.index = 1 * e.msg)\n      break\n\n    case 15801:\n    case 15802:\n    case 15803:\n    case 15804:\n    case 15805:\n    case 15806:\n      a.errmsg =\n        e.remind_wording ||\n        '你所编辑的内容可能含有违反微信公众平台平台协议、相关法律法规和政策的内容'\n      break\n\n    case 1530503:\n      a.errmsg = '请勿添加其他公众号的主页链接'\n      break\n\n    case 1530504:\n      a.errmsg = '请勿添加其他公众号的主页链接'\n      break\n\n    case 1530510:\n      a.errmsg = '链接已失效，请在手机端重新复制链接'\n      break\n\n    case 153007:\n    case 153008:\n    case 153009:\n    case 153010:\n      a.errmsg =\n        '很抱歉，原创声明不成功|你的文章内容未达到声明原创的要求，满足以下任一条件可发起声明：<br />1、文章文字字数大于300字，且自己创作的内容大于引用内容<br />2、文章文字字数小于300字，无视频，且图片（包括封面图）均为你已成功声明原创的图片<br />说明：上述要求中，文章文字字数不包含标点符号和空格，请知悉。'\n      break\n\n    case 153200:\n      a.errmsg = '无权限声明原创，取消声明后重试'\n      break\n\n    case 1530511:\n      a.errmsg = '链接已失效，请在手机端重新复制链接'\n      break\n\n    case 220001:\n      a.errmsg = '\"素材管理\"中的存储数量已达到上限，请删除后再操作。'\n      break\n\n    case 220002:\n      a.errmsg = '你的图片库已达到存储上限，请进行清理。'\n      break\n\n    case 153012:\n      a.errmsg = '请设置转载类型'\n      break\n\n    case 200042:\n      a.errmsg = '图文中包含的小程序素材不能多于50个、小程序帐号不能多于10个'\n      break\n\n    case 200043:\n      a.errmsg = '图文中包含没有关联的小程序，请删除后再保存'\n      break\n\n    case 64601:\n      a.errmsg = '一篇文章只能插入一个广告卡片'\n      break\n\n    case 64602:\n      a.errmsg = '尚未开通文中广告位，但文章中有广告'\n      break\n\n    case 64603:\n      a.errmsg = '文中广告前不足300字'\n      break\n\n    case 64604:\n      a.errmsg = '文中广告后不足300字'\n      break\n\n    case 64605:\n      a.errmsg = '文中不能同时插入文中广告和互选广告'\n      break\n\n    case 65101:\n      a.errmsg = '图文模版数量已达到上限，请删除后再操作'\n      break\n\n    case 64560:\n      a.errmsg = '请勿插入历史图文消息页链接'\n      break\n\n    case 64561:\n      a.errmsg = '请勿插入mp.weixin.qq.com域名下的非图文消息链接'\n      break\n\n    case 64562:\n      a.errmsg = '请勿插入非mp.weixin.qq.com域名的链接'\n      break\n\n    case 153013:\n      a.errmsg = '文章内含有投票，不能设置为开放转载'\n      break\n\n    case 153014:\n      a.errmsg = '文章内含有卡券，不能设置为开放转载'\n      break\n\n    case 153015:\n      a.errmsg = '文章内含有小程序链接，不能设置为开放转载'\n      break\n\n    case 153016:\n      a.errmsg = '文章内含有小程序链接，不能设置为开放转载'\n      break\n\n    case 153017:\n      a.errmsg = '文章内含有小程序卡片，不能设置为开放转载'\n      break\n\n    case 153018:\n      a.errmsg = '文章内含有商品，不能设置为开放转载'\n      break\n\n    case 153019:\n      a.errmsg = '文章内含有广告卡片，不能设置为开放转载'\n      break\n\n    case 153020:\n      a.errmsg = '文章内含有广告卡片，不能设置为开放转载'\n      break\n\n    case 153021:\n      a.errmsg = '文章内含有广告卡片，不能设置为开放转载'\n      break\n\n    case 153101:\n      a.errmsg = '含有原文已删除的转载文章，请删除后重试'\n      break\n\n    case 64707:\n      a.errmsg = '赞赏账户授权失效或者状态异常'\n      break\n\n    case 420001:\n      a.errmsg = '封面图不支持GIF，请更换'\n      break\n\n    case 353004:\n      a.errmsg = '不支持添加商品，请删除后重试'\n      break\n\n    case 442001:\n      a.errmsg = '帐号新建/编辑素材能力已被封禁，暂不可使用。'\n      break\n\n    default:\n      a.errmsg = '系统繁忙，请稍后重试'\n  }\n  return a\n}\n\n// exports.driver = WeixinAdapter\n\n\n/***/ }),\n\n/***/ 402:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WordpressAdapter)\n/* harmony export */ });\nfunction xmlrpcWrapper(conf) {\n  return new Promise((resolve, reject) => {\n    $.xmlrpc(conf).then(\n      function(response, status, xhr) {\n        resolve({\n          response,\n          status,\n          xhr,\n        })\n      },\n      function(jqXHR, status, error) {\n        reject({\n          jqXHR,\n          status,\n          error,\n        })\n      }\n    )\n  })\n}\n\nclass WordpressAdapter {\n  constructor(url, user, pwd, isTypecho) {\n    this.url = url\n    this.user = user\n    this.pwd = pwd\n    this.isTypecho = isTypecho\n  }\n\n  getRPC() {\n    var endPoint = this.url + '/xmlrpc.php'\n    if (this.isTypecho) {\n      endPoint = this.url + '/action/xmlrpc'\n    }\n    return endPoint\n  }\n\n  async getMetaData() {\n    var params = [this.user, this.pwd]\n    var res = await xmlrpcWrapper({\n      url: this.getRPC(),\n      methodName: 'wp.getUsersBlogs',\n      params: params,\n    })\n    console.log('end')\n    res.icon = chrome.extension.getURL('images/wordpress.ico')\n    return res\n  }\n\n  addPost(post) {\n    if (this.isTypecho) {\n      return {\n        status: 'success',\n        post_id: '1',\n      }\n    }\n\n    var params = [0, this.user, this.pwd, post]\n    var end = this.url\n    return xmlrpcWrapper({\n      url: this.getRPC(),\n      methodName: 'wp.newPost',\n      params: params,\n    })\n  }\n\n  editPost(post_id, post) {\n    var params = [0, this.user, this.pwd, post]\n    var endpoint = this.getRPC()\n    var isTypecho = this.isTypecho\n    if (isTypecho) {\n      params.push(false)\n      params[3] = {\n        title: post['post_title'],\n        // text: \"!!!\\n\" + post['post_content'].trim() + \"\\n!!!\",\n        description: post['post_content'].trim(),\n        // markdown: 1\n      }\n      console.log('params', params)\n    } else {\n      params[3] = post_id\n      params.push(post)\n    }\n    return new Promise((resolve, reject) => {\n      ;(async () => {\n        try {\n          var res = await xmlrpcWrapper({\n            url: endpoint,\n            methodName: isTypecho ? 'metaWeblog.newPost' : 'wp.editPost',\n            params: params,\n          })\n\n          res.draftLink = this.url + '?p=' + post_id\n          console.log('Wordpress', res)\n          resolve(res)\n        } catch (e) {\n          reject(e)\n        }\n      })()\n    })\n  }\n\n  //  'metaWeblog.getPost' => array($this, 'mwGetPost'),\n\n  editImg(img, source) {\n    // img.attr('web_uri', source.images[0].origin_web_uri)\n    img.removeAttr('data-src')\n  }\n\n  uploadFile(file) {\n    if (this.isTypecho) {\n      file['bytes'] = file['bits']\n      delete file['bits']\n    }\n    var params = [0, this.user, this.pwd, file]\n\n    var end = this.url\n    return $.xmlrpc({\n      url: this.getRPC(),\n      methodName: 'wp.uploadFile',\n      params: params,\n    })\n  }\n}\n\n\n/***/ }),\n\n/***/ 534:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ XueQiuAdapter)\n/* harmony export */ });\n\nclass XueQiuAdapter {\n  constructor() {\n    this.name = 'xueqiu'\n    modifyRequestHeaders('mp.xueqiu.com/xq/statuses', {\n      Origin: 'https://mp.xueqiu.com',\n      Referer: 'https://mp.xueqiu.com'\n    }, [\n      '*://mp.xueqiu.com/xq/*',\n    ], function (details) {\n    })\n  }\n\n  async getMetaData() {\n    const res = await axios.get('https://mp.xueqiu.com/write/')\n    const parser = new DOMParser()\n    const htmlDoc = parser.parseFromString(res.data, 'text/html')\n    const link = htmlDoc.querySelector('#currentUser')\n    if (!link) {\n      throw Error('not found')\n    }\n    const state = new Function(\"return \" + link.innerHTML.replace('window.UOM_CURRENTUSER = ', ''))()\n    const { currentUser } = state\n    if (currentUser.id == \"\")  throw Error('not found')\n    return {\n      uid: currentUser.id,\n      title: currentUser.screen_name,\n      avatar:\n        `https:${currentUser.photo_domain}` +\n        currentUser.profile_image_url.split(',')[0],\n      supportTypes: ['html'],\n      type: 'xueqiu',\n      displayName: '雪球',\n      home: 'https://mp.xueqiu.com/write',\n      icon: 'https://xqdoc.imedao.com/17aebcfb84a145d33fc18679.ico',\n    }\n  }\n\n  async addPost(post) {\n    return {\n      status: 'success',\n      post_id: 0,\n    }\n  }\n\n  async editPost(post_id, post) {\n    const content = `${post.post_content}`\n    try {\n      var res = await $.ajax({\n        url: 'https://mp.xueqiu.com/xq/statuses/draft/save.json',\n        type: 'POST',\n        dataType: 'JSON',\n        data: {\n          text: post.post_content,\n          title: post.post_title,\n          cover_pic: null,\n          flags: false,\n          original_event: null,\n          status_id: null,\n          legal_user_visible: false,\n          is_private: false,\n        },\n      })\n\n      const post_id = res.id\n      return {\n        status: 'success',\n        post_id: post_id,\n        draftLink: `https://mp.xueqiu.com/write/draft/${post_id}`\n      }\n    } catch (e) {\n      throw new Error(e.toString())\n    }\n  }\n\n  async uploadFile(file) {\n    const { src, post_id } = file\n    var uploadUrl = \"https://mp.xueqiu.com/xq/photo/upload.json\"\n    var file = new File([file.bits], 'temp.jpg', {\n      type: file.type\n    });\n    var formdata = new FormData()\n    formdata.append('file', file)\n    var res = await axios({\n      url: uploadUrl,\n      method: 'post',\n      data: formdata,\n      headers: { 'Content-Type': 'multipart/form-data' },\n    })\n\n    if (!res.data.url) {\n      throw new Error('图片上传失败 ' + src)\n    }\n    // http only\n    console.log('uploadFile', res)\n    const url = `https:${res.data.url}/${res.data.filename}`;\n    return [{\n      id: res.data.filename,\n      object_key: res.data.filename,\n      url: url,\n      images: [\n        res.data\n      ]\n    }]\n  }\n\n  async preEditPost(post) {\n    var div = $('<div>')\n    $('body').append(div)\n    div.html(post.content)\n    var doc = div\n\n    tools.doPreFilter(div)\n    tools.processDocCode(div)\n\n    var pres = doc.find('a')\n    for (let mindex = 0; mindex < pres.length; mindex++) {\n      const pre = pres.eq(mindex)\n      try {\n        pre.after(pre.html()).remove()\n      } catch (e) { }\n    }\n\n    var pres = doc.find('iframe')\n    for (let mindex = 0; mindex < pres.length; mindex++) {\n      const pre = pres.eq(mindex)\n      try {\n        pre.remove()\n      } catch (e) { }\n    }\n\n    try {\n      const images = doc.find('img')\n      for (let index = 0; index < images.length; index++) {\n        const image = images.eq(index)\n        const imgSrc = image.attr('src')\n        if (imgSrc && imgSrc.indexOf('.svg') > -1) {\n          console.log('remove svg Image')\n          image.remove()\n        }\n      }\n      const qqm = doc.find('qqmusic')\n      qqm.next().remove()\n      qqm.remove()\n    } catch (e) { }\n\n    post.content = $('<div>').append(doc.clone()).html()\n    console.log('post', post)\n  }\n}\n\n\n/***/ }),\n\n/***/ 301:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ YuQueAdapter)\n/* harmony export */ });\nlet _cachedBook = null;\n\nclass YuQueAdapter {\n  constructor() {\n    // this.skipReadImage = true\n    this.name = 'yuque'\n    modifyRequestHeaders('www.yuque.com/api/', {\n    \tOrigin: 'https://www.yuque.com',\n      Referer: 'https://www.yuque.com/dashboard'\n    }, [\n    \t'*://www.yuque.com/api/*',\n    ], function(details) {\n\t\t\t// parse token from cookie inject to headers\n      if (details.url.indexOf('www.yuque.com/api/') > -1) {\n        helpers.parseTokenAndToHeaders(details, 'yuque_ctoken', 'x-csrf-token')\n      }\n    })\n  }\n\n  async getMetaData() {\n    var res = await $.ajax({\n      url: 'https://www.yuque.com/api/mine/common_used?',\n    })\n    \n    const firstBook = res.data.user_books[0];\n    const user = firstBook.user;\n    _cachedBook = firstBook.target_id;\n    return {\n      bookId: firstBook.target_id,\n    \tuid: user.id,\n      title: user.name,\n      avatar: user.avatar_url,\n      supportTypes: ['html'],\n      type: 'yuque',\n      displayName: '语雀',\n      home: 'https://www.yuque.com/dashboard',\n      icon: 'https://gw.alipayobjects.com/zos/rmsportal/UTjFYEzMSYVwzxIGVhMu.png',\n    }\n  }\n\n  async addPost(post) {\n    const postBody = {\n        \"title\": post.post_title,\n        \"type\": \"Doc\",\n        \"format\": \"lake\",\n        \"book_id\": _cachedBook ? _cachedBook : 689837,\n        \"status\": 0\n    }\n    var res = await axios.post('https://www.yuque.com/api/docs', postBody)\n    if (!res.data.data) {\n      throw new Error(res.data.message)\n    }\n    const result = res.data.data\n    return {\n      status: 'success',\n      post_id: result.id,\n    }\n  }\n\n  async editPost(post_id, post) {\n    const content = `${post.post_content}`\n    try {\n      const aslContent = `${content}`;\n      if(!post.markdown) {\n        var turndownService = new turndown()\n        turndownService.use(tools.turndownExt)\n        var markdown = turndownService.turndown(post.post_content)\n        console.log(markdown);\n        post.markdown = markdown\n      }\n      \n      const convertRes = await axios.post(`https://www.yuque.com/api/docs/convert`, {\n        from: 'markdown',\n        to: 'lake',\n\t\t\t\tcontent: post.markdown\n      });\n      \n      const bodyContent = convertRes.data.data.content;\n\t\t\tconst saveRes = await axios.put(`https://www.yuque.com/api/docs/${post_id}/content`, {\n          \"format\": \"lake\",\n          \"body_asl\": bodyContent,\n          \"body\": `<div class=\"lake-content\" typography=\"traditional\">${bodyContent}</div>`,\n          \"body_html\": `<div class=\"lake-content\" typography=\"traditional\">${bodyContent}</div><!8384d2492e7a3edff34dd91c1ee5e056bb6ac85f2aad21b0eaa9188dc6a190a7>`,\n          \"draft_version\": 0,\n          \"sync_dynamic_data\": false,\n          \"save_type\": \"auto\",\n          \"edit_type\": \"Lake\"\n      });\n      return {\n        status: 'success',\n        post_id: post_id,\n        draftLink: `https://www.yuque.com/go/doc/${post_id}/edit`\n      }\n    } catch (e) {\n    \tthrow new Error(e.toString())\n    }\n  }\n\n  async uploadFile(file) {\n    const { src, post_id } = file\n    var uploadUrl = \"https://www.yuque.com/api/upload/attach?attachable_type=Doc&attachable_id=\"+post_id+\"&type=image\"\n    var file = new File([file.bits], 'temp.jpg', {\n      type: file.type\n    });\n    var formdata = new FormData()\n    formdata.append('file', file)\n    var res = await axios({\n      url: uploadUrl,\n      method: 'post',\n      data: formdata,\n      headers: { 'Content-Type': 'multipart/form-data' },\n    })\n\n    if (!res.data.data.attachment_id) {\n      throw new Error('图片上传失败 ' + src)\n    }\n    // http only\n    console.log('uploadFile', res)\n    return [{\n      id: res.data.data.attachment_id,\n      object_key: res.data.data.attachment_id,\n      url: res.data.data.url,\n      images: [\n        res.data.data\n      ]\n    }]\n  }\n\n  async preEditPost(post) {\n    var div = $('<div>')\n    $('body').append(div)\n    div.html(post.content)\n    \n    var doc = div\n\n    tools.doPreFilter(div)\n    tools.processDocCode(div)\n    \n    var pres = doc.find('a')\n    for (let mindex = 0; mindex < pres.length; mindex++) {\n      const pre = pres.eq(mindex)\n      try {\n        pre.after(pre.html()).remove()\n      } catch (e) {}\n    }\n\n    var pres = doc.find('iframe')\n    for (let mindex = 0; mindex < pres.length; mindex++) {\n      const pre = pres.eq(mindex)\n      try {\n        pre.remove()\n      } catch (e) {}\n    }\n\n    try {\n      const images = doc.find('img')\n      for (let index = 0; index < images.length; index++) {\n        const image = images.eq(index)\n        const imgSrc = image.attr('src')\n        if (imgSrc && imgSrc.indexOf('.svg') > -1) {\n          console.log('remove svg Image')\n          image.remove()\n        }\n      }\n      const qqm = doc.find('qqmusic')\n      qqm.next().remove()\n      qqm.remove()\n    } catch (e) {}\n\n    post.content = $('<div>').append(doc.clone()).html()\n    console.log('post', post)\n  }\n  \n  editImg(img, source) {\n    img.attr('class', 'ne-image')\n    img.attr('id', 'u483fad24')\n  }\n}\n\n\n/***/ }),\n\n/***/ 659:\n/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ZhiHuAdapter)\n/* harmony export */ });\n// function escapeHtml(text) {\n//   return text\n//     .replace(/&/g, '&amp;')\n//     .replace(/</g, '&lt;')\n//     .replace(/>/g, '&gt;')\n//     .replace(/\"/g, '&quot;')\n//     .replace(/'/g, '&#039;')\n// }\n\n// function getChildren(obj, count) {\n//   count++\n//   if (count > 4) return null\n//   if (obj.children().length > 1) return obj\n//   return getChildren(obj.children().eq(0), count)\n// }\n\n// function CodeBlockToPlainTextOther(pre) {\n//   var text = []\n//   var minSub = getChildren(pre, 0)\n//   var lines = minSub.children()\n//   for (let index = 0; index < lines.length; index++) {\n//     const element = lines.eq(index)\n//     const codeStr = element.text()\n//     text.push('<code>' + escapeHtml(codeStr) + '</code>')\n//   }\n//   return text.join('\\n')\n// }\n\n// function CodeBlockToPlainText(pre) {\n//   var text = []\n//   var minSub = getChildren(pre, 0)\n//   var lines = pre.find('code')\n//   if (lines.length > 1) {\n//     return CodeBlockToPlainTextOther(pre)\n//   }\n\n//   for (let index = 0; index < lines.length; index++) {\n//     const element = lines.eq(index)\n//     const codeStr = element[0].innerText\n//     console.log('codeStr', codeStr)\n//     var codeLines = codeStr.split('\\n')\n//     codeLines.forEach((codeLine) => {\n//       text.push('<code>' + escapeHtml(codeLine) + '</code>')\n//     })\n//   }\n//   return text.join('\\n')\n// }\n\nclass ZhiHuAdapter {\n  constructor() {\n    // this.skipReadImage = true\n    this.version = '0.0.1'\n    this.name = 'zhihu'\n  }\n\n  async getMetaData() {\n    var res = await $.ajax({\n      url:\n        'https://www.zhihu.com/api/v4/me?include=account_status%2Cis_bind_phone%2Cis_force_renamed%2Cemail%2Crenamed_fullname',\n    })\n    // console.log(res);\n    return {\n      uid: res.uid,\n      title: res.name,\n      avatar: res.avatar_url,\n      supportTypes: ['html'],\n      type: 'zhihu',\n      displayName: '知乎',\n      home: 'https://www.zhihu.com/settings/account',\n      icon: 'https://static.zhihu.com/static/favicon.ico',\n    }\n  }\n\n  async addPost(post) {\n    var res = await $.ajax({\n      url: 'https://zhuanlan.zhihu.com/api/articles/drafts',\n      type: 'POST',\n      dataType: 'JSON',\n      contentType: 'application/json',\n      data: JSON.stringify({\n        title: post.post_title,\n        // content: post.post_content\n      }),\n    })\n    console.log(res)\n    return {\n      status: 'success',\n      post_id: res.id,\n    }\n    //\n  }\n\n  async editPost(post_id, post) {\n    console.log('editPost', post.post_thumbnail)\n    var res = await $.ajax({\n      url: 'https://zhuanlan.zhihu.com/api/articles/' + post_id + '/draft',\n      type: 'PATCH',\n      contentType: 'application/json',\n      data: JSON.stringify({\n        title: post.post_title,\n        content: post.post_content,\n        isTitleImageFullScreen: false,\n        titleImage: 'https://pic1.zhimg.com/' + post.post_thumbnail + '.png',\n      }),\n    })\n\n    return {\n      status: 'success',\n      post_id: post_id,\n      draftLink: 'https://zhuanlan.zhihu.com/p/' + post_id + '/edit',\n    }\n    // https://zhuanlan.zhihu.com/api/articles/68769713/draft\n  }\n\n  untiImageDone(image_id) {\n    return new Promise(function(resolve, reject) {\n      function waitToNext() {\n        console.log('untiImageDone', image_id);\n        (async () => {\n          var imgDetail = await $.ajax({\n            url: 'https://api.zhihu.com/images/' + image_id,\n            type: 'GET',\n          })\n          console.log('imgDetail', imgDetail)\n          if (imgDetail.status != 'processing') {\n            console.log('all done')\n            resolve(imgDetail)\n          } else {\n            // console.log('go next', waitToNext)\n            setTimeout(waitToNext, 300)\n          }\n        })()\n      }\n      waitToNext()\n    })\n  }\n\n  async _uploadFile(file) {\n    var src = file.src\n    var res = await $.ajax({\n      url: 'https://zhuanlan.zhihu.com/api/uploaded_images',\n      type: 'POST',\n      headers: {\n        accept: '*/*',\n        'x-requested-with': 'fetch',\n      },\n      data: {\n        url: src,\n        source: 'article',\n      },\n    })\n\n    return [\n      {\n        id: res.hash,\n        object_key: res.hash,\n        url: res.src,\n      },\n    ]\n  }\n\n  async uploadFile(file) {\n    console.log('ZhiHuDriver.uploadFile', file, md5)\n    var updateData = JSON.stringify({\n      image_hash: md5(file.bits),\n      source: 'article',\n    })\n    console.log('upload', updateData)\n    var fileResp = await $.ajax({\n      url: 'https://api.zhihu.com/images',\n      type: 'POST',\n      dataType: 'JSON',\n      contentType: 'application/json',\n      data: updateData,\n    })\n\n    console.log('upload', fileResp)\n\n    var upload_file = fileResp.upload_file\n    if (fileResp.upload_file.state == 1) {\n      var imgDetail = await this.untiImageDone(upload_file.image_id)\n      console.log('imgDetail', imgDetail)\n      upload_file.object_key = imgDetail.original_hash\n    } else {\n      var token = fileResp.upload_token\n      let client = new OSS({\n        endpoint: 'https://zhihu-pics-upload.zhimg.com',\n        accessKeyId: token.access_id,\n        accessKeySecret: token.access_key,\n        stsToken: token.access_token,\n        cname: true,\n        bucket: 'zhihu-pics',\n      })\n      var finalUrl = await client.put(\n        upload_file.object_key,\n        new Blob([file.bits])\n      )\n      console.log(client, finalUrl)\n    }\n    console.log(file, fileResp)\n\n    if (file.type === 'image/gif') {\n      // add extension for gif\n      upload_file.object_key = upload_file.object_key + '.gif';\n    }\n    return [\n      {\n        id: upload_file.object_key,\n        object_key: upload_file.object_key,\n        url: 'https://pic4.zhimg.com/' + upload_file.object_key,\n        // url: 'https://pic1.zhimg.com/80/' + upload_file.object_key + '_hd.png',\n      },\n    ]\n  }\n\n  async preEditPost(post) {\n    var div = $('<div>')\n    $('body').append(div)\n\n    // post.content = post.content.replace(/\\>\\s+\\</g,'');\n    div.html(post.content)\n\n    // var org = $(post.content);\n    // var doc = $('<div>').append(org.clone());\n    var doc = div\n    // var pres = doc.find('pre')\n    // console.log('find code blocks', pres.length, post)\n    // for (let mindex = 0; mindex < pres.length; mindex++) {\n    //   const pre = pres.eq(mindex)\n    //   try {\n    //     var newHtml = CodeBlockToPlainText(pre, 0)\n    //     if (newHtml) {\n    //       console.log(newHtml)\n    //       pre.html(newHtml)\n    //     }\n    //   } catch (e) {}\n    // }\n    tools.doPreFilter(div)\n    tools.processDocCode(div)\n\n    var removeIfEmpty = function() {\n      var $obj = $(this)\n      var originalText = $obj.text()\n      if (originalText == '') {\n        $obj.remove()\n      }\n    }\n\n    var removeIfNoImageEmpty = function() {\n      var $obj = $(this)\n      var originalText = $obj.text()\n      var img = $obj.find('img')\n      if (originalText == '' && !img.length) {\n        $obj.remove()\n      }\n    }\n\n    var processEmptyLine = function (idx, el) {\n      var $obj = $(this)\n      var originalText = $obj.text()\n      var img = $obj.find('img')\n      var brs = $obj.find('br')\n      if (originalText == '') {\n        ;(function () {\n          if (img.length){\n            console.log('has img skip')\n            return\n          }\n          if (!brs.length) {\n            console.log('no br skip')\n            return\n          }\n          $obj.remove()\n        })()\n      } else {\n        if(originalText.trim() == '') {\n          console.log('processEmptyLine', $obj)\n          $obj.remove()\n        }\n      }\n      // try to replace as h2;\n    }\n\n    var highlightTitle = function() {\n      var strongTag = $obj.find('strong').eq(0)\n      var childStrongText = strongTag.text()\n      var isHead = false\n      if (originalText == childStrongText) {\n        var strongSize = null\n        var tagStart = strongTag\n        var align = null\n        for (let index = 0; index < 4; index++) {\n          var fontSize = tagStart.css('font-size')\n          var textAlign = tagStart.css('text-align')\n          if (fontSize) {\n            strongSize = fontSize\n          }\n          if (textAlign) {\n            align = textAlign\n          }\n          if (align && strongSize) break\n          if (tagStart == $obj) {\n            console.log('near top')\n            break\n          }\n          tagStart = tagStart.parent()\n        }\n        if (strongSize) {\n          var theFontSize = parseInt(strongSize)\n          if (theFontSize > 15 && align == 'center') {\n            // var newTag = $('<h2></h2>').append($obj.html())\n            // $obj.after(newTag).remove()\n            isHead = true;\n          }\n        }\n      }\n      if (isHead) {\n        var NewElement = $(\"<h2 />\");\n        // $.each(this.attributes, function(i, attrib){\n        //   $(NewElement).attr(attrib.name, attrib.value);\n        // });\n        $(this).replaceWith(function () {\n          return $(NewElement).append($obj.text());\n        });\n      }\n    }\n    // doc.find('[data-role=\"outer\"]').children()\n    // doc.find('section').each(removeIfNoImageEmpty)\n\n    // remove empty break line\n    doc.find('section').each(function() {\n      var NewElement = $(\"<div />\");\n      $.each(this.attributes, function(i, attrib){\n        $(NewElement).attr(attrib.name, attrib.value);\n      });\n      // Replace the current element with the new one and carry over the contents\n      $(this).replaceWith(function () {\n        return $(NewElement).append($(this).contents());\n      });\n    });\n\n    doc.find('p').each(processEmptyLine)\n    // doc.find('section').each(processEmptyLine)\n    doc.find('div').each(processEmptyLine)\n    doc.find('div').each(removeIfNoImageEmpty)\n    // doc.find('[powered-by]').each(removeIfEmpty)\n    // doc.find('[data-role=\"paragraph\"]').each(highlightTitle)\n\n    var processBr = function (idx, el) {\n      var $obj = $(this)\n      if (!$obj.next().length) {\n        $obj.remove()\n      }\n    }\n    doc.find('br').each(processBr)\n    // table {\n    //     margin-bottom: 10px;\n    //     border-collapse: collapse;\n    //     display: table;\n    //     width: 100%!important;\n    // }\n    // td, th {\n    //     word-wrap: break-word;\n    //     word-break: break-all;\n    //     padding: 5px 10px;\n    //     border: 1px solid #DDD;\n    // }\n\n    // console.log('found table', doc.find('table'))\n    var tempDoc = $('<div>').append(doc.clone())\n    post.content =\n      tempDoc.children('div').length == 1\n        ? tempDoc.children('div').html()\n        : tempDoc.html()\n    // div.remove();\n    // this.addNotify(post)\n  }\n\n  addPromotion(post) {\n    var sharcode = `<blockquote><p>本文使用 <a href=\"https://zhuanlan.zhihu.com/p/358098152\" class=\"internal\">文章同步助手</a> 同步</p></blockquote>`\n    post.content = post.content.trim() + `${sharcode}`\n  }\n}\n\n\n/***/ }),\n\n/***/ 449:\n/***/ ((module, __unused_webpack_exports, __webpack_require__) => {\n\nvar map = {\n\t\"./163.js\": 150,\n\t\"./51cto.js\": 258,\n\t\"./BaseAdapter.js\": 174,\n\t\"./CSDN.js\": 617,\n\t\"./Cnblog.js\": 308,\n\t\"./Discuz.js\": 159,\n\t\"./Juejin.js\": 341,\n\t\"./Segmentfault.js\": 920,\n\t\"./Weibo.js\": 836,\n\t\"./YiDian.js\": 522,\n\t\"./baijiahao.js\": 386,\n\t\"./bilibili.js\": 536,\n\t\"./dayu.js\": 637,\n\t\"./douban.js\": 247,\n\t\"./focus.js\": 754,\n\t\"./imooc.js\": 355,\n\t\"./ipfs.js\": 839,\n\t\"./jianshu.js\": 172,\n\t\"./oschina.js\": 47,\n\t\"./sohu.js\": 192,\n\t\"./toutiao.js\": 968,\n\t\"./weixin.js\": 476,\n\t\"./wordpress.js\": 402,\n\t\"./xueqiu.js\": 534,\n\t\"./yuque.js\": 301,\n\t\"./zhihu.js\": 659\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = 449;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/make namespace object */\n/******/ \t(() => {\n/******/ \t\t// define __esModule on exports\n/******/ \t\t__webpack_require__.r = (exports) => {\n/******/ \t\t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n/******/ \t\t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n/******/ \t\t\t}\n/******/ \t\t\tObject.defineProperty(exports, '__esModule', { value: true });\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n// This entry need to be wrapped in an IIFE because it need to be in strict mode.\n(() => {\n\"use strict\";\n// ESM COMPAT FLAG\n__webpack_require__.r(__webpack_exports__);\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"addCustomDriver\": () => (/* binding */ addCustomDriver),\n  \"getDriver\": () => (/* binding */ getDriver),\n  \"getMeta\": () => (/* binding */ getMeta),\n  \"getPublicAccounts\": () => (/* binding */ getPublicAccounts)\n});\n\n;// CONCATENATED MODULE: ../@wechatsync/drivers/index.js\nfunction importAll(r) {\n  const modules = {}\n  r.keys().forEach(key => {\n    const module = r(key)\n    const moduleName = module.default ? module.default.name : null\n    if (moduleName && moduleName !== 'BaseAdapter') {\n      modules[moduleName] = module.default\n    }\n    // console.log('moduleName', moduleName)\n  })\n  return modules\n}\n\n/* harmony default export */ const drivers = (importAll(__webpack_require__(449)));\n\n;// CONCATENATED MODULE: ./src/drivers/driver.js\n\n\nconst {\n  JianShuAdapter,\n  ZhiHuAdapter,\n  WordpressAdapter,\n  ToutiaoAdapter,\n  WeiboAdapter,\n  SegmentfaultAdapter,\n  JuejinAdapter,\n  CSDNAdapter,\n  CnblogAdapter,\n  WeixinAdapter,\n  YiDianAdapter,\n  DoubanAdapter,\n  BilibiliAdapter,\n  _51CtoAdapter,\n  FocusAdapter,\n  DiscuzAdapter,\n  SoHuAdapter,\n  BaiJiaHaoAdapter,\n  OsChinaAdapter,\n  DaYuAdapter,\n  ImoocAdapter,\n  YuQueAdapter,\n  XueQiuAdapter,\n  IPFSAdapter,\n} = drivers\n\nvar _cacheState = {}\nconst _customDrivers = {};\n\nfunction addCustomDriver(name, driverClass) {\n  _customDrivers[name] = {\n    name: name,\n    handler: driverClass\n  }\n  console.log('addCustomDriver', _customDrivers)\n}\n\n\nfunction getDriver(account) {\n\n  // 保证在内置的前面\n  if(_customDrivers[account.type]) {\n    const driverInCustom = _customDrivers[account.type]\n    return new driverInCustom['handler'](account)\n  }\n\n  if (account.type == 'wordpress') {\n    return new WordpressAdapter(\n      account.params.wpUrl,\n      account.params.wpUser,\n      account.params.wpPwd\n    )\n  }\n\n  if (account.type == 'zhihu') {\n    return new ZhiHuAdapter()\n  }\n\n  if (account.type == 'dayu') {\n    return new DaYuAdapter(account)\n  }\n\n  if (account.type == 'jianshu') {\n    return new JianShuAdapter()\n  }\n\n  if (account.type == 'typecho') {\n    return new WordpressAdapter(\n      account.params.wpUrl,\n      account.params.wpUser,\n      account.params.wpPwd,\n      true\n    )\n  }\n\n  if (account.type == 'toutiao') {\n    return new ToutiaoAdapter()\n  }\n\n  if (account.type == 'bilibili') {\n    return new BilibiliAdapter({\n      globalState: _cacheState,\n      state: _cacheState[account.type],\n    })\n  }\n\n  if (account.type == 'weibo') {\n    return new WeiboAdapter()\n  }\n\n  if (account.type == 'sohufocus') {\n    return new FocusAdapter()\n  }\n\n  if (account.type == '51cto') {\n    return new _51CtoAdapter()\n  }\n\n  if (account.type == 'segmentfault') {\n    return new SegmentfaultAdapter(account)\n  }\n\n  if (account.type == 'juejin') {\n    return new JuejinAdapter(account)\n  }\n\n  if (account.type == 'csdn') {\n    return new CSDNAdapter(account)\n  }\n\n  if (account.type == 'cnblog') {\n    return new CnblogAdapter(account)\n  }\n  if (account.type == 'weixin') {\n    return new WeixinAdapter(account)\n  }\n\n  if (account.type == 'yidian') {\n    return new YiDianAdapter(account)\n  }\n\n  if (account.type == 'baijiahao') {\n    return new BaiJiaHaoAdapter(account)\n  }\n\n  if(account.type == 'douban') {\n    console.log(account.type)\n    return new DoubanAdapter({\n      globalState: _cacheState,\n      state: _cacheState[account.type],\n    })\n  }\n\n  if(account.type == 'discuz') {\n    console.log('discuz', account)\n    return new DiscuzAdapter(account.config)\n  }\n\n  if (account.type == 'sohu') {\n    return new SoHuAdapter(account)\n  }\n\n  if (account.type == 'oschina') {\n    return new OsChinaAdapter(account)\n  }\n\n  if (account.type == 'imooc') {\n    return new ImoocAdapter(account)\n  }\n\n  if (account.type == 'ipfs') {\n    return new IPFSAdapter(account)\n  }\n\n  if (account.type == 'xueqiu') {\n    return new XueQiuAdapter(account)\n  }\n\n  if (account.type == 'yuque') {\n    return new YuQueAdapter(account)\n  }\n\n  throw Error('not supprt account type')\n}\n\nconst chunk = (arr, size) => Array.from({ length: Math.ceil(arr.length / size) }, (v, i) =>\n  arr.slice(i * size, i * size + size)\n);\n\nlet _cacheUsers = null\nlet _lastFetch = null\n\nasync function getPublicAccounts() {\n\n  // 限制20s 保证不会太频繁请求平台\n  if(_lastFetch != null) {\n    const isTooQuickly = (Date.now() - _lastFetch) < 20 * 1000\n    if (isTooQuickly) {\n      console.log('too quickly return by cache')\n      return _cacheUsers\n    }\n  }\n\n  console.log('getPublicAccounts')\n  var drivers = [\n    new SegmentfaultAdapter(),\n    new CSDNAdapter(),\n    new JuejinAdapter(),\n    new CnblogAdapter(),\n    new WeiboAdapter(),\n    new ZhiHuAdapter(),\n    new JianShuAdapter(),\n    new ToutiaoAdapter(),\n    new WeixinAdapter(),\n    new YiDianAdapter(),\n    new DoubanAdapter(),\n    new BilibiliAdapter(),\n    new _51CtoAdapter(),\n    new FocusAdapter(),\n    new BaiJiaHaoAdapter(),\n    new SoHuAdapter(),\n    new OsChinaAdapter(),\n    new DaYuAdapter(),\n    new ImoocAdapter(),\n    new YuQueAdapter(),\n    new XueQiuAdapter(),\n    new IPFSAdapter(),\n  ]\n\n  var customDiscuzEndpoints = ['https://www.51hanghai.com'];\n  customDiscuzEndpoints.forEach(_ => {\n    drivers.push(new DiscuzAdapter({\n      url: _,\n   }));\n  })\n\n  Object.keys(_customDrivers).forEach(type => {\n    const _customDriver = _customDrivers[type];\n    try {\n      drivers.push(new _customDriver['handler']());\n    } catch (e) {\n      console.log('initlaze custom driver error', e)\n    }\n  });\n\n  var users = []\n\n  const stepItems = chunk(drivers, 20);\n  const startTime = Date.now()\n  for (let index = 0; index < stepItems.length; index++) {\n    try {\n      const stepItem = stepItems[index];\n      const results = await Promise.all(\n        stepItem.map((driver) => {\n          return new Promise((resolve, reject) => {\n            driver.getMetaData().then(resolve, function() {\n              resolve(null)\n            })\n          })\n        })\n      );\n      const successAccounts = results.filter(_ => _)\n      users = users.concat(successAccounts)\n    } catch (e) {\n      console.log(\"chunkPromise\", e);\n    }\n  }\n  // for (let index = 0; index < drivers.length; index++) {\n  //   const driver = drivers[index]\n  //   try {\n  //     var user = await driver.getMetaData()\n  //     users.push(user)\n  //   } catch (e) {\n  //     console.log(e)\n  //   }\n  // }\n  const spend = Date.now() - startTime\n  console.log('getPublicAccounts spend', spend, 'driverCount', drivers.length)\n  _lastFetch = Date.now()\n  _cacheUsers = users\n  return users\n}\n\nfunction getCookie(name, cookieStr) {\n  let arr,\n    reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)')\n  if ((arr = cookieStr.match(reg))) {\n    return unescape(arr[2])\n  } else {\n    return ''\n  }\n}\n\nfunction urlHandler(details) {\n  if (\n    details.url.indexOf('api.bilibili.com') >\n    -1\n  ) {\n    var cookieHeader = details.requestHeaders.filter(h => {\n      return h.name.toLowerCase() == 'cookie'\n    })\n\n    if (cookieHeader.length) {\n      var cookieStr = cookieHeader[0].value\n      var bili_jct = getCookie('bili_jct', cookieStr)\n      if (bili_jct) {\n        _cacheState['bilibili'] = _cacheState['bilibili'] || {};\n        Object.assign(_cacheState['bilibili'], {\n          csrf: bili_jct,\n        })\n        console.log('bili_jct', bili_jct, details)\n      }\n    }\n    // console.log('details.requestHeaders', details)\n  }\n  // https://music.douban.com/subject/24856133/new_review\n  if (\n    details.url.indexOf('music.douban.com') >\n    -1\n    &&\n    details.url.indexOf('/new_review') >\n    -1\n  ) {\n    _cacheState['douban'] = _cacheState['douban'] || {};\n    Object.assign(_cacheState['douban'], {\n      is_review: true,\n      subject: 'music',\n      url: details.url,\n      id: details.url.replace('https://music.douban.com/subject/', '')\n      .replace('/new_review', '')\n    })\n  }\n}\n\nfunction getMeta() {\n  return {\n    version: '0.0.15',\n    versionNumber: 15,\n    log: '',\n    urlHandler: urlHandler,\n    inspectUrls: ['*://api.bilibili.com/*', '*://music.douban.com/*'],\n  }\n}\n\n// DEVTOOL_PLACEHOLDER_INSERT\n\n})();\n\nmodules = __webpack_exports__;\n/******/ })()\n;for (var k in modules) exports[k] = modules[k];"