{"manifest_version": 2, "name": "微信公众号同步助手", "description": "微信公众号文章同步助手，支持头条号、简书、知乎、WordPress", "icons": {"16": "images/logo.png", "48": "images/logo.png", "128": "images/logo.png"}, "version": "1.0.12", "content_security_policy": "script-src 'self' 'unsafe-eval' https://hm.baidu.com https://cdn.authing.cn https://cdn.jsdelivr.net https://www.google.com/ https://ajax.googleapis.com/; object-src 'self'", "browser_action": {"default_icon": "icon.png", "default_popup": "index.html"}, "options_ui": {"page": "index.html#/options", "open_in_tab": false}, "permissions": ["contextMenus", "webRequest", "webRequestBlocking", "storage", "unlimitedStorage", "notifications", "http://*/*", "https://*/*"], "content_scripts": [{"js": ["libs/juqery.js", "libs/Readability.js", "libs/reader.js", "page.js"], "matches": ["http://*/*", "https://*/*"]}, {"js": ["libs/juqery.js", "libs/popper.min.js", "libs/bootstrap.min.js", "content.js"], "css": ["css/boot.css"], "matches": ["http://mp.weixin.qq.com/*", "https://mp.weixin.qq.com/*"], "run_at": "document_end"}, {"js": ["libs/juqery.js", "api.js"], "all_frames": true, "matches": ["http://*/*", "https://*/*"]}, {"js": ["libs/juqery.js", "segmenftfault.js"], "all_frames": true, "matches": ["http://segmentfault.com/*", "https://segmentfault.com/*", "https://mp.toutiao.com/*", "http://mp.toutiao.com/*"], "run_at": "document_end"}], "web_accessible_resources": ["view.html", "templates.html", "autoformat.js", "inject.js", "images/*"], "background": {"scripts": ["libs/juqery.js", "libs/jquery.xmlrpc.min.js", "libs/aliyun-oss-sdk.min.js", "libs/hmac-sha256.js", "libs/enc-base64-min.js", "libs/google-analytics-bundle.js", "background.js"]}}