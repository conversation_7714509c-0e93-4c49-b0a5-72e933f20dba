window.driver = "let modules =/******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, {\n/******/ \t\t\t\tconfigurable: false,\n/******/ \t\t\t\tenumerable: true,\n/******/ \t\t\t\tget: getter\n/******/ \t\t\t});\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 2);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\nfunction extend (destination) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      if (source.hasOwnProperty(key)) destination[key] = source[key];\n    }\n  }\n  return destination\n}\n\nfunction repeat (character, count) {\n  return Array(count + 1).join(character)\n}\n\nvar blockElements = [\n  'address', 'article', 'aside', 'audio', 'blockquote', 'body', 'canvas',\n  'center', 'dd', 'dir', 'div', 'dl', 'dt', 'fieldset', 'figcaption',\n  'figure', 'footer', 'form', 'frameset', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',\n  'header', 'hgroup', 'hr', 'html', 'isindex', 'li', 'main', 'menu', 'nav',\n  'noframes', 'noscript', 'ol', 'output', 'p', 'pre', 'section', 'table',\n  'tbody', 'td', 'tfoot', 'th', 'thead', 'tr', 'ul'\n];\n\nfunction isBlock (node) {\n  return blockElements.indexOf(node.nodeName.toLowerCase()) !== -1\n}\n\nvar voidElements = [\n  'area', 'base', 'br', 'col', 'command', 'embed', 'hr', 'img', 'input',\n  'keygen', 'link', 'meta', 'param', 'source', 'track', 'wbr'\n];\n\nfunction isVoid (node) {\n  return voidElements.indexOf(node.nodeName.toLowerCase()) !== -1\n}\n\nvar voidSelector = voidElements.join();\nfunction hasVoid (node) {\n  return node.querySelector && node.querySelector(voidSelector)\n}\n\nvar rules = {};\n\nrules.paragraph = {\n  filter: 'p',\n\n  replacement: function (content) {\n    return '\\n\\n' + content + '\\n\\n'\n  }\n};\n\nrules.lineBreak = {\n  filter: 'br',\n\n  replacement: function (content, node, options) {\n    return options.br + '\\n'\n  }\n};\n\nrules.heading = {\n  filter: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'],\n\n  replacement: function (content, node, options) {\n    var hLevel = Number(node.nodeName.charAt(1));\n\n    if (options.headingStyle === 'setext' && hLevel < 3) {\n      var underline = repeat((hLevel === 1 ? '=' : '-'), content.length);\n      return (\n        '\\n\\n' + content + '\\n' + underline + '\\n\\n'\n      )\n    } else {\n      return '\\n\\n' + repeat('#', hLevel) + ' ' + content + '\\n\\n'\n    }\n  }\n};\n\nrules.blockquote = {\n  filter: 'blockquote',\n\n  replacement: function (content) {\n    content = content.replace(/^\\n+|\\n+$/g, '');\n    content = content.replace(/^/gm, '> ');\n    return '\\n\\n' + content + '\\n\\n'\n  }\n};\n\nrules.list = {\n  filter: ['ul', 'ol'],\n\n  replacement: function (content, node) {\n    var parent = node.parentNode;\n    if (parent.nodeName === 'LI' && parent.lastElementChild === node) {\n      return '\\n' + content\n    } else {\n      return '\\n\\n' + content + '\\n\\n'\n    }\n  }\n};\n\nrules.listItem = {\n  filter: 'li',\n\n  replacement: function (content, node, options) {\n    content = content\n      .replace(/^\\n+/, '') // remove leading newlines\n      .replace(/\\n+$/, '\\n') // replace trailing newlines with just a single one\n      .replace(/\\n/gm, '\\n    '); // indent\n    var prefix = options.bulletListMarker + '   ';\n    var parent = node.parentNode;\n    if (parent.nodeName === 'OL') {\n      var start = parent.getAttribute('start');\n      var index = Array.prototype.indexOf.call(parent.children, node);\n      prefix = (start ? Number(start) + index : index + 1) + '.  ';\n    }\n    return (\n      prefix + content + (node.nextSibling && !/\\n$/.test(content) ? '\\n' : '')\n    )\n  }\n};\n\nrules.indentedCodeBlock = {\n  filter: function (node, options) {\n    return (\n      options.codeBlockStyle === 'indented' &&\n      node.nodeName === 'PRE' &&\n      node.firstChild &&\n      node.firstChild.nodeName === 'CODE'\n    )\n  },\n\n  replacement: function (content, node, options) {\n    return (\n      '\\n\\n    ' +\n      node.firstChild.textContent.replace(/\\n/g, '\\n    ') +\n      '\\n\\n'\n    )\n  }\n};\n\nrules.fencedCodeBlock = {\n  filter: function (node, options) {\n    return (\n      options.codeBlockStyle === 'fenced' &&\n      node.nodeName === 'PRE' &&\n      node.firstChild &&\n      node.firstChild.nodeName === 'CODE'\n    )\n  },\n\n  replacement: function (content, node, options) {\n    var className = node.firstChild.className || '';\n    var language = (className.match(/language-(\\S+)/) || [null, ''])[1];\n    var code = node.firstChild.textContent;\n\n    var fenceChar = options.fence.charAt(0);\n    var fenceSize = 3;\n    var fenceInCodeRegex = new RegExp('^' + fenceChar + '{3,}', 'gm');\n\n    var match;\n    while ((match = fenceInCodeRegex.exec(code))) {\n      if (match[0].length >= fenceSize) {\n        fenceSize = match[0].length + 1;\n      }\n    }\n\n    var fence = repeat(fenceChar, fenceSize);\n\n    return (\n      '\\n\\n' + fence + language + '\\n' +\n      code.replace(/\\n$/, '') +\n      '\\n' + fence + '\\n\\n'\n    )\n  }\n};\n\nrules.horizontalRule = {\n  filter: 'hr',\n\n  replacement: function (content, node, options) {\n    return '\\n\\n' + options.hr + '\\n\\n'\n  }\n};\n\nrules.inlineLink = {\n  filter: function (node, options) {\n    return (\n      options.linkStyle === 'inlined' &&\n      node.nodeName === 'A' &&\n      node.getAttribute('href')\n    )\n  },\n\n  replacement: function (content, node) {\n    var href = node.getAttribute('href');\n    var title = node.title ? ' \"' + node.title + '\"' : '';\n    return '[' + content + '](' + href + title + ')'\n  }\n};\n\nrules.referenceLink = {\n  filter: function (node, options) {\n    return (\n      options.linkStyle === 'referenced' &&\n      node.nodeName === 'A' &&\n      node.getAttribute('href')\n    )\n  },\n\n  replacement: function (content, node, options) {\n    var href = node.getAttribute('href');\n    var title = node.title ? ' \"' + node.title + '\"' : '';\n    var replacement;\n    var reference;\n\n    switch (options.linkReferenceStyle) {\n      case 'collapsed':\n        replacement = '[' + content + '][]';\n        reference = '[' + content + ']: ' + href + title;\n        break\n      case 'shortcut':\n        replacement = '[' + content + ']';\n        reference = '[' + content + ']: ' + href + title;\n        break\n      default:\n        var id = this.references.length + 1;\n        replacement = '[' + content + '][' + id + ']';\n        reference = '[' + id + ']: ' + href + title;\n    }\n\n    this.references.push(reference);\n    return replacement\n  },\n\n  references: [],\n\n  append: function (options) {\n    var references = '';\n    if (this.references.length) {\n      references = '\\n\\n' + this.references.join('\\n') + '\\n\\n';\n      this.references = []; // Reset references\n    }\n    return references\n  }\n};\n\nrules.emphasis = {\n  filter: ['em', 'i'],\n\n  replacement: function (content, node, options) {\n    if (!content.trim()) return ''\n    return options.emDelimiter + content + options.emDelimiter\n  }\n};\n\nrules.strong = {\n  filter: ['strong', 'b'],\n\n  replacement: function (content, node, options) {\n    if (!content.trim()) return ''\n    return options.strongDelimiter + content + options.strongDelimiter\n  }\n};\n\nrules.code = {\n  filter: function (node) {\n    var hasSiblings = node.previousSibling || node.nextSibling;\n    var isCodeBlock = node.parentNode.nodeName === 'PRE' && !hasSiblings;\n\n    return node.nodeName === 'CODE' && !isCodeBlock\n  },\n\n  replacement: function (content) {\n    if (!content.trim()) return ''\n\n    var delimiter = '`';\n    var leadingSpace = '';\n    var trailingSpace = '';\n    var matches = content.match(/`+/gm);\n    if (matches) {\n      if (/^`/.test(content)) leadingSpace = ' ';\n      if (/`$/.test(content)) trailingSpace = ' ';\n      while (matches.indexOf(delimiter) !== -1) delimiter = delimiter + '`';\n    }\n\n    return delimiter + leadingSpace + content + trailingSpace + delimiter\n  }\n};\n\nrules.image = {\n  filter: 'img',\n\n  replacement: function (content, node) {\n    var alt = node.alt || '';\n    var src = node.getAttribute('src') || '';\n    var title = node.title || '';\n    var titlePart = title ? ' \"' + title + '\"' : '';\n    return src ? '![' + alt + ']' + '(' + src + titlePart + ')' : ''\n  }\n};\n\n/**\n * Manages a collection of rules used to convert HTML to Markdown\n */\n\nfunction Rules (options) {\n  this.options = options;\n  this._keep = [];\n  this._remove = [];\n\n  this.blankRule = {\n    replacement: options.blankReplacement\n  };\n\n  this.keepReplacement = options.keepReplacement;\n\n  this.defaultRule = {\n    replacement: options.defaultReplacement\n  };\n\n  this.array = [];\n  for (var key in options.rules) this.array.push(options.rules[key]);\n}\n\nRules.prototype = {\n  add: function (key, rule) {\n    this.array.unshift(rule);\n  },\n\n  keep: function (filter) {\n    this._keep.unshift({\n      filter: filter,\n      replacement: this.keepReplacement\n    });\n  },\n\n  remove: function (filter) {\n    this._remove.unshift({\n      filter: filter,\n      replacement: function () {\n        return ''\n      }\n    });\n  },\n\n  forNode: function (node) {\n    if (node.isBlank) return this.blankRule\n    var rule;\n\n    if ((rule = findRule(this.array, node, this.options))) return rule\n    if ((rule = findRule(this._keep, node, this.options))) return rule\n    if ((rule = findRule(this._remove, node, this.options))) return rule\n\n    return this.defaultRule\n  },\n\n  forEach: function (fn) {\n    for (var i = 0; i < this.array.length; i++) fn(this.array[i], i);\n  }\n};\n\nfunction findRule (rules, node, options) {\n  for (var i = 0; i < rules.length; i++) {\n    var rule = rules[i];\n    if (filterValue(rule, node, options)) return rule\n  }\n  return void 0\n}\n\nfunction filterValue (rule, node, options) {\n  var filter = rule.filter;\n  if (typeof filter === 'string') {\n    if (filter === node.nodeName.toLowerCase()) return true\n  } else if (Array.isArray(filter)) {\n    if (filter.indexOf(node.nodeName.toLowerCase()) > -1) return true\n  } else if (typeof filter === 'function') {\n    if (filter.call(rule, node, options)) return true\n  } else {\n    throw new TypeError('`filter` needs to be a string, array, or function')\n  }\n}\n\n/**\n * The collapseWhitespace function is adapted from collapse-whitespace\n * by Luc Thevenard.\n *\n * The MIT License (MIT)\n *\n * Copyright (c) 2014 Luc Thevenard <<EMAIL>>\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\n/**\n * collapseWhitespace(options) removes extraneous whitespace from an the given element.\n *\n * @param {Object} options\n */\nfunction collapseWhitespace (options) {\n  var element = options.element;\n  var isBlock = options.isBlock;\n  var isVoid = options.isVoid;\n  var isPre = options.isPre || function (node) {\n    return node.nodeName === 'PRE'\n  };\n\n  if (!element.firstChild || isPre(element)) return\n\n  var prevText = null;\n  var prevVoid = false;\n\n  var prev = null;\n  var node = next(prev, element, isPre);\n\n  while (node !== element) {\n    if (node.nodeType === 3 || node.nodeType === 4) { // Node.TEXT_NODE or Node.CDATA_SECTION_NODE\n      var text = node.data.replace(/[ \\r\\n\\t]+/g, ' ');\n\n      if ((!prevText || / $/.test(prevText.data)) &&\n          !prevVoid && text[0] === ' ') {\n        text = text.substr(1);\n      }\n\n      // `text` might be empty at this point.\n      if (!text) {\n        node = remove(node);\n        continue\n      }\n\n      node.data = text;\n\n      prevText = node;\n    } else if (node.nodeType === 1) { // Node.ELEMENT_NODE\n      if (isBlock(node) || node.nodeName === 'BR') {\n        if (prevText) {\n          prevText.data = prevText.data.replace(/ $/, '');\n        }\n\n        prevText = null;\n        prevVoid = false;\n      } else if (isVoid(node)) {\n        // Avoid trimming space around non-block, non-BR void elements.\n        prevText = null;\n        prevVoid = true;\n      }\n    } else {\n      node = remove(node);\n      continue\n    }\n\n    var nextNode = next(prev, node, isPre);\n    prev = node;\n    node = nextNode;\n  }\n\n  if (prevText) {\n    prevText.data = prevText.data.replace(/ $/, '');\n    if (!prevText.data) {\n      remove(prevText);\n    }\n  }\n}\n\n/**\n * remove(node) removes the given node from the DOM and returns the\n * next node in the sequence.\n *\n * @param {Node} node\n * @return {Node} node\n */\nfunction remove (node) {\n  var next = node.nextSibling || node.parentNode;\n\n  node.parentNode.removeChild(node);\n\n  return next\n}\n\n/**\n * next(prev, current, isPre) returns the next node in the sequence, given the\n * current and previous nodes.\n *\n * @param {Node} prev\n * @param {Node} current\n * @param {Function} isPre\n * @return {Node}\n */\nfunction next (prev, current, isPre) {\n  if ((prev && prev.parentNode === current) || isPre(current)) {\n    return current.nextSibling || current.parentNode\n  }\n\n  return current.firstChild || current.nextSibling || current.parentNode\n}\n\n/*\n * Set up window for Node.js\n */\n\nvar root = (typeof window !== 'undefined' ? window : {});\n\n/*\n * Parsing HTML strings\n */\n\nfunction canParseHTMLNatively () {\n  var Parser = root.DOMParser;\n  var canParse = false;\n\n  // Adapted from https://gist.github.com/1129031\n  // Firefox/Opera/IE throw errors on unsupported types\n  try {\n    // WebKit returns null on unsupported types\n    if (new Parser().parseFromString('', 'text/html')) {\n      canParse = true;\n    }\n  } catch (e) {}\n\n  return canParse\n}\n\nfunction createHTMLParser () {\n  var Parser = function () {};\n\n  {\n    var JSDOM = __webpack_require__(9).JSDOM;\n    Parser.prototype.parseFromString = function (string) {\n      return new JSDOM(string).window.document\n    };\n  }\n  return Parser\n}\n\nvar HTMLParser = canParseHTMLNatively() ? root.DOMParser : createHTMLParser();\n\nfunction RootNode (input) {\n  var root;\n  if (typeof input === 'string') {\n    var doc = htmlParser().parseFromString(\n      // DOM parsers arrange elements in the <head> and <body>.\n      // Wrapping in a custom element ensures elements are reliably arranged in\n      // a single element.\n      '<x-turndown id=\"turndown-root\">' + input + '</x-turndown>',\n      'text/html'\n    );\n    root = doc.getElementById('turndown-root');\n  } else {\n    root = input.cloneNode(true);\n  }\n  collapseWhitespace({\n    element: root,\n    isBlock: isBlock,\n    isVoid: isVoid\n  });\n\n  return root\n}\n\nvar _htmlParser;\nfunction htmlParser () {\n  _htmlParser = _htmlParser || new HTMLParser();\n  return _htmlParser\n}\n\nfunction Node (node) {\n  node.isBlock = isBlock(node);\n  node.isCode = node.nodeName.toLowerCase() === 'code' || node.parentNode.isCode;\n  node.isBlank = isBlank(node);\n  node.flankingWhitespace = flankingWhitespace(node);\n  return node\n}\n\nfunction isBlank (node) {\n  return (\n    ['A', 'TH', 'TD', 'IFRAME', 'SCRIPT', 'AUDIO', 'VIDEO'].indexOf(node.nodeName) === -1 &&\n    /^\\s*$/i.test(node.textContent) &&\n    !isVoid(node) &&\n    !hasVoid(node)\n  )\n}\n\nfunction flankingWhitespace (node) {\n  var leading = '';\n  var trailing = '';\n\n  if (!node.isBlock) {\n    var hasLeading = /^\\s/.test(node.textContent);\n    var hasTrailing = /\\s$/.test(node.textContent);\n    var blankWithSpaces = node.isBlank && hasLeading && hasTrailing;\n\n    if (hasLeading && !isFlankedByWhitespace('left', node)) {\n      leading = ' ';\n    }\n\n    if (!blankWithSpaces && hasTrailing && !isFlankedByWhitespace('right', node)) {\n      trailing = ' ';\n    }\n  }\n\n  return { leading: leading, trailing: trailing }\n}\n\nfunction isFlankedByWhitespace (side, node) {\n  var sibling;\n  var regExp;\n  var isFlanked;\n\n  if (side === 'left') {\n    sibling = node.previousSibling;\n    regExp = / $/;\n  } else {\n    sibling = node.nextSibling;\n    regExp = /^ /;\n  }\n\n  if (sibling) {\n    if (sibling.nodeType === 3) {\n      isFlanked = regExp.test(sibling.nodeValue);\n    } else if (sibling.nodeType === 1 && !isBlock(sibling)) {\n      isFlanked = regExp.test(sibling.textContent);\n    }\n  }\n  return isFlanked\n}\n\nvar reduce = Array.prototype.reduce;\nvar leadingNewLinesRegExp = /^\\n*/;\nvar trailingNewLinesRegExp = /\\n*$/;\nvar escapes = [\n  [/\\\\/g, '\\\\\\\\'],\n  [/\\*/g, '\\\\*'],\n  [/^-/g, '\\\\-'],\n  [/^\\+ /g, '\\\\+ '],\n  [/^(=+)/g, '\\\\$1'],\n  [/^(#{1,6}) /g, '\\\\$1 '],\n  [/`/g, '\\\\`'],\n  [/^~~~/g, '\\\\~~~'],\n  [/\\[/g, '\\\\['],\n  [/\\]/g, '\\\\]'],\n  [/^>/g, '\\\\>'],\n  [/_/g, '\\\\_'],\n  [/^(\\d+)\\. /g, '$1\\\\. ']\n];\n\nfunction TurndownService (options) {\n  if (!(this instanceof TurndownService)) return new TurndownService(options)\n\n  var defaults = {\n    rules: rules,\n    headingStyle: 'setext',\n    hr: '* * *',\n    bulletListMarker: '*',\n    codeBlockStyle: 'indented',\n    fence: '```',\n    emDelimiter: '_',\n    strongDelimiter: '**',\n    linkStyle: 'inlined',\n    linkReferenceStyle: 'full',\n    br: '  ',\n    blankReplacement: function (content, node) {\n      return node.isBlock ? '\\n\\n' : ''\n    },\n    keepReplacement: function (content, node) {\n      return node.isBlock ? '\\n\\n' + node.outerHTML + '\\n\\n' : node.outerHTML\n    },\n    defaultReplacement: function (content, node) {\n      return node.isBlock ? '\\n\\n' + content + '\\n\\n' : content\n    }\n  };\n  this.options = extend({}, defaults, options);\n  this.rules = new Rules(this.options);\n}\n\nTurndownService.prototype = {\n  /**\n   * The entry point for converting a string or DOM node to Markdown\n   * @public\n   * @param {String|HTMLElement} input The string or DOM node to convert\n   * @returns A Markdown representation of the input\n   * @type String\n   */\n\n  turndown: function (input) {\n    if (!canConvert(input)) {\n      throw new TypeError(\n        input + ' is not a string, or an element/document/fragment node.'\n      )\n    }\n\n    if (input === '') return ''\n\n    var output = process.call(this, new RootNode(input));\n    return postProcess.call(this, output)\n  },\n\n  /**\n   * Add one or more plugins\n   * @public\n   * @param {Function|Array} plugin The plugin or array of plugins to add\n   * @returns The Turndown instance for chaining\n   * @type Object\n   */\n\n  use: function (plugin) {\n    if (Array.isArray(plugin)) {\n      for (var i = 0; i < plugin.length; i++) this.use(plugin[i]);\n    } else if (typeof plugin === 'function') {\n      plugin(this);\n    } else {\n      throw new TypeError('plugin must be a Function or an Array of Functions')\n    }\n    return this\n  },\n\n  /**\n   * Adds a rule\n   * @public\n   * @param {String} key The unique key of the rule\n   * @param {Object} rule The rule\n   * @returns The Turndown instance for chaining\n   * @type Object\n   */\n\n  addRule: function (key, rule) {\n    this.rules.add(key, rule);\n    return this\n  },\n\n  /**\n   * Keep a node (as HTML) that matches the filter\n   * @public\n   * @param {String|Array|Function} filter The unique key of the rule\n   * @returns The Turndown instance for chaining\n   * @type Object\n   */\n\n  keep: function (filter) {\n    this.rules.keep(filter);\n    return this\n  },\n\n  /**\n   * Remove a node that matches the filter\n   * @public\n   * @param {String|Array|Function} filter The unique key of the rule\n   * @returns The Turndown instance for chaining\n   * @type Object\n   */\n\n  remove: function (filter) {\n    this.rules.remove(filter);\n    return this\n  },\n\n  /**\n   * Escapes Markdown syntax\n   * @public\n   * @param {String} string The string to escape\n   * @returns A string with Markdown syntax escaped\n   * @type String\n   */\n\n  escape: function (string) {\n    return escapes.reduce(function (accumulator, escape) {\n      return accumulator.replace(escape[0], escape[1])\n    }, string)\n  }\n};\n\n/**\n * Reduces a DOM node down to its Markdown string equivalent\n * @private\n * @param {HTMLElement} parentNode The node to convert\n * @returns A Markdown representation of the node\n * @type String\n */\n\nfunction process (parentNode) {\n  var self = this;\n  return reduce.call(parentNode.childNodes, function (output, node) {\n    node = new Node(node);\n\n    var replacement = '';\n    if (node.nodeType === 3) {\n      replacement = node.isCode ? node.nodeValue : self.escape(node.nodeValue);\n    } else if (node.nodeType === 1) {\n      replacement = replacementForNode.call(self, node);\n    }\n\n    return join(output, replacement)\n  }, '')\n}\n\n/**\n * Appends strings as each rule requires and trims the output\n * @private\n * @param {String} output The conversion output\n * @returns A trimmed version of the ouput\n * @type String\n */\n\nfunction postProcess (output) {\n  var self = this;\n  this.rules.forEach(function (rule) {\n    if (typeof rule.append === 'function') {\n      output = join(output, rule.append(self.options));\n    }\n  });\n\n  return output.replace(/^[\\t\\r\\n]+/, '').replace(/[\\t\\r\\n\\s]+$/, '')\n}\n\n/**\n * Converts an element node to its Markdown equivalent\n * @private\n * @param {HTMLElement} node The node to convert\n * @returns A Markdown representation of the node\n * @type String\n */\n\nfunction replacementForNode (node) {\n  var rule = this.rules.forNode(node);\n  var content = process.call(this, node);\n  var whitespace = node.flankingWhitespace;\n  if (whitespace.leading || whitespace.trailing) content = content.trim();\n  return (\n    whitespace.leading +\n    rule.replacement(content, node, this.options) +\n    whitespace.trailing\n  )\n}\n\n/**\n * Determines the new lines between the current output and the replacement\n * @private\n * @param {String} output The current conversion output\n * @param {String} replacement The string to append to the output\n * @returns The whitespace to separate the current output and the replacement\n * @type String\n */\n\nfunction separatingNewlines (output, replacement) {\n  var newlines = [\n    output.match(trailingNewLinesRegExp)[0],\n    replacement.match(leadingNewLinesRegExp)[0]\n  ].sort();\n  var maxNewlines = newlines[newlines.length - 1];\n  return maxNewlines.length < 2 ? maxNewlines : '\\n\\n'\n}\n\nfunction join (string1, string2) {\n  var separator = separatingNewlines(string1, string2);\n\n  // Remove trailing/leading newlines and replace with separator\n  string1 = string1.replace(trailingNewLinesRegExp, '');\n  string2 = string2.replace(leadingNewLinesRegExp, '');\n\n  return string1 + separator + string2\n}\n\n/**\n * Determines whether an input can be converted\n * @private\n * @param {String|HTMLElement} input Describe this parameter\n * @returns Describe what it returns\n * @type String|Object|Array|Boolean|Number\n */\n\nfunction canConvert (input) {\n  return (\n    input != null && (\n      typeof input === 'string' ||\n      (input.nodeType && (\n        input.nodeType === 1 || input.nodeType === 9 || input.nodeType === 11\n      ))\n    )\n  )\n}\n\n/* harmony default export */ __webpack_exports__[\"a\"] = (TurndownService);\n\n\n/***/ }),\n/* 1 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\nObject.defineProperty(__webpack_exports__, \"__esModule\", { value: true });\n/* harmony export (immutable) */ __webpack_exports__[\"CodeBlockToPlainText\"] = CodeBlockToPlainText;\n/* harmony export (immutable) */ __webpack_exports__[\"processDocCode\"] = processDocCode;\n/* harmony export (immutable) */ __webpack_exports__[\"makeImgVisible\"] = makeImgVisible;\nfunction escapeHtml(text) {\n  return text\n    .replace(/&/g, '&amp;')\n    .replace(/</g, '&lt;')\n    .replace(/>/g, '&gt;')\n    .replace(/\"/g, '&quot;')\n    .replace(/'/g, '&#039;')\n}\n\nfunction getChildren(obj, count) {\n  count++\n  if (count > 4) return null\n  if (obj.children().length > 1) return obj\n  return getChildren(obj.children().eq(0), count)\n}\n\nfunction CodeBlockToPlainTextOther(pre) {\n  var text = []\n  var minSub = getChildren(pre, 0)\n  var lines = minSub.children()\n  for (let index = 0; index < lines.length; index++) {\n    const element = lines.eq(index)\n    const codeStr = element.text()\n    text.push(escapeHtml(codeStr))\n  }\n  return text.join('\\n')\n}\n\nfunction CodeBlockToPlainText(pre) {\n  var text = []\n  var minSub = getChildren(pre, 0)\n  var lines = pre.find('code')\n  if (lines.length > 1) {\n    return CodeBlockToPlainTextOther(pre)\n  }\n\n  for (let index = 0; index < lines.length; index++) {\n    const element = lines.eq(index)\n    const codeStr = element[0].innerText\n    console.log('codeStr', codeStr)\n    var codeLines = codeStr.split('\\n')\n    codeLines.forEach((codeLine) => {\n      text.push('<code>' + escapeHtml(codeLine) + '</code>')\n    })\n  }\n  return text.join('\\n')\n}\n\nfunction processDocCode(div) {\n  var doc = div\n  var pres = doc.find('pre')\n  // console.log(\"find code blocks\", pres.length, post);\n  for (let mindex = 0; mindex < pres.length; mindex++) {\n    const pre = pres.eq(mindex)\n    try {\n      var newHtml = CodeBlockToPlainText(pre, 0)\n      if (newHtml) {\n        console.log('processDocCode', newHtml)\n        pre.html(newHtml)\n      } else {\n        console.log('processDocCode.failed')\n      }\n    } catch (e) {}\n  }\n}\n\nfunction makeImgVisible(doc) {\n  console.log('makeImgVisible')\n  var pres = doc.find('img')\n  for (let mindex = 0; mindex < pres.length; mindex++) {\n    const item = pres.eq(mindex)\n    const src = item.attr('data-src')\n    if (src) {\n      item.attr('src', src)\n    }\n  }\n}\n\n\n/***/ }),\n/* 2 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\nObject.defineProperty(__webpack_exports__, \"__esModule\", { value: true });\n/* harmony export (immutable) */ __webpack_exports__[\"addCustomDriver\"] = addCustomDriver;\n/* harmony export (immutable) */ __webpack_exports__[\"getDriver\"] = getDriver;\n/* harmony export (immutable) */ __webpack_exports__[\"getPublicAccounts\"] = getPublicAccounts;\n/* harmony export (immutable) */ __webpack_exports__[\"getMeta\"] = getMeta;\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__jianshu__ = __webpack_require__(3);\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__zhihu__ = __webpack_require__(4);\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_2__wordpress__ = __webpack_require__(5);\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_3__toutiao__ = __webpack_require__(6);\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_4__weibo__ = __webpack_require__(7);\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_5__segmentfault__ = __webpack_require__(8);\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_6__juejin__ = __webpack_require__(10);\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_7__csdn__ = __webpack_require__(11);\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_8__cnblog__ = __webpack_require__(12);\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_9__weixin__ = __webpack_require__(13);\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_10__yidian__ = __webpack_require__(14);\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_11__douban__ = __webpack_require__(15);\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_12__bilibili__ = __webpack_require__(18);\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_13__51cto__ = __webpack_require__(19);\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_14__focus__ = __webpack_require__(20);\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_15__Discuz__ = __webpack_require__(21);\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nvar _cacheState = {}\r\nconst _customDrivers = [];\r\n\r\nfunction addCustomDriver(name, driverClass) {\r\n  _customDrivers.push({\r\n    name: name,\r\n    handler: driverClass\r\n  })\r\n}\r\n\r\nfunction getDriver(account) {\r\n  if (account.type == 'wordpress') {\r\n    return new __WEBPACK_IMPORTED_MODULE_2__wordpress__[\"a\" /* default */](\r\n      account.params.wpUrl,\r\n      account.params.wpUser,\r\n      account.params.wpPwd\r\n    )\r\n  }\r\n\r\n  if (account.type == 'zhihu') {\r\n    return new __WEBPACK_IMPORTED_MODULE_1__zhihu__[\"a\" /* default */]()\r\n  }\r\n\r\n  if (account.type == 'jianshu') {\r\n    return new __WEBPACK_IMPORTED_MODULE_0__jianshu__[\"a\" /* default */]()\r\n  }\r\n\r\n  if (account.type == 'typecho') {\r\n    return new __WEBPACK_IMPORTED_MODULE_2__wordpress__[\"a\" /* default */](\r\n      account.params.wpUrl,\r\n      account.params.wpUser,\r\n      account.params.wpPwd,\r\n      true\r\n    )\r\n  }\r\n\r\n  if (account.type == 'toutiao') {\r\n    return new __WEBPACK_IMPORTED_MODULE_3__toutiao__[\"a\" /* default */]()\r\n  }\r\n\r\n  if (account.type == 'bilibili') {\r\n    return new __WEBPACK_IMPORTED_MODULE_12__bilibili__[\"a\" /* default */]({\r\n      globalState: _cacheState,\r\n      state: _cacheState[account.type],\r\n    })\r\n  }\r\n\r\n  if (account.type == 'weibo') {\r\n    return new __WEBPACK_IMPORTED_MODULE_4__weibo__[\"a\" /* default */]()\r\n  }\r\n  \r\n  if (account.type == 'sohufocus') {\r\n    return new __WEBPACK_IMPORTED_MODULE_14__focus__[\"a\" /* default */]()\r\n  }\r\n  \r\n  if (account.type == '51cto') {\r\n    return new __WEBPACK_IMPORTED_MODULE_13__51cto__[\"a\" /* default */]()\r\n  }\r\n\r\n  if (account.type == 'segmentfault') {\r\n    return new __WEBPACK_IMPORTED_MODULE_5__segmentfault__[\"a\" /* default */](account)\r\n  }\r\n\r\n  if (account.type == 'juejin') {\r\n    return new __WEBPACK_IMPORTED_MODULE_6__juejin__[\"a\" /* default */](account)\r\n  }\r\n\r\n  if (account.type == 'csdn') {\r\n    return new __WEBPACK_IMPORTED_MODULE_7__csdn__[\"a\" /* default */](account)\r\n  }\r\n\r\n  if (account.type == 'cnblog') {\r\n    return new __WEBPACK_IMPORTED_MODULE_8__cnblog__[\"a\" /* default */](account)\r\n  }\r\n  if (account.type == 'weixin') {\r\n    return new __WEBPACK_IMPORTED_MODULE_9__weixin__[\"a\" /* default */](account)\r\n  }\r\n\r\n  if (account.type == 'yidian') {\r\n    return new __WEBPACK_IMPORTED_MODULE_10__yidian__[\"a\" /* default */](account)\r\n  }\r\n\r\n  if(account.type == 'douban') {\r\n    return new __WEBPACK_IMPORTED_MODULE_11__douban__[\"a\" /* default */]({\r\n      globalState: _cacheState,\r\n      state: _cacheState[account.type],\r\n    })\r\n  }\r\n\r\n  if(account.type == 'discuz') {\r\n    console.log('discuz', account)\r\n    return new __WEBPACK_IMPORTED_MODULE_15__Discuz__[\"a\" /* default */](account.config)\r\n  }\r\n\r\n  const matchedDrivers = _customDrivers.filter(_ => _.name == account.type)\r\n  if(matchedDrivers.length) {\r\n    const driverInCustom = matchedDrivers[0]\r\n    return new driverInCustom['handler'](account)\r\n  }\r\n\r\n  throw Error('not supprt account type')\r\n}\r\n\r\nasync function getPublicAccounts() {\r\n  console.log('getPublicAccounts')\r\n  var drivers = [\r\n    new __WEBPACK_IMPORTED_MODULE_5__segmentfault__[\"a\" /* default */](),\r\n    new __WEBPACK_IMPORTED_MODULE_7__csdn__[\"a\" /* default */](),\r\n    new __WEBPACK_IMPORTED_MODULE_6__juejin__[\"a\" /* default */](),\r\n    new __WEBPACK_IMPORTED_MODULE_8__cnblog__[\"a\" /* default */](),\r\n    new __WEBPACK_IMPORTED_MODULE_4__weibo__[\"a\" /* default */](),\r\n    new __WEBPACK_IMPORTED_MODULE_1__zhihu__[\"a\" /* default */](),\r\n    new __WEBPACK_IMPORTED_MODULE_0__jianshu__[\"a\" /* default */](),\r\n    new __WEBPACK_IMPORTED_MODULE_3__toutiao__[\"a\" /* default */](),\r\n    new __WEBPACK_IMPORTED_MODULE_9__weixin__[\"a\" /* default */](),\r\n    new __WEBPACK_IMPORTED_MODULE_10__yidian__[\"a\" /* default */](),\r\n    new __WEBPACK_IMPORTED_MODULE_11__douban__[\"a\" /* default */](),\r\n    new __WEBPACK_IMPORTED_MODULE_12__bilibili__[\"a\" /* default */](),\r\n    new __WEBPACK_IMPORTED_MODULE_13__51cto__[\"a\" /* default */](),\r\n    new __WEBPACK_IMPORTED_MODULE_14__focus__[\"a\" /* default */](),\r\n  ]\r\n\r\n  var customDiscuzEndpoints = ['https://www.51hanghai.com'];\r\n  customDiscuzEndpoints.forEach(_ => {\r\n    drivers.push(new __WEBPACK_IMPORTED_MODULE_15__Discuz__[\"a\" /* default */]({\r\n      url: _,\r\n   }));\r\n  })\r\n\r\n  for (let index = 0; index < _customDrivers.length; index++) {\r\n    const _customDriver = _customDrivers[index];\r\n    try {\r\n      drivers.push(new _customDriver['handler']());\r\n    } catch (e) {\r\n      console.log('initlaze custom driver error', e)\r\n    }\r\n  }\r\n\r\n  var users = []\r\n  for (let index = 0; index < drivers.length; index++) {\r\n    const driver = drivers[index]\r\n    try {\r\n      var user = await driver.getMetaData()\r\n      users.push(user)\r\n    } catch (e) {\r\n      console.log(e)\r\n    }\r\n  }\r\n  return users\r\n}\r\n\r\nfunction getCookie(name, cookieStr) {\r\n  let arr,\r\n    reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)')\r\n  if ((arr = cookieStr.match(reg))) {\r\n    return unescape(arr[2])\r\n  } else {\r\n    return ''\r\n  }\r\n}\r\n\r\nfunction urlHandler(details) {\r\n  if (\r\n    details.url.indexOf('api.bilibili.com') >\r\n    -1\r\n  ) {\r\n    var cookieHeader = details.requestHeaders.filter(h => {\r\n      return h.name.toLowerCase() == 'cookie'\r\n    })\r\n\r\n    if (cookieHeader.length) {\r\n      var cookieStr = cookieHeader[0].value\r\n      var bili_jct = getCookie('bili_jct', cookieStr)\r\n      if (bili_jct) {\r\n        _cacheState['bilibili'] = _cacheState['bilibili'] || {};\r\n        Object.assign(_cacheState['bilibili'], {\r\n          csrf: bili_jct,\r\n        })\r\n        console.log('bili_jct', bili_jct, details)\r\n      }\r\n    }\r\n    // console.log('details.requestHeaders', details)\r\n  }\r\n  // https://music.douban.com/subject/24856133/new_review\r\n  if (\r\n    details.url.indexOf('music.douban.com') >\r\n    -1\r\n    && \r\n    details.url.indexOf('/new_review') >\r\n    -1\r\n  ) {\r\n    _cacheState['douban'] = _cacheState['douban'] || {};\r\n    Object.assign(_cacheState['douban'], {\r\n      is_review: true,\r\n      subject: 'music',\r\n      url: details.url,\r\n      id: details.url.replace('https://music.douban.com/subject/', '')\r\n      .replace('/new_review', '')\r\n    })\r\n  }\r\n}\r\n\r\nfunction getMeta() {\r\n  return {\r\n    version: '0.0.11',\r\n    versionNumber: 12,\r\n    log: '',\r\n    urlHandler: urlHandler,\r\n    inspectUrls: ['*://api.bilibili.com/*', '*://music.douban.com/*'],\r\n  }\r\n}\r\n\r\n// DEVTOOL_PLACEHOLDER_INSERT\n\n/***/ }),\n/* 3 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\nvar NoteVersionCaches = {}\r\nvar defaultNoteBookId\r\n\r\nclass JianShuDriver {\r\n  constructor() {\r\n    this.name = 'jianshu'\r\n    // chrome.cookies.getAll({ domain: \"zhihu.com\"},  function(cookies){\r\n    //     console.log(cookies)\r\n    // })\r\n  }\r\n\r\n  async getMetaData() {\r\n    var res = await $.ajax({\r\n      url: 'https://www.jianshu.com/settings/basic.json',\r\n    })\r\n    var notebooks = await $.get('https://www.jianshu.com/author/notebooks')\r\n    // console.log(res);\r\n    // https://upload.jianshu.io/users/upload_avatars/12192974/d02c5033-7f82-458f-9b3e-f4c4dbaa1221?imageMogr2/auto-orient/strip|imageView2/1/w/96/h/96\r\n    return {\r\n      uid: res.data.avatar.split('/')[5],\r\n      title: res.data.nickname,\r\n      avatar: res.data.avatar,\r\n      type: 'jianshu',\r\n      displayName: '简书',\r\n      supportTypes: ['html'],\r\n      home: 'https://www.jianshu.com/settings/basic',\r\n      icon: 'https://www.jianshu.com/favicon.ico',\r\n      notebooks: notebooks,\r\n    }\r\n  }\r\n\r\n  async addPost(post) {\r\n    var notebooks = await $.get('https://www.jianshu.com/author/notebooks')\r\n    var firstNoteBook = notebooks[0]\r\n    defaultNoteBookId = firstNoteBook.id\r\n    var res = await $.ajax({\r\n      url: 'https://www.jianshu.com/author/notes',\r\n      type: 'POST',\r\n      dataType: 'JSON',\r\n      headers: {\r\n        accept: 'application/json',\r\n      },\r\n      contentType: 'application/json',\r\n      data: JSON.stringify({\r\n        at_bottom: false,\r\n        notebook_id: firstNoteBook.id,\r\n        title: post.post_title,\r\n      }),\r\n    })\r\n    console.log(res)\r\n    return {\r\n      status: 'success',\r\n      post_id: res.id,\r\n      notebook_id: firstNoteBook.id,\r\n    }\r\n  }\r\n\r\n  async editPost(post_id, post) {\r\n    var cacheVerions = NoteVersionCaches[post_id]\r\n    var notebook_id = post.notebook_id ? post.notebook_id : defaultNoteBookId\r\n\r\n    if (!cacheVerions) {\r\n      var bookNotes = await $.get(\r\n        'https://www.jianshu.com/author/notebooks/' + notebook_id + '/notes'\r\n      )\r\n      var currentNote = bookNotes.filter((t) => {\r\n        return t.id == post_id\r\n      })[0]\r\n\r\n      console.log(post_id, bookNotes)\r\n      NoteVersionCaches[post_id] = currentNote.autosave_control\r\n      NoteVersionCaches[post_id]++\r\n      cacheVerions = NoteVersionCaches[post_id]\r\n    } else {\r\n      NoteVersionCaches[post_id]++\r\n      cacheVerions = NoteVersionCaches[post_id]\r\n    }\r\n\r\n    console.log('currentNote', cacheVerions)\r\n    var requestData = {\r\n      autosave_control: cacheVerions,\r\n    }\r\n\r\n    if (post.post_content) {\r\n      requestData.content = post.post_content\r\n    }\r\n\r\n    if (post_id) {\r\n      requestData.id = post_id\r\n    }\r\n\r\n    if (post.post_title) {\r\n      requestData.title = post.post_title\r\n    }\r\n\r\n    // https://www.jianshu.com/author/notebooks/108908/notes\r\n    var res = await $.ajax({\r\n      url: 'https://www.jianshu.com/author/notes/' + post_id,\r\n      type: 'PUT',\r\n      dataType: 'JSON',\r\n      contentType: 'application/json',\r\n      headers: {\r\n        accept: 'application/json',\r\n      },\r\n      data: JSON.stringify(requestData),\r\n    })\r\n\r\n    return {\r\n      status: 'success',\r\n      notebook_id: notebook_id,\r\n      post_id: post_id,\r\n      draftLink:\r\n        'https://www.jianshu.com/writer#/notebooks/' +\r\n        notebook_id +\r\n        '/notes/' +\r\n        post_id,\r\n    }\r\n  }\r\n\r\n  async uploadFile(file) {\r\n    const tokenReq = await axios.get('https://www.jianshu.com/upload_images/token.json?filename='+ new Date().getTime() +'.png')\r\n    if(tokenReq.data.token) {\r\n      var blob = new Blob([file.bits], {\r\n        type: file.type\r\n      });\r\n      var formdata = new FormData()\r\n      formdata.append('token', tokenReq.data.token)\r\n      formdata.append('key', tokenReq.data.key)\r\n      formdata.append('x:protocol', 'https')\r\n      formdata.append('file', blob, new Date().getTime() + '.jpg')\r\n      var res = await axios({\r\n        url: 'https://upload.qiniup.com/',\r\n        method: 'post',\r\n        data: formdata,\r\n        headers: { 'Content-Type': 'multipart/form-data' },\r\n      })\r\n\r\n      if(!res.data.url) {\r\n        console.log(res.data);\r\n        throw new Error('upload failed')\r\n      }\r\n      var url = res.data.url\r\n      return [\r\n        {\r\n          id: tokenReq.data.key,\r\n          object_key: tokenReq.data.key,\r\n          url: url\r\n        }\r\n      ]\r\n    }\r\n    throw new Error('upload failed')\r\n  }\r\n\r\n  async uploadFileBySrc(file) {\r\n    var src = file.src\r\n    try {\r\n      // jianshu not support webp\r\n      if (src.indexOf('xitu.io') > -1) {\r\n        src = src.replace('webp', 'png')\r\n      }\r\n\r\n      var res = await $.ajax({\r\n        url: 'https://www.jianshu.com/upload_images/fetch',\r\n        type: 'POST',\r\n        contentType: 'application/json',\r\n        xhrFields: {\r\n          withCredentials: true,\r\n        },\r\n        headers: {\r\n          accept: 'application/json',\r\n        },\r\n        data: JSON.stringify({\r\n          url: src,\r\n        }),\r\n      })\r\n\r\n      // http only\r\n      console.log('uploadFile', res)\r\n      return [res]\r\n    } catch (e) {\r\n      console.log('JianShuDriver.uploadFile', e)\r\n      var error = e.responseJSON.error[0].message\r\n      throw new Error(error)\r\n    }\r\n  }\r\n\r\n  async preEditPost(post) {\r\n    var div = $('<div>')\r\n    $('body').append(div)\r\n    div.html(post.content)\r\n    var doc = div\r\n    var processEmptyLine = function (idx, el) {\r\n      var $obj = $(this)\r\n      var originalText = $obj.text()\r\n      var img = $obj.find('img')\r\n      var brs = $obj.find('br')\r\n      if (originalText == '') {\r\n        ;(function () {\r\n          if (img.length) return\r\n          if (!brs.length) return\r\n          $obj.remove()\r\n        })()\r\n      }\r\n\r\n      // try to replace as h2;\r\n      var strongTag = $obj.find('strong').eq(0)\r\n      var childStrongText = strongTag.text()\r\n      if (originalText == childStrongText) {\r\n        var strongSize = null\r\n        var tagStart = strongTag\r\n        var align = null\r\n        for (let index = 0; index < 4; index++) {\r\n          var fontSize = tagStart.css('font-size')\r\n          var textAlign = tagStart.css('text-align')\r\n          if (fontSize) {\r\n            strongSize = fontSize\r\n          }\r\n          if (textAlign) {\r\n            align = textAlign\r\n          }\r\n          if (align && strongSize) break\r\n          if (tagStart == $obj) {\r\n            console.log('near top')\r\n            break\r\n          }\r\n          tagStart = tagStart.parent()\r\n        }\r\n        if (strongSize) {\r\n          var theFontSize = parseInt(strongSize)\r\n          if (theFontSize > 17 && align == 'center') {\r\n            var newTag = $('<h2></h2>').append($obj.html())\r\n            $obj.after(newTag).remove()\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // remove empty break line\r\n    doc.find('p').each(processEmptyLine)\r\n    doc.find('section').each(processEmptyLine)\r\n\r\n    var processBr = function (idx, el) {\r\n      var $obj = $(this)\r\n      if (!$obj.next().length) {\r\n        $obj.remove()\r\n      }\r\n    }\r\n    doc.find('br').each(processBr)\r\n    // table {\r\n    //     margin-bottom: 10px;\r\n    //     border-collapse: collapse;\r\n    //     display: table;\r\n    //     width: 100%!important;\r\n    // }\r\n    // td, th {\r\n    //     word-wrap: break-word;\r\n    //     word-break: break-all;\r\n    //     padding: 5px 10px;\r\n    //     border: 1px solid #DDD;\r\n    // }\r\n\r\n    // console.log('found table', doc.find('table'))\r\n    var tempDoc = $('<div>').append(doc.clone())\r\n    post.content =\r\n      tempDoc.children('div').length == 1\r\n        ? tempDoc.children('div').html()\r\n        : tempDoc.html()\r\n    // div.remove();\r\n  }\r\n}\n/* harmony export (immutable) */ __webpack_exports__[\"a\"] = JianShuDriver;\n\r\n\n\n/***/ }),\n/* 4 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\nfunction escapeHtml(text) {\r\n  return text\r\n    .replace(/&/g, '&amp;')\r\n    .replace(/</g, '&lt;')\r\n    .replace(/>/g, '&gt;')\r\n    .replace(/\"/g, '&quot;')\r\n    .replace(/'/g, '&#039;')\r\n}\r\n\r\nfunction getChildren(obj, count) {\r\n  count++\r\n  if (count > 4) return null\r\n  if (obj.children().length > 1) return obj\r\n  return getChildren(obj.children().eq(0), count)\r\n}\r\n\r\nfunction CodeBlockToPlainTextOther(pre) {\r\n  var text = []\r\n  var minSub = getChildren(pre, 0)\r\n  var lines = minSub.children()\r\n  for (let index = 0; index < lines.length; index++) {\r\n    const element = lines.eq(index)\r\n    const codeStr = element.text()\r\n    text.push('<code>' + escapeHtml(codeStr) + '</code>')\r\n  }\r\n  return text.join('\\n')\r\n}\r\n\r\nfunction CodeBlockToPlainText(pre) {\r\n  var text = []\r\n  var minSub = getChildren(pre, 0)\r\n  var lines = pre.find('code')\r\n  if (lines.length > 1) {\r\n    return CodeBlockToPlainTextOther(pre)\r\n  }\r\n\r\n  for (let index = 0; index < lines.length; index++) {\r\n    const element = lines.eq(index)\r\n    const codeStr = element[0].innerText\r\n    console.log('codeStr', codeStr)\r\n    var codeLines = codeStr.split('\\n')\r\n    codeLines.forEach((codeLine) => {\r\n      text.push('<code>' + escapeHtml(codeLine) + '</code>')\r\n    })\r\n  }\r\n  return text.join('\\n')\r\n}\r\n\r\nclass ZhiHuDriver {\r\n  constructor() {\r\n    // this.skipReadImage = true\r\n    this.version = '0.0.1'\r\n    this.name = 'zhihu'\r\n  }\r\n\r\n  async getMetaData() {\r\n    var res = await $.ajax({\r\n      url:\r\n        'https://www.zhihu.com/api/v4/me?include=account_status%2Cis_bind_phone%2Cis_force_renamed%2Cemail%2Crenamed_fullname',\r\n    })\r\n    // console.log(res);\r\n    return {\r\n      uid: res.uid,\r\n      title: res.name,\r\n      avatar: res.avatar_url,\r\n      supportTypes: ['html'],\r\n      type: 'zhihu',\r\n      displayName: '知乎',\r\n      home: 'https://www.zhihu.com/settings/account',\r\n      icon: 'https://static.zhihu.com/static/favicon.ico',\r\n    }\r\n  }\r\n\r\n  async addPost(post) {\r\n    var res = await $.ajax({\r\n      url: 'https://zhuanlan.zhihu.com/api/articles/drafts',\r\n      type: 'POST',\r\n      dataType: 'JSON',\r\n      contentType: 'application/json',\r\n      data: JSON.stringify({\r\n        title: post.post_title,\r\n        // content: post.post_content\r\n      }),\r\n    })\r\n    console.log(res)\r\n    return {\r\n      status: 'success',\r\n      post_id: res.id,\r\n    }\r\n    //\r\n  }\r\n\r\n  async editPost(post_id, post) {\r\n    console.log('editPost', post.post_thumbnail)\r\n    var res = await $.ajax({\r\n      url: 'https://zhuanlan.zhihu.com/api/articles/' + post_id + '/draft',\r\n      type: 'PATCH',\r\n      contentType: 'application/json',\r\n      data: JSON.stringify({\r\n        title: post.post_title,\r\n        content: post.post_content,\r\n        isTitleImageFullScreen: false,\r\n        titleImage: 'https://pic1.zhimg.com/' + post.post_thumbnail + '.png',\r\n      }),\r\n    })\r\n\r\n    return {\r\n      status: 'success',\r\n      post_id: post_id,\r\n      draftLink: 'https://zhuanlan.zhihu.com/p/' + post_id + '/edit',\r\n    }\r\n    // https://zhuanlan.zhihu.com/api/articles/68769713/draft\r\n  }\r\n\r\n  untiImageDone(image_id) {\r\n    return new Promise(function(resolve, reject) {\r\n      function waitToNext() {\r\n        console.log('untiImageDone', image_id);\r\n        (async () => {\r\n          var imgDetail = await $.ajax({\r\n            url: 'https://api.zhihu.com/images/' + image_id,\r\n            type: 'GET',\r\n          })\r\n          console.log('imgDetail', imgDetail)\r\n          if (imgDetail.status != 'processing') {\r\n            console.log('all done')\r\n            resolve(imgDetail)\r\n          } else {\r\n            // console.log('go next', waitToNext)\r\n            setTimeout(waitToNext, 300)\r\n          }\r\n        })()\r\n      }\r\n      waitToNext()\r\n    })\r\n  }\r\n\r\n  async _uploadFile(file) {\r\n    var src = file.src\r\n    var res = await $.ajax({\r\n      url: 'https://zhuanlan.zhihu.com/api/uploaded_images',\r\n      type: 'POST',\r\n      headers: {\r\n        accept: '*/*',\r\n        'x-requested-with': 'fetch',\r\n      },\r\n      data: {\r\n        url: src,\r\n        source: 'article',\r\n      },\r\n    })\r\n\r\n    return [\r\n      {\r\n        id: res.hash,\r\n        object_key: res.hash,\r\n        url: res.src,\r\n      },\r\n    ]\r\n  }\r\n\r\n  async uploadFile(file) {\r\n    console.log('ZhiHuDriver.uploadFile', file, md5)\r\n    var updateData = JSON.stringify({\r\n      image_hash: md5(file.bits),\r\n      source: 'article',\r\n    })\r\n    console.log('upload', updateData)\r\n    var fileResp = await $.ajax({\r\n      url: 'https://api.zhihu.com/images',\r\n      type: 'POST',\r\n      dataType: 'JSON',\r\n      contentType: 'application/json',\r\n      data: updateData,\r\n    })\r\n\r\n    console.log('upload', fileResp)\r\n\r\n    var upload_file = fileResp.upload_file\r\n    if (fileResp.upload_file.state == 1) {\r\n      var imgDetail = await this.untiImageDone(upload_file.image_id)\r\n      console.log('imgDetail', imgDetail)\r\n      upload_file.object_key = imgDetail.original_hash\r\n    } else {\r\n      var token = fileResp.upload_token\r\n      let client = new OSS({\r\n        endpoint: 'https://zhihu-pics-upload.zhimg.com',\r\n        accessKeyId: token.access_id,\r\n        accessKeySecret: token.access_key,\r\n        stsToken: token.access_token,\r\n        cname: true,\r\n        bucket: 'zhihu-pics',\r\n      })\r\n      var finalUrl = await client.put(\r\n        upload_file.object_key,\r\n        new Blob([file.bits])\r\n      )\r\n      console.log(client, finalUrl)\r\n    }\r\n    console.log(file, fileResp)\r\n\r\n    if (file.type === 'image/gif') {\r\n      // add extension for gif\r\n      upload_file.object_key = upload_file.object_key + '.gif';\r\n    }\r\n    return [\r\n      {\r\n        id: upload_file.object_key,\r\n        object_key: upload_file.object_key,\r\n        url: 'https://pic4.zhimg.com/' + upload_file.object_key,\r\n        // url: 'https://pic1.zhimg.com/80/' + upload_file.object_key + '_hd.png',\r\n      },\r\n    ]\r\n  }\r\n\r\n  async preEditPost(post) {\r\n    var div = $('<div>')\r\n    $('body').append(div)\r\n\r\n    // post.content = post.content.replace(/\\>\\s+\\</g,'');\r\n    div.html(post.content)\r\n\r\n    // var org = $(post.content);\r\n    // var doc = $('<div>').append(org.clone());\r\n    var doc = div\r\n    var pres = doc.find('pre')\r\n    console.log('find code blocks', pres.length, post)\r\n    for (let mindex = 0; mindex < pres.length; mindex++) {\r\n      const pre = pres.eq(mindex)\r\n      try {\r\n        var newHtml = CodeBlockToPlainText(pre, 0)\r\n        if (newHtml) {\r\n          console.log(newHtml)\r\n          pre.html(newHtml)\r\n        }\r\n      } catch (e) {}\r\n    }\r\n\r\n    var processEmptyLine = function (idx, el) {\r\n      var $obj = $(this)\r\n      var originalText = $obj.text()\r\n      var img = $obj.find('img')\r\n      var brs = $obj.find('br')\r\n      if (originalText == '') {\r\n        ;(function () {\r\n          if (img.length) return\r\n          if (!brs.length) return\r\n          $obj.remove()\r\n        })()\r\n      }\r\n\r\n      // try to replace as h2;\r\n      var strongTag = $obj.find('strong').eq(0)\r\n      var childStrongText = strongTag.text()\r\n      if (originalText == childStrongText) {\r\n        var strongSize = null\r\n        var tagStart = strongTag\r\n        var align = null\r\n        for (let index = 0; index < 4; index++) {\r\n          var fontSize = tagStart.css('font-size')\r\n          var textAlign = tagStart.css('text-align')\r\n          if (fontSize) {\r\n            strongSize = fontSize\r\n          }\r\n          if (textAlign) {\r\n            align = textAlign\r\n          }\r\n          if (align && strongSize) break\r\n          if (tagStart == $obj) {\r\n            console.log('near top')\r\n            break\r\n          }\r\n          tagStart = tagStart.parent()\r\n        }\r\n        if (strongSize) {\r\n          var theFontSize = parseInt(strongSize)\r\n          if (theFontSize > 17 && align == 'center') {\r\n            var newTag = $('<h2></h2>').append($obj.html())\r\n            $obj.after(newTag).remove()\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // remove empty break line\r\n    doc.find('p').each(processEmptyLine)\r\n    doc.find('section').each(processEmptyLine)\r\n\r\n    var processBr = function (idx, el) {\r\n      var $obj = $(this)\r\n      if (!$obj.next().length) {\r\n        $obj.remove()\r\n      }\r\n    }\r\n    doc.find('br').each(processBr)\r\n    // table {\r\n    //     margin-bottom: 10px;\r\n    //     border-collapse: collapse;\r\n    //     display: table;\r\n    //     width: 100%!important;\r\n    // }\r\n    // td, th {\r\n    //     word-wrap: break-word;\r\n    //     word-break: break-all;\r\n    //     padding: 5px 10px;\r\n    //     border: 1px solid #DDD;\r\n    // }\r\n\r\n    // console.log('found table', doc.find('table'))\r\n    var tempDoc = $('<div>').append(doc.clone())\r\n    post.content =\r\n      tempDoc.children('div').length == 1\r\n        ? tempDoc.children('div').html()\r\n        : tempDoc.html()\r\n    // div.remove();\r\n  }\r\n}\n/* harmony export (immutable) */ __webpack_exports__[\"a\"] = ZhiHuDriver;\n\r\n\n\n/***/ }),\n/* 5 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\nfunction xmlrpcWrapper(conf) {\r\n  return new Promise((resolve, reject) => {\r\n    $.xmlrpc(conf).then(\r\n      function (response, status, xhr) {\r\n        resolve({\r\n          response,\r\n          status,\r\n          xhr,\r\n        })\r\n      },\r\n      function (jqXHR, status, error) {\r\n        reject({\r\n          jqXHR,\r\n          status,\r\n          error,\r\n        })\r\n      }\r\n    )\r\n  })\r\n}\r\n\r\nclass WordpressDriver {\r\n  constructor(url, user, pwd, isTypecho) {\r\n    this.url = url\r\n    this.user = user\r\n    this.pwd = pwd\r\n    this.isTypecho = isTypecho\r\n  }\r\n\r\n  getRPC() {\r\n    var endPoint = this.url + '/xmlrpc.php'\r\n    if (this.isTypecho) {\r\n      endPoint = this.url + '/action/xmlrpc'\r\n    }\r\n    return endPoint\r\n  }\r\n\r\n  async getMetaData() {\r\n    var params = [this.user, this.pwd]\r\n    var res = await xmlrpcWrapper({\r\n      url: this.getRPC(),\r\n      methodName: 'wp.getUsersBlogs',\r\n      params: params,\r\n    })\r\n    console.log('end');\r\n    res.icon = chrome.extension.getURL('images/wordpress.ico')\r\n    return res\r\n  }\r\n\r\n  addPost(post) {\r\n    if (this.isTypecho) {\r\n      return {\r\n        status: 'success',\r\n        post_id: '1',\r\n      }\r\n    }\r\n\r\n    var params = [0, this.user, this.pwd, post]\r\n    var end = this.url\r\n    return xmlrpcWrapper({\r\n      url: this.getRPC(),\r\n      methodName: 'wp.newPost',\r\n      params: params,\r\n    })\r\n  }\r\n\r\n  editPost(post_id, post) {\r\n    var params = [0, this.user, this.pwd, post]\r\n    var endpoint = this.getRPC()\r\n    var isTypecho = this.isTypecho\r\n    if (isTypecho) {\r\n      params.push(false)\r\n      params[3] = {\r\n        title: post['post_title'],\r\n        // text: \"!!!\\n\" + post['post_content'].trim() + \"\\n!!!\",\r\n        description: post['post_content'].trim(),\r\n        // markdown: 1\r\n      }\r\n      console.log('params', params)\r\n    } else {\r\n      params[3] = post_id\r\n      params.push(post)\r\n    }\r\n    return new Promise((resolve, reject) => {\r\n      ;(async () => {\r\n        try {\r\n          var res = await xmlrpcWrapper({\r\n            url: endpoint,\r\n            methodName: isTypecho ? 'metaWeblog.newPost' : 'wp.editPost',\r\n            params: params,\r\n          })\r\n\r\n          res.draftLink = this.url + '?p=' + post_id\r\n          console.log('Wordpress', res)\r\n          resolve(res)\r\n        } catch (e) {\r\n          reject(e)\r\n        }\r\n      })()\r\n    })\r\n  }\r\n\r\n  //  'metaWeblog.getPost' => array($this, 'mwGetPost'),\r\n\r\n  editImg(img, source) {\r\n    // img.attr('web_uri', source.images[0].origin_web_uri)\r\n    img.removeAttr('data-src');\r\n  }\r\n\r\n  uploadFile(file) {\r\n    if (this.isTypecho) {\r\n      file['bytes'] = file['bits']\r\n      delete file['bits']\r\n    }\r\n    var params = [0, this.user, this.pwd, file]\r\n\r\n    var end = this.url\r\n    return $.xmlrpc({\r\n      url: this.getRPC(),\r\n      methodName: 'wp.uploadFile',\r\n      params: params,\r\n    })\r\n  }\r\n}\n/* harmony export (immutable) */ __webpack_exports__[\"a\"] = WordpressDriver;\n\r\n\r\nwindow.WordpressDriver = WordpressDriver\r\n\n\n/***/ }),\n/* 6 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n// source: 0\r\n// content: <p>testaaa</p>\r\n// title: test\r\n// search_creation_info: {\"searchTopOne\":0,\"abstract\":\"\"}\r\n// title_id: 1607584898506_1559572462858242\r\n// extra: {\"content_word_cnt\":7,\"gd_ext\":{\"entrance\":\"hotspots\",\"from_page\":\"publisher_mp\",\"enter_from\":\"PC\",\"device_platform\":\"mp\",\"is_message\":0}}\r\n// mp_editor_stat: {\"a_justify\":1}\r\n// educluecard: \r\n// draft_form_data: {\"coverType\":2}\r\n// pgc_feed_covers: []\r\n// claim_origin: 0\r\n// origin_debut_check_pgc_normal: 0\r\n// is_fans_article: 0\r\n// govern_forward: 0\r\n// praise: 0\r\n// disable_praise: 0\r\n// article_ad_type: 2\r\n// tree_plan_article: 0\r\n// activity_tag: 0\r\n// trends_writing_tag: 0\r\n// community_sync: 0\r\n// is_refute_rumor: 0\r\n// save: 0\r\n// timer_status: 0\r\n// timer_time: \r\n\r\nclass ToutiaoDriver {\r\n  constructor() {\r\n    // this.skipReadImage = true\r\n    this.name = 'toutiao'\r\n  }\r\n\r\n  async getMetaData() {\r\n    var res = await $.ajax({\r\n      url: 'https://mp.toutiao.com/get_media_info/',\r\n    })\r\n    // console.log(res);\r\n    return {\r\n      uid: res.data.user.id,\r\n      title: res.data.user.screen_name,\r\n      avatar: res.data.user.https_avatar_url,\r\n      supportTypes: ['html'],\r\n      type: 'toutiao',\r\n      displayName: '头条',\r\n      home: 'https://mp.toutiao.com/profile_v3/graphic/publish',\r\n      icon: 'https://sf1-ttcdn-tos.pstatp.com/obj/ttfe/pgcfe/sz/mp_logo.png',\r\n    }\r\n  }\r\n\r\n  async addPost(post) {\r\n    return {\r\n      status: 'success',\r\n      post_id: 0,\r\n    }\r\n  }\r\n\r\n  async editPost(post_id, post) {\r\n    var pgc_feed_covers = []\r\n    if (post.post_thumbnail_raw && post.post_thumbnail_raw.images) {\r\n      pgc_feed_covers.push({\r\n        id: 0,\r\n        url: post.post_thumbnail_raw.url,\r\n        uri: post.post_thumbnail_raw.images[0].origin_web_uri,\r\n        origin_uri: post.post_thumbnail_raw.images[0].origin_web_uri,\r\n        ic_uri: '',\r\n        thumb_width: post.post_thumbnail_raw.images[0].width,\r\n        thumb_height: post.post_thumbnail_raw.images[0].height,\r\n      })\r\n    }\r\n\r\n    await $.get('https://mp.toutiao.com/profile_v3/graphic/publish')\r\n\r\n    var res = await $.ajax({\r\n      // url:'https://mp.toutiao.com/core/article/edit_article_post/?source=mp&type=article',\r\n      url: 'https://mp.toutiao.com/mp/agw/article/publish?source=mp&type=article',\r\n      type: 'POST',\r\n      dataType: 'JSON',\r\n      data: {\r\n        title: post.post_title,\r\n        article_ad_type: 2,\r\n        article_type: 0,\r\n        from_diagnosis: 0,\r\n        origin_debut_check_pgc_normal: 0,\r\n        tree_plan_article: 0,\r\n        save: 0,\r\n        pgc_id: 0,\r\n        content: post.post_content,\r\n        pgc_feed_covers: JSON.stringify(pgc_feed_covers),\r\n      },\r\n    })\r\n\r\n    if (!res.data) {\r\n      throw new Error(res.message)\r\n    }\r\n\r\n    return {\r\n      status: 'success',\r\n      post_id: res.data.pgc_id,\r\n      draftLink:\r\n        'https://mp.toutiao.com/profile_v3/graphic/publish?pgc_id=' +\r\n        res.data.pgc_id,\r\n    }\r\n  }\r\n\r\n  async uploadFileBySrc(file) {\r\n    var src = file.src\r\n    var res = await $.ajax({\r\n      url: 'https://mp.toutiao.com/tools/catch_picture/',\r\n      type: 'POST',\r\n      headers: {\r\n        accept: '*/*',\r\n      },\r\n      data: {\r\n        upfile: src,\r\n        version: 2,\r\n      },\r\n    })\r\n\r\n    // throw new Error('fuck');\r\n    if (res.images && !res.images.length) {\r\n      throw new Error('图片上传失败 ' + src)\r\n    }\r\n\r\n    // http only\r\n    console.log('uploadFile', res)\r\n    return [res]\r\n  }\r\n\r\n  async uploadFile(file) {\r\n    var src = file.src\r\n    var uploadUrl = 'https://mp.toutiao.com/mp/agw/article_material/photo/upload_picture?type=ueditor&pgc_watermark=1&action=uploadimage&encode=utf-8'\r\n    // var blob = new Blob([file.bits], {\r\n    //   type: file.type\r\n    // });\r\n    var file = new File([file.bits], 'temp', {\r\n      type: file.type\r\n    });\r\n    var formdata = new FormData()\r\n    formdata.append('upfile', file)\r\n    var res = await axios({\r\n      url: uploadUrl,\r\n      method: 'post',\r\n      data: formdata,\r\n      headers: { 'Content-Type': 'multipart/form-data' },\r\n    })\r\n\r\n    if (res.data.state != 'SUCCESS') {\r\n      throw new Error('图片上传失败 ' + src)\r\n    }\r\n    // http only\r\n    console.log('uploadFile', res)\r\n    return [{\r\n      id: res.data.original,\r\n      object_key: res.data.original,\r\n      url: res.data.url,\r\n      images: [\r\n        res.data\r\n      ]\r\n    }]\r\n  }\r\n\r\n  async preEditPost(post) {\r\n    var div = $('<div>')\r\n    $('body').append(div)\r\n\r\n    div.html(post.content)\r\n\r\n    // var org = $(post.content);\r\n    // var doc = $('<div>').append(org.clone());\r\n\r\n    var doc = div\r\n    var pres = doc.find('a')\r\n    for (let mindex = 0; mindex < pres.length; mindex++) {\r\n      const pre = pres.eq(mindex)\r\n      try {\r\n        pre.after(pre.html()).remove()\r\n      } catch (e) {}\r\n    }\r\n\r\n    var pres = doc.find('iframe')\r\n    for (let mindex = 0; mindex < pres.length; mindex++) {\r\n      const pre = pres.eq(mindex)\r\n      try {\r\n        pre.remove()\r\n      } catch (e) {}\r\n    }\r\n\r\n    try {\r\n      const images = doc.find('img')\r\n      for (let index = 0; index < images.length; index++) {\r\n        const image = images.eq(index)\r\n        const imgSrc = image.attr('src')\r\n        if (imgSrc && imgSrc.indexOf('.svg') > -1) {\r\n          console.log('remove svg Image')\r\n          image.remove()\r\n        }\r\n      }\r\n      const qqm = doc.find('qqmusic')\r\n      qqm.next().remove()\r\n      qqm.remove()\r\n    } catch (e) {}\r\n\r\n    post.content = $('<div>').append(doc.clone()).html()\r\n    console.log('post', post)\r\n  }\r\n\r\n  editImg(img, source) {\r\n    img.attr('web_uri', source.images[0].origin_web_uri)\r\n  }\r\n  //   <img class=\"\" src=\"http://p2.pstatp.com/large/pgc-image/bc0a9fc8e595453083d85deb947c3d6e\" data-ic=\"false\" data-ic-uri=\"\" data-height=\"1333\" data-width=\"1000\" image_type=\"1\" web_uri=\"pgc-image/bc0a9fc8e595453083d85deb947c3d6e\" img_width=\"1000\" img_height=\"1333\"></img>\r\n}\n/* harmony export (immutable) */ __webpack_exports__[\"a\"] = ToutiaoDriver;\n\r\n\n\n/***/ }),\n/* 7 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\nvar cacheWeiboUser = null\n// var Readability = require(\"../reader/Readability\");\n\n// fetch(\"https://card.weibo.com/article/v3/aj/editor/draft/save?uid=1820387812&id=402\", { \"credentials\": \"include\", \"headers\": { \"accept\": \"application/json, text/plain, */*\", \"accept-language\": \"zh-CN,zh;q=0.9\", \"content-type\": \"application/x-www-form-urlencoded\" }, \"referrer\": \"https://card.weibo.com/article/v3/editor\", \"referrerPolicy\": \"no-referrer-when-downgrade\", \"body\": \"id=402&title=aaaaaaaaaaa&updated=2019-10-10%2016%3A06%3A43&subtitle=&type=&status=0&publish_at=&error_msg=&error_code=0&collection=%5B%5D&free_content=&content=%3Cp%20align%3D%22justify%22%3Eaaaaaaaaaaaaa%3C%2Fp%3E&cover=https%3A%2F%2Fwx3.sinaimg.cn%2Flarge%2F6c80e9e4ly1g7t62jq7uzj202s01kdfz.jpg&summary=aaa&writer=&extra=null&is_word=0&article_recommend=%5B%5D&follow_to_read=1&isreward=1&pay_setting=%7B%22ispay%22%3A0%2C%22isvclub%22%3A0%7D&source=0&action=1&save=1\", \"method\": \"POST\", \"mode\": \"cors\" });\n\nclass WeiboDriver {\n  constructor() {\n    this.name = 'weibo'\n  }\n\n  async getMetaData() {\n    var html = await $.get('https://card.weibo.com/article/v3/editor')\n    var configIndx = html.indexOf('$CONFIG')\n    var lastIndex = html.indexOf('</script>', configIndx)\n    var configStr = html.substring(configIndx - 12, lastIndex)\n\n    if (configStr.indexOf('CONFIG') > -1) {\n      var res = new Function(configStr + ' return $CONFIG;')()\n      cacheWeiboUser = res\n      return {\n        uid: res.uid,\n        title: res.nick,\n        avatar: res.avatar_large,\n        supportTypes: ['html'],\n        displayName: '微博',\n        type: 'weibo',\n        home: 'https://card.weibo.com/article/v3/editor',\n        icon: 'https://weibo.com/favicon.ico',\n      }\n    } else {\n      throw new Error('not found')\n    }\n  }\n\n  async addPost(post) {\n    var res = await $.post(\n      'https://card.weibo.com/article/v3/aj/editor/draft/create?uid=' +\n        cacheWeiboUser.uid\n    )\n    if (res.code != 100000) {\n      throw new Error(res.msg)\n      return\n    }\n\n    console.log(res)\n    var post_id = res.data.id\n\n    var res = await $.ajax({\n      url:\n        'https://card.weibo.com/article/v3/aj/editor/draft/save?uid=' +\n        cacheWeiboUser.uid +\n        '&id=' +\n        post_id,\n      type: 'POST',\n      dataType: 'JSON',\n      headers: {\n        accept: 'application/json',\n      },\n      data: {\n        id: post_id,\n        title: post.post_title,\n        subtitle: '',\n        type: '',\n        status: '0',\n        publish_at: '',\n        error_msg: '',\n        error_code: '0',\n        collection: '[]',\n        free_content: '',\n        content: post.post_content,\n        cover: '',\n        summary: '',\n        writer: '',\n        extra: 'null',\n        is_word: '0',\n        article_recommend: '[]',\n        follow_to_read: '1',\n        isreward: '1',\n        pay_setting: '{\"ispay\":0,\"isvclub\":0}',\n        source: '0',\n        action: '1',\n        content_type: '0',\n        save: '1',\n      },\n      // data: {\n      //   id: post_id,\n      //   title: post.post_title,\n      //   status: 0,\n      //   error_code: 0,\n      //   content: post.post_content,\n      //   cover: \"\",\n      //   // summary: 'aaaab',\n      //   writer: \"\",\n      //   is_word: 0,\n      //   article_recommend: [],\n      //   follow_to_read: 1,\n      //   isreward: 1,\n      //   pay_setting: JSON.stringify({ ispay: 0, isvclub: 0 }),\n      //   source: 0,\n      //   action: 1,\n      //   save: 1\n      // }\n    })\n    console.log(res)\n    return {\n      status: 'success',\n      post_id: post_id,\n    }\n  }\n\n  async preEditPost(post) {\n    // var div = $('<div>');\n    // $('body').append(div);\n    // div.html(post.content);\n\n    // // var doc = div;\n    // // doc.clone()\n    // var documentClone = document.cloneNode(true);\n    // var article = new Readability(documentClone).parse();\n\n    // div.remove();\n    // console.log(article);\n    var rexp = new RegExp('>[\\ts ]*<', 'g')\n    var result = post.content.replace(rexp, '><')\n    post.content = result\n  }\n\n  async editPost(post_id, post) {\n    var res = await $.ajax({\n      url:\n        'https://card.weibo.com/article/v3/aj/editor/draft/save?uid=' +\n        cacheWeiboUser.uid +\n        '&id=' +\n        post_id,\n      type: 'POST',\n      dataType: 'JSON',\n      headers: {\n        accept: 'application/json',\n      },\n      data: {\n        id: post_id,\n        title: post.post_title,\n        subtitle: '',\n        type: '',\n        status: '0',\n        publish_at: '',\n        error_msg: '',\n        error_code: '0',\n        collection: '[]',\n        free_content: '',\n        content: post.post_content,\n        cover: post.post_thumbnail_raw ? post.post_thumbnail_raw.url : '',\n        summary: '',\n        writer: '',\n        extra: 'null',\n        is_word: '0',\n        article_recommend: '[]',\n        follow_to_read: '1',\n        isreward: '1',\n        pay_setting: '{\"ispay\":0,\"isvclub\":0}',\n        source: '0',\n        action: '1',\n        content_type: '0',\n        save: '1',\n      },\n      // data: {\n      //   id: post_id,\n      //   title: post.post_title,\n      //   status: 0,\n      //   error_code: 0,\n      //   content: post.post_content,\n      //   cover: post.post_thumbnail_raw ? post.post_thumbnail_raw.url : \"\",\n      //   // summary: 'aaaab',\n      //   writer: \"\",\n      //   is_word: 0,\n      //   article_recommend: [],\n      //   follow_to_read: 1,\n      //   isreward: 1,\n      //   pay_setting: JSON.stringify({ ispay: 0, isvclub: 0 }),\n      //   source: 0,\n      //   action: 1,\n      //   save: 1\n      // }\n    })\n    console.log(res)\n    return {\n      status: 'success',\n      post_id: post_id,\n      draftLink: 'https://card.weibo.com/article/v3/editor#/draft/' + post_id,\n    }\n  }\n\n  untiImageDone(src) {\n    return new Promise((resolve, reject) => {\n      ;(async function loop() {\n        var res = await $.ajax({\n          url:\n            'https://card.weibo.com/article/v3/aj/editor/plugins/asyncimginfo?uid=' +\n            cacheWeiboUser.uid,\n          type: 'POST',\n          headers: {\n            accept: '*/*',\n            'x-requested-with': 'fetch',\n          },\n          data: {\n            'urls[0]': src,\n          },\n        })\n\n        var done = res.data[0].task_status_code == 1\n        if (done) {\n          resolve(res.data[0])\n        } else {\n          setTimeout(loop, 1000)\n        }\n      })()\n    })\n  }\n\n  async uploadFileByUrl(file) {\n    var src = file.src\n    var res = await $.ajax({\n      url:\n        'https://card.weibo.com/article/v3/aj/editor/plugins/asyncuploadimg?uid=' +\n        cacheWeiboUser.uid,\n      type: 'POST',\n      headers: {\n        accept: '*/*',\n        'x-requested-with': 'fetch',\n      },\n      data: {\n        'urls[0]': src,\n      },\n    })\n\n    // https://card.weibo.com/article/v3/aj/editor/plugins/asyncuploadimg?uid=1820387812\n    var imgDetail = await this.untiImageDone(src)\n    return [\n      {\n        id: imgDetail.pid,\n        object_key: imgDetail.pid,\n        url: imgDetail.url,\n      },\n    ]\n  }\n\n  async uploadFile(file) {\n    var blob = new Blob([file.bits])\n    console.log('uploadFile', file, blob)\n    var uploadurl1 = `https://picupload.weibo.com/interface/pic_upload.php?app=miniblog&s=json&p=1&data=1&url=&markpos=1&logo=0&nick=&file_source=4`\n    var uploadurl2 = 'https://picupload.weibo.com/interface/pic_upload.php?app=miniblog&s=json&p=1&data=1&url=&markpos=1&logo=0&nick='\n    var fileResp = await $.ajax({\n      url:\n      uploadurl1,\n      type: 'POST',\n      processData: false,\n      data: new Blob([file.bits]),\n    })\n    console.log(file, fileResp)\n    return [\n      {\n        id: fileResp.data.pics.pic_1.pid,\n        object_key: fileResp.data.pics.pic_1.pid,\n        url:\n          'https://wx3.sinaimg.cn/large/' +\n          fileResp.data.pics.pic_1.pid +\n          '.jpg',\n      },\n    ]\n  }\n}\n/* harmony export (immutable) */ __webpack_exports__[\"a\"] = WeiboDriver;\n\n\n\n/***/ }),\n/* 8 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0_turndown__ = __webpack_require__(0);\nconst { processDocCode, makeImgVisible } = __webpack_require__(1)\r\n\r\n\r\n\r\nclass Segmentfault {\r\n  constructor() {\r\n    this.name = 'segmentfault'\r\n  }\r\n\r\n  async getMetaData() {\r\n    var res = await $.get('https://segmentfault.com/user/settings')\r\n    var parser = new DOMParser()\r\n    var htmlDoc = parser.parseFromString(res, 'text/html')\r\n    var link = htmlDoc.getElementsByClassName('user-avatar')[0]\r\n    if (!link) {\r\n      throw Error('not found')\r\n    }\r\n\r\n    var uid = link.href.split('/').pop()\r\n    var avatar = link.style['background-image']\r\n      .replace('url(\"', '')\r\n      .replace('\")', '')\r\n    console.log(\r\n      link.href,\r\n      link.style['background-image'].replace('url(\"', '').replace('\")', '')\r\n    )\r\n\r\n    // if (!segIframe) {\r\n    //   segIframe = document.createElement('iframe')\r\n    //   segIframe.src = 'https://segmentfault.com/write?freshman=1'\r\n    //   document.body.append(segIframe)\r\n    // }\r\n    initliazeFrame('https://segmentfault.com/write?freshman=1', 'segment')\r\n\r\n    return {\r\n      uid: uid,\r\n      title: uid,\r\n      avatar: avatar,\r\n      type: 'segmentfault',\r\n      displayName: 'Segmentfault',\r\n      supportTypes: ['markdown', 'html'],\r\n      home: 'https://segmentfault.com/user/draft',\r\n      icon:\r\n        'https://imgcache.iyiou.com/Company/2016-05-11/cf-segmentfault.jpg',\r\n    }\r\n  }\r\n\r\n  async addPost(post) {\r\n    // console.log('addPost', segIframe)\r\n\r\n    var turndownService = new __WEBPACK_IMPORTED_MODULE_0_turndown__[\"a\" /* default */]()\r\n    turndownService.addRule('codefor', {\r\n      filter: ['pre'],\r\n      replacement: function (content) {\r\n        // content = content.replace(new RegExp(\"` \", \"g\"), \"\\n\");\r\n        // content = content.replace(new RegExp(\"`\", \"g\"), \"\");\r\n        return ['```', content, '```'].join('\\n')\r\n      },\r\n    })\r\n\r\n    var markdown = turndownService.turndown(post.post_content)\r\n    post.markdown = markdown\r\n    console.log(markdown)\r\n\r\n    var data = await requestFrameMethod(\r\n      {\r\n        type: 'sendPost',\r\n        data: {\r\n          type: 1,\r\n          url: '',\r\n          blogId: 0,\r\n          isTiming: 0,\r\n          created: '',\r\n          weibo: 0,\r\n          license: 0,\r\n          tags: '',\r\n          title: post.post_title,\r\n          text: post.markdown,\r\n          articleId: '',\r\n          draftId: '',\r\n          id: '',\r\n        },\r\n      },\r\n      'segment'\r\n    )\r\n\r\n    console.log('data', data)\r\n    return {\r\n      status: 'success',\r\n      post_id: data.data,\r\n    }\r\n  }\r\n\r\n  async editPost(post_id, post) {\r\n    return {\r\n      status: 'success',\r\n      post_id: post_id,\r\n      draftLink: 'https://segmentfault.com/write?draftId=' + post_id,\r\n    }\r\n  }\r\n\r\n  async uploadFile(file) {\r\n    var formdata = new FormData()\r\n    var blob = new Blob([file.bits])\r\n    formdata.append('image', blob)\r\n    var res = await axios({\r\n      url: 'https://segmentfault.com/img/upload/image',\r\n      method: 'post',\r\n      data: formdata,\r\n      headers: { 'Content-Type': 'multipart/form-data' },\r\n    })\r\n    var url = 'https://image-static.segmentfault.com/' + res.data[2]\r\n    //  return url;\r\n    return [\r\n      {\r\n        id: res.data[2],\r\n        object_key: res.data[2],\r\n        url: url,\r\n      },\r\n    ]\r\n  }\r\n\r\n  async preEditPost(post) {\r\n    var div = $('<div>')\r\n    $('body').append(div)\r\n\r\n    try {\r\n      // post.content = post.content.replace(/\\>\\s+\\</g,'');\r\n      console.log('zihu.Juejin')\r\n      div.html(post.content)\r\n      // var org = $(post.content);\r\n      // var doc = $('<div>').append(org.clone());\r\n      var doc = div\r\n      // var pres = doc.find(\"pre\");\r\n      processDocCode(div)\r\n      makeImgVisible(div)\r\n\r\n      var tempDoc = $('<div>').append(doc.clone())\r\n      post.content =\r\n        tempDoc.children('div').length == 1\r\n          ? tempDoc.children('div').html()\r\n          : tempDoc.html()\r\n\r\n      console.log('after.predEdit', post.content)\r\n    } catch (e) {\r\n      console.log('preEdit.error', e)\r\n    }\r\n  }\r\n\r\n  async uploadFileByForm($file) {\r\n    var formdata = new FormData()\r\n    formdata.append('image', $file)\r\n    var res = await axios({\r\n      url: 'https://segmentfault.com/img/upload/image',\r\n      method: 'post',\r\n      data: formdata,\r\n      headers: { 'Content-Type': 'multipart/form-data' },\r\n    })\r\n    var url = 'https://image-static.segmentfault.com/' + res.data[2]\r\n    return url\r\n  }\r\n}\n/* harmony export (immutable) */ __webpack_exports__[\"a\"] = Segmentfault;\n\r\n\n\n/***/ }),\n/* 9 */\n/***/ (function(module, exports) {\n\n/* (ignored) */\n\n/***/ }),\n/* 10 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0_turndown__ = __webpack_require__(0);\nconst { processDocCode, makeImgVisible } = __webpack_require__(1)\n\n\nclass Juejin {\n  constructor(ac) {\n    this.version = '0.0.1'\n    this.name = 'juejin'\n    this.account = ac\n    this.skipUpload = true\n    console.log('Juejin', 'initliaze', ac, this)\n  }\n\n  async getMetaData() {\n    var data = await $.get('https://juejin.im/auth')\n    console.log(data)\n    return {\n      uid: data.userId,\n      title: data.user.username,\n      avatar: data.user.avatarLarge,\n      type: 'juejin',\n      displayName: '掘金',\n      raw: data,\n      supportTypes: ['markdown', 'html'],\n      home: 'https://juejin.im/editor/drafts',\n      icon: 'https://gold-cdn.xitu.io/favicons/favicon.ico',\n    }\n  }\n\n  async addPost(post, _instance) {\n    // https://post-storage-api-ms.juejin.im/v1/draftStorage\n    console.log('addPost', _instance)\n    console.log(_instance.account, post.markdown)\n    // var post_id = res.data.id;\n    console.log('TurndownService', __WEBPACK_IMPORTED_MODULE_0_turndown__[\"a\" /* default */])\n    var turndownService = new __WEBPACK_IMPORTED_MODULE_0_turndown__[\"a\" /* default */]()\n    turndownService.addRule('codefor', {\n      filter: ['pre'],\n      replacement: function (content) {\n        // content = content.replace(new RegExp(\"` \", \"g\"), \"\\n\");\n        // content = content.replace(new RegExp(\"`\", \"g\"), \"\");\n        return ['```', content, '```'].join('\\n')\n      },\n    })\n\n    var markdown = turndownService.turndown(post.post_content)\n    console.log(markdown)\n    var res = await $.ajax({\n      url: 'https://post-storage-api-ms.juejin.im/v1/draftStorage',\n      type: 'POST',\n      dataType: 'JSON',\n      headers: {\n        accept: 'application/json',\n      },\n      data: {\n        uid: _instance.account.uid,\n        device_id: _instance.account.raw.clientId,\n        token: _instance.account.raw.token,\n        src: 'web',\n        category: '5562b428e4b00c57d9b94b9d',\n        content: '',\n        // html: post.post_content,\n        html: ``,\n        markdown: markdown,\n        screenshot: '',\n        isTitleImageFullscreen: 0,\n        tags: '',\n        title: post.post_title,\n        type: 'markdown',\n      },\n    })\n    console.log(res)\n    return {\n      status: 'success',\n      post_id: res.d[0],\n    }\n  }\n\n  async editPost(post_id, post) {\n    return {\n      status: 'success',\n      post_id: post_id,\n      draftLink: 'https://juejin.im/editor/drafts/' + post_id,\n    }\n  }\n\n  async uploadFile(file) {\n    var src = file.src\n    var res = await $.ajax({\n      url: 'https://cdn-ms.juejin.im/v1/fetch',\n      type: 'POST',\n      contentType: 'application/json',\n      xhrFields: {\n        withCredentials: true,\n      },\n      headers: {\n        accept: 'application/json',\n      },\n      data: JSON.stringify({\n        bucket: 'gold-user-assets',\n        url: src,\n      }),\n    })\n\n    console.log(res)\n    return [\n      {\n        id: res.dkey,\n        object_key: res.dkey,\n        url: res.d.url.https,\n      },\n    ]\n  }\n\n  async preEditPost(post) {\n    var div = $('<div>')\n    $('body').append(div)\n\n    try {\n      // post.content = post.content.replace(/\\>\\s+\\</g,'');\n      console.log('zihu.Juejin')\n      div.html(post.content)\n\n      // var org = $(post.content);\n      // var doc = $('<div>').append(org.clone());\n      var doc = div\n      // var pres = doc.find(\"pre\");\n      processDocCode(div)\n      makeImgVisible(div)\n\n      var tempDoc = $('<div>').append(doc.clone())\n      post.content =\n        tempDoc.children('div').length == 1\n          ? tempDoc.children('div').html()\n          : tempDoc.html()\n\n      console.log('after.predEdit', post.content)\n    } catch (e) {\n      console.log('preEdit.error', e)\n    }\n  }\n\n  async uploadFileByForm($file) {\n    var formdata = new FormData()\n    formdata.append('file', $file)\n    var res = await axios({\n      url: 'https://cdn-ms.juejin.im/v1/upload?bucket=gold-user-assets',\n      method: 'post',\n      data: formdata,\n      headers: { 'Content-Type': 'multipart/form-data' },\n    })\n\n    return res.data.d.url.http\n  }\n}\n/* harmony export (immutable) */ __webpack_exports__[\"a\"] = Juejin;\n\n\n\n/***/ }),\n/* 11 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n// https://mp.csdn.net/mdeditor/saveArticle\n// title: 996.ICU项目Stars构成分析\n// markdowncontent:\n// 996.ICU项目Stars构成分析\n// content: <p>996.ICU项目Stars构成分析</p>\n// id:\n// readType: public\n// tags:\n// status: 2\n// categories:\n// type:\n// original_link:\n// authorized_status: undefined\n// articleedittype: 1\n// Description:\n// resource_url:\n// csrf_token:\n\n// https://me.csdn.net/api/user/show\n\nclass CSDN {\n  constructor() {\n    this.name = 'csdn'\n  }\n\n  async getMetaData() {\n    var res = await $.get('https://me.csdn.net/api/user/show')\n    return {\n      uid: res.data.csdnid,\n      title: res.data.username,\n      avatar: res.data.avatarurl,\n      type: 'csdn',\n      displayName: 'CSDN',\n      supportTypes: ['markdown'],\n      home: 'https://mp.csdn.net/',\n      icon: 'https://csdnimg.cn/public/favicon.ico',\n    }\n  }\n\n  async addPost(post) {\n    var res = await $.ajax({\n      url: 'https://mp.csdn.net/mdeditor/saveArticle',\n      type: 'POST',\n      dataType: 'JSON',\n      headers: {\n        accept: 'application/json',\n      },\n      data: {\n        title: post.post_title,\n        markdowncontent: post.markdown,\n        content: post.post_content,\n        id: '',\n        readType: 'public',\n        tags: '',\n        status: 2,\n        categories: '',\n        type: '',\n        original_link: '',\n        authorized_status: 'undefined',\n        articleedittype: 1,\n        Description: '',\n        resource_url: '',\n        csrf_token: '',\n      },\n    })\n    console.log(res)\n    return {\n      status: 'success',\n      post_id: res.data.id,\n    }\n  }\n\n  async editPost(post_id, post) {\n    return {\n      status: 'success',\n      post_id: post_id,\n      draftLink: 'https://mp.csdn.net/mdeditor/' + post_id,\n    }\n  }\n}\n/* harmony export (immutable) */ __webpack_exports__[\"a\"] = CSDN;\n\n\n\n\n/***/ }),\n/* 12 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n// https://mp.csdn.net/mdeditor/saveArticle\n// title: 996.ICU项目Stars构成分析\n// markdowncontent:\n// 996.ICU项目Stars构成分析\n// content: <p>996.ICU项目Stars构成分析</p>\n// id:\n// readType: public\n// tags:\n// status: 2\n// categories:\n// type:\n// original_link:\n// authorized_status: undefined\n// articleedittype: 1\n// Description:\n// resource_url:\n// csrf_token:\n// https://me.csdn.net/api/user/show\n// https://passport.cnblogs.com/user/LoginInfo?callback=jQuery17083998854357229_1570784103705&_=1570784103764\n\nclass Cnblog {\n  constructor() {\n    this.name = 'cnblog'\n  }\n\n  async getMetaData() {\n    var res = await $.get('https://home.cnblogs.com/user/CurrentUserInfo')\n    var parser = new DOMParser()\n    var htmlDoc = parser.parseFromString(res, 'text/html')\n    var img = htmlDoc.getElementsByClassName('pfs')[0]\n    var link = img.parentNode.href\n    var pie = link.split('/')\n    pie.pop()\n    var uid = pie.pop()\n    console.log(link)\n    return {\n      uid: uid,\n      title: uid,\n      avatar: img.src,\n      type: 'cnblog',\n      displayName: 'CnBlog',\n      supportTypes: ['markdown'],\n      home: 'https://i.cnblogs.com/EditArticles.aspx?IsDraft=1',\n      icon: 'https://common.cnblogs.com/favicon.ico',\n    }\n  }\n\n  async addPost(post) {\n    var postId = null\n    try {\n      var res = await $.ajax({\n        url: 'https://i.cnblogs.com/EditArticles.aspx?opt=1',\n        type: 'POST',\n        dataType: 'JSON',\n        headers: {},\n        data: {\n          __VIEWSTATE: '',\n          __VIEWSTATEGENERATOR: '',\n          Editor$Edit$txbTitle: post.post_title,\n          Editor$Edit$EditorBody: post.markdown,\n          Editor$Edit$Advanced$ckbPublished: 'on',\n          Editor$Edit$Advanced$chkDisplayHomePage: 'on',\n          Editor$Edit$Advanced$chkComments: 'on',\n          Editor$Edit$Advanced$chkMainSyndication: 'on',\n          Editor$Edit$Advanced$txbEntryName: '',\n          Editor$Edit$Advanced$txbExcerpt: '',\n          Editor$Edit$Advanced$txbTag: '',\n          Editor$Edit$Advanced$tbEnryPassword: '',\n          Editor$Edit$lkbDraft: '存为草稿',\n        },\n      })\n      console.log('CNBLOG addPost', res)\n    } catch (e) {\n      var parser = new DOMParser()\n      var htmlDoc = parser.parseFromString(e.responseText, 'text/html')\n      var editLink = htmlDoc.getElementById('TipsPanel_LinkEdit')\n      var ErrorPanel = htmlDoc.getElementsByClassName('ErrorPanel')[0]\n      if (editLink) {\n        postId = editLink.href.split('postid=')[1]\n        console.log('CNBLOG error', editLink, editLink.href.query)\n      } else {\n        if (ErrorPanel) {\n          throw Error(ErrorPanel.innerText)\n        }\n      }\n      console.log('CNBLOG error', e.responseText, htmlDoc, editLink)\n    }\n\n    return {\n      status: 'success',\n      post_id: postId,\n    }\n  }\n\n  async editPost(post_id, post) {\n    return {\n      status: 'success',\n      post_id: post_id,\n      draftLink:\n        'https://i.cnblogs.com/EditArticles.aspx?postid=' +\n        post_id +\n        '&update=1',\n    }\n  }\n}\n/* harmony export (immutable) */ __webpack_exports__[\"a\"] = Cnblog;\n\n\n\n\n/***/ }),\n/* 13 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\nvar weixinMetaCache = null\r\n\r\nclass WeixinDriver {\r\n  constructor() {\r\n    this.meta = weixinMetaCache\r\n    this.name = 'weixin'\r\n  }\r\n\r\n  async getMetaData() {\r\n    var res = await $.ajax({ url: 'https://mp.weixin.qq.com/' })\r\n    var innerDoc = $(res)\r\n    var doc = $('<div>').append(innerDoc.clone())\r\n    // console.log('WeixinDriver', res);\r\n    var code = doc.find('script').eq(0).text()\r\n    code = code.substring(code.indexOf('window.wx.commonData'))\r\n    var wx = new Function(\r\n      'window.wx = {}; window.handlerNickname = function(){};' +\r\n        code +\r\n        '; return window.wx;'\r\n    )()\r\n    console.log(code, wx)\r\n    var commonData = Object.assign({}, wx.commonData)\r\n    delete window.wx\r\n    if (!commonData.data.t) {\r\n      throw new Error('未登录')\r\n    }\r\n    var metadata = {\r\n      uid: commonData.data.user_name,\r\n      title: commonData.data.nick_name,\r\n      token: commonData.data.t,\r\n      commonData: commonData,\r\n      avatar: doc.find('.weui-desktop-account__thumb').eq(0).attr('src'),\r\n      type: 'weixin',\r\n      supportTypes: ['html'],\r\n      home: 'https://mp.weixin.qq.com',\r\n      icon: 'https://mp.weixin.qq.com/favicon.ico',\r\n    }\r\n    weixinMetaCache = metadata\r\n    console.log('weixinMetaCache', weixinMetaCache)\r\n    return metadata\r\n  }\r\n\r\n  async addPost(post) {\r\n    return {\r\n      status: 'success',\r\n      post_id: 0,\r\n    }\r\n  }\r\n\r\n  async getArticle(data) {\r\n    var token = weixinMetaCache.token || '*********'\r\n    const tempRespone = await $.get(\r\n      `https://mp.weixin.qq.com/cgi-bin/appmsg?action=get_temp_url&appmsgid=${data.msgId}&itemidx=1&token=${token}&lang=zh_CN&f=json&ajax=1`\r\n    )\r\n    const { temp_url } = tempRespone\r\n    const htmlData = await $.get(temp_url)\r\n    const doc = $(htmlData)\r\n    console.log('htmlData', htmlData)\r\n    var post = {}\r\n\r\n    const allMetas = doc\r\n      .filter(function(index, el) {\r\n        return $(el).attr('property') && $(el).attr('content')\r\n      })\r\n      .map(function() {\r\n        return {\r\n          name: $(this).attr('property'),\r\n          content: $(this).attr('content'),\r\n        }\r\n      })\r\n      .toArray()\r\n\r\n    const metaObj = {}\r\n    allMetas.forEach(obj => {\r\n      metaObj[obj.name] = obj.content\r\n    })\r\n\r\n    post.title = metaObj['og:title']\r\n    post.content = doc.find('#js_content').html()\r\n    post.thumb = metaObj['og:image']\r\n    post.desc = metaObj['og:description'] \r\n    post.link = metaObj['og:url'];\r\n    console.log('post', post, doc)\r\n    return post\r\n  }\r\n\r\n  async editPost(post_id, post) {\r\n    console.log('editPost', post.post_thumbnail)\r\n    var res = await $.ajax({\r\n      url:\r\n        'https://mp.weixin.qq.com/cgi-bin/operate_appmsg?t=ajax-response&sub=create&type=10&token=' +\r\n        weixinMetaCache.token +\r\n        '&lang=zh_CN',\r\n      type: 'POST',\r\n      dataType: 'JSON',\r\n      data: {\r\n        token: weixinMetaCache.token,\r\n        lang: 'zh_CN',\r\n        f: 'json',\r\n        ajax: '1',\r\n        random: Math.random(),\r\n        AppMsgId: '',\r\n        count: '1',\r\n        data_seq: '0',\r\n        operate_from: 'Chrome',\r\n        isnew: '0',\r\n        ad_video_transition0: '',\r\n        can_reward0: '0',\r\n        related_video0: '',\r\n        is_video_recommend0: '-1',\r\n        title0: post.post_title,\r\n        author0: '',\r\n        writerid0: '0',\r\n        fileid0: '',\r\n        digest0: post.post_title,\r\n        auto_gen_digest0: '1',\r\n        content0: post.post_content,\r\n        sourceurl0: '',\r\n        need_open_comment0: '1',\r\n        only_fans_can_comment0: '0',\r\n        cdn_url0: '',\r\n        cdn_235_1_url0: '',\r\n        cdn_1_1_url0: '',\r\n        cdn_url_back0: '',\r\n        crop_list0: '',\r\n        music_id0: '',\r\n        video_id0: '',\r\n        voteid0: '',\r\n        voteismlt0: '',\r\n        supervoteid0: '',\r\n        cardid0: '',\r\n        cardquantity0: '',\r\n        cardlimit0: '',\r\n        vid_type0: '',\r\n        show_cover_pic0: '0',\r\n        shortvideofileid0: '',\r\n        copyright_type0: '0',\r\n        releasefirst0: '',\r\n        platform0: '',\r\n        reprint_permit_type0: '',\r\n        allow_reprint0: '',\r\n        allow_reprint_modify0: '',\r\n        original_article_type0: '',\r\n        ori_white_list0: '',\r\n        free_content0: '',\r\n        fee0: '0',\r\n        ad_id0: '',\r\n        guide_words0: '',\r\n        is_share_copyright0: '0',\r\n        share_copyright_url0: '',\r\n        source_article_type0: '',\r\n        reprint_recommend_title0: '',\r\n        reprint_recommend_content0: '',\r\n        share_page_type0: '0',\r\n        share_imageinfo0: '{\"list\":[]}',\r\n        share_video_id0: '',\r\n        dot0: '{}',\r\n        share_voice_id0: '',\r\n        insert_ad_mode0: '',\r\n        categories_list0: '[]',\r\n        sections0:\r\n          '[{\"section_index\":1000000,\"text_content\":\"​kkk\",\"section_type\":9,\"ad_available\":false}]',\r\n        compose_info0:\r\n          '{\"list\":[{\"blockIdx\":1,\"content\":\"<p>​kkk<br></p>\",\"width\":574,\"height\":27,\"topMargin\":0,\"blockType\":9,\"background\":\"rgba(0, 0, 0, 0)\",\"text\":\"kkk\",\"textColor\":\"rgb(51, 51, 51)\",\"textFontSize\":\"17px\",\"textBackGround\":\"rgba(0, 0, 0, 0)\"}]}',\r\n      },\r\n    })\r\n\r\n    if (!res.appMsgId) {\r\n      var err = formatError(res)\r\n      console.log('error', err)\r\n      throw new Error(\r\n        '同步失败 错误内容：' + (err && err.errmsg ? err.errmsg : res.ret)\r\n      )\r\n    }\r\n    return {\r\n      status: 'success',\r\n      post_id: res.appMsgId,\r\n      draftLink:\r\n        'https://mp.weixin.qq.com/cgi-bin/appmsg?t=media/appmsg_edit&action=edit&type=10&appmsgid=' +\r\n        res.appMsgId +\r\n        '&token=' +\r\n        weixinMetaCache.token +\r\n        '&lang=zh_CN',\r\n    }\r\n    // https://zhuanlan.zhihu.com/api/articles/68769713/draft\r\n  }\r\n\r\n  async uploadFile(file) {\r\n    var formdata = new FormData()\r\n    var blob = new Blob([file.bits], {\r\n        type: file.type\r\n    });\r\n\r\n    formdata.append('type', blob.type)\r\n    formdata.append('id', new Date().getTime())\r\n    formdata.append('name', new Date().getTime() + '.jpg')\r\n    formdata.append('lastModifiedDate', new Date().toString())\r\n    formdata.append('size', blob.size)\r\n    formdata.append('file', blob, new Date().getTime() + '.jpg')\r\n    \r\n    var ticket_id = this.meta.commonData.data.user_name,\r\n      ticket = this.meta.commonData.data.ticket,\r\n      svr_time =  this.meta.commonData.data.time,\r\n      token = this.meta.commonData.data.t,\r\n      seq = new Date().getTime();\r\n\r\n    var res = await axios({\r\n      url: `https://mp.weixin.qq.com/cgi-bin/filetransfer?action=upload_material&f=json&scene=8&writetype=doublewrite&groupid=1&ticket_id=${ticket_id}&ticket=${ticket}&svr_time=${svr_time}&token=${token}&lang=zh_CN&seq=${seq}&t=` + Math.random(),\r\n      method: 'post',\r\n      data: formdata,\r\n      headers: { 'Content-Type': 'multipart/form-data' },\r\n    })\r\n    var url = res.data.cdn_url\r\n    if(res.data.base_resp.err_msg != 'ok') {\r\n      console.log(res.data);\r\n      throw new Error('upload failed')\r\n    }\r\n    //  return url;\r\n    return [\r\n      {\r\n        id: res.data.content,\r\n        object_key: res.data.content,\r\n        url: url,\r\n      },\r\n    ]\r\n  }\r\n\r\n  async uploadFileBySource(file) {\r\n    var src = file.src\r\n    var res = await $.ajax({\r\n      url:\r\n        'https://mp.weixin.qq.com/cgi-bin/uploadimg2cdn?lang=zh_CN&token=' +\r\n        weixinMetaCache.token +\r\n        '&t=' +\r\n        Math.random(),\r\n      type: 'POST',\r\n      dataType: 'JSON',\r\n      data: {\r\n        imgurl: src,\r\n        t: 'ajax-editor-upload-img',\r\n        token: weixinMetaCache.token,\r\n        lang: 'zh_CN',\r\n        f: 'json',\r\n        ajax: '1',\r\n      },\r\n    })\r\n\r\n    if (res.errcode != 0) {\r\n      throw new Error('图片上传失败' + src)\r\n    }\r\n    console.log(res)\r\n    return [\r\n      {\r\n        id: 'aaa',\r\n        object_key: 'aaa',\r\n        url: res.url,\r\n      },\r\n    ]\r\n  }\r\n\r\n  async preEditPost(post) {\r\n    var div = $('<div>')\r\n    $('body').append(div)\r\n\r\n    if (post.inline_content) {\r\n      post.content = post.inline_content\r\n    }\r\n\r\n    div.html(post.content)\r\n\r\n    var doc = div\r\n    var tags = doc.find('p')\r\n    for (let mindex = 0; mindex < tags.length; mindex++) {\r\n      const tag = tags.eq(mindex)\r\n      try {\r\n        var nextHasImage = tag.next().children('img').length\r\n        var span = $('<span></span>')\r\n        span.html(tag.html())\r\n        tag.html('')\r\n        tag.append(span)\r\n        // if (!tag.children(\"br\").length) tag.css(\"margin-bottom\", \"20px\");\r\n        // tag.after(\"<p><br></p>\");\r\n        // span.css(\"color\", \"rgb(68, 68, 68)\");\r\n        // span.css(\"font-size\", \"16px\");\r\n      } catch (e) {}\r\n    }\r\n\r\n    var tags = doc.find('img')\r\n    for (let mindex = 0; mindex < tags.length; mindex++) {\r\n      const tag = tags.eq(mindex)\r\n      const wraperTag = tag.parent()\r\n      try {\r\n        tag.removeAttr('_src')\r\n        tag.attr('style', '')\r\n        wraperTag.replaceWith('<p>' + wraperTag.html() + '</p>')\r\n      } catch (e) {}\r\n    }\r\n\r\n    var pres = doc.find('a')\r\n    for (let mindex = 0; mindex < pres.length; mindex++) {\r\n      const pre = pres.eq(mindex)\r\n      try {\r\n        pre.after(pre.html()).remove()\r\n      } catch (e) {}\r\n    }\r\n\r\n    var processEmptyLine = function (idx, el) {\r\n      var $obj = $(this)\r\n      var originalText = $obj.text()\r\n      var img = $obj.find('img')\r\n      var brs = $obj.find('br')\r\n      if (originalText == '') {\r\n        ;(function () {\r\n          if (img.length) return\r\n          if (!brs.length) return\r\n          $obj.remove()\r\n        })()\r\n      }\r\n    }\r\n\r\n    var processListItem = function (idx, el) {\r\n      var $obj = $(this)\r\n      $obj.html($('<p></p>').append($obj.html()))\r\n    }\r\n\r\n    doc.find('li').each(processListItem)\r\n    // remove empty break line\r\n    doc.find('p').each(processEmptyLine)\r\n\r\n    var processBr = function (idx, el) {\r\n      var $obj = $(this)\r\n      if (!$obj.next().length) {\r\n        $obj.remove()\r\n      }\r\n    }\r\n\r\n    doc.find('br').each(processBr)\r\n    post.content = $('<div>')\r\n      .append(\r\n        \"<section style='margin-left: 6px;margin-right: 6px;line-height: 1.75em;'>\" +\r\n          doc.clone().html() +\r\n          '</section>'\r\n      )\r\n      .html()\r\n\r\n    console.log('post.content', post.content)\r\n    var inlineCssHTML = juice.inlineContent(\r\n      post.content,\r\n      `\r\n    /**\r\n    * common style\r\n    */\r\n\r\n   html, address,\r\n   blockquote,\r\n   body, dd, div,\r\n   dl, dt, fieldset, form,\r\n   frame, frameset,\r\n   h1, h2, h3, h4,\r\n   h5, h6, noframes,\r\n   ol, p, ul, center,\r\n   dir, hr, menu, pre   { display: block; unicode-bidi: embed }\r\n   li              { display: list-item }\r\n   head            { display: none }\r\n   table           { display: table }\r\n   tr              { display: table-row }\r\n   thead           { display: table-header-group }\r\n   tbody           { display: table-row-group }\r\n   tfoot           { display: table-footer-group }\r\n   col             { display: table-column }\r\n   colgroup        { display: table-column-group }\r\n   td, th          { display: table-cell }\r\n   caption         { display: table-caption }\r\n   th              { font-weight: bolder; text-align: center }\r\n   caption         { text-align: center }\r\n   body            { margin: 8px }\r\n   h1              { font-size: 2em; margin: .67em 0 }\r\n   h2              { font-size: 1.5em; margin: .75em 0 }\r\n   h3              { font-size: 1.17em; margin: .83em 0 }\r\n   h4, p,\r\n   blockquote, ul,\r\n   fieldset, form,\r\n   ol, dl, dir,\r\n   menu            { margin: 1.12em 0 }\r\n   h5              { font-size: .83em; margin: 1.5em 0 }\r\n   h6              { font-size: .75em; margin: 1.67em 0 }\r\n   h1, h2, h3, h4,\r\n   h5, h6, b,\r\n   strong          { font-weight: bolder }\r\n   blockquote      { margin-left: 40px; margin-right: 40px }\r\n   i, cite, em,\r\n   var, address    { font-style: italic }\r\n   pre, tt, code,\r\n   kbd, samp       { font-family: monospace }\r\n   pre             { white-space: pre }\r\n   button, textarea,\r\n   input, select   { display: inline-block }\r\n   big             { font-size: 1.17em }\r\n   small, sub, sup { font-size: .83em }\r\n   sub             { vertical-align: sub }\r\n   sup             { vertical-align: super }\r\n   table           { border-spacing: 2px; }\r\n   thead, tbody,\r\n   tfoot           { vertical-align: middle }\r\n   td, th, tr      { vertical-align: inherit }\r\n   s, strike, del  { text-decoration: line-through }\r\n   hr              { border: 1px inset }\r\n   ol, ul, dir,\r\n   menu, dd        { margin-left: 40px }\r\n   ol              { list-style-type: decimal }\r\n   ol ul, ul ol,\r\n   ul ul, ol ol    { margin-top: 0; margin-bottom: 0 }\r\n   u, ins          { text-decoration: underline }\r\n   br:before       { content: \"\\A\"; white-space: pre-line }\r\n   center          { text-align: center }\r\n   :link, :visited { text-decoration: underline }\r\n   :focus          { outline: thin dotted invert }\r\n   \r\n   /* Begin bidirectionality settings (do not change) */\r\n   BDO[DIR=\"ltr\"]  { direction: ltr; unicode-bidi: bidi-override }\r\n   BDO[DIR=\"rtl\"]  { direction: rtl; unicode-bidi: bidi-override }\r\n   \r\n   *[DIR=\"ltr\"]    { direction: ltr; unicode-bidi: embed }\r\n   *[DIR=\"rtl\"]    { direction: rtl; unicode-bidi: embed }\r\n   \r\n   @media print {\r\n     h1            { page-break-before: always }\r\n     h1, h2, h3,\r\n     h4, h5, h6    { page-break-after: avoid }\r\n     ul, ol, dl    { page-break-before: avoid }\r\n   }\r\n   h1,\r\n   h2,\r\n   h3,\r\n   h4,\r\n   h5,\r\n   h6 {\r\n     font-weight: bold;\r\n   }\r\n   \r\n   h1 {\r\n     font-size: 1.25em;\r\n     line-height: 1.4em;\r\n   }\r\n   \r\n   h2 {\r\n     font-size: 1.125em;\r\n   }\r\n   \r\n   h3 {\r\n     font-size: 1.05em;\r\n   }\r\n   \r\n   h4,\r\n   h5,\r\n   h6 {\r\n     font-size: 1em;\r\n     margin: 1em 0;\r\n   }\r\n\r\n    p {\r\n      color: rgb(51, 51, 51);\r\n      font-size: 15px;\r\n    }\r\n\r\n    li p {\r\n      margin: 0;\r\n    }\r\n   `\r\n    )\r\n    console.log('inlineCssHTML new', inlineCssHTML)\r\n    post.content = inlineCssHTML\r\n  }\r\n}\n/* harmony export (immutable) */ __webpack_exports__[\"a\"] = WeixinDriver;\n\r\n\r\nfunction formatError(e) {\r\n  var r,\r\n    a = {\r\n      errmsg: '',\r\n      index: !1,\r\n    }\r\n  switch (\r\n    ('undefined' != typeof e.ret\r\n      ? (r = 1 * e.ret)\r\n      : e.base_resp &&\r\n        'undefined' != typeof e.base_resp.ret &&\r\n        (r = 1 * e.base_resp.ret),\r\n    1 * r)\r\n  ) {\r\n    case -8:\r\n    case -6:\r\n      ;(e.ret = '-6'), (a.errmsg = '请输入验证码')\r\n      break\r\n\r\n    case 62752:\r\n      a.errmsg = '可能含有具备安全风险的链接，请检查'\r\n      break\r\n\r\n    case 64505:\r\n      a.errmsg = '发送预览失败，请稍后再试'\r\n      break\r\n\r\n    case 64504:\r\n      a.errmsg = '保存图文消息发送错误，请稍后再试'\r\n      break\r\n\r\n    case 64518:\r\n      a.errmsg = '正文只能包含一个投票'\r\n      break\r\n\r\n    case 10704:\r\n    case 10705:\r\n      a.errmsg = '该素材已被删除'\r\n      break\r\n\r\n    case 10701:\r\n      a.errmsg = '用户已被加入黑名单，无法向其发送消息'\r\n      break\r\n\r\n    case 10703:\r\n      a.errmsg = '对方关闭了接收消息'\r\n      break\r\n\r\n    case 10700:\r\n    case 64503:\r\n      a.errmsg =\r\n        '1.接收预览消息的微信尚未关注公众号，请先扫码关注<br /> 2.如果已经关注公众号，请查看微信的隐私设置（在手机微信的“我->设置->隐私->添加我的方式”中），并开启“可通过以下方式找到我”的“手机号”、“微信号”、“QQ号”，否则可能接收不到预览消息'\r\n      break\r\n\r\n    case 64502:\r\n      a.errmsg = '你输入的微信号不存在，请重新输入'\r\n      break\r\n\r\n    case 64501:\r\n      a.errmsg = '你输入的帐号不存在，请重新输入'\r\n      break\r\n\r\n    case 412:\r\n      a.errmsg = '图文中含非法外链'\r\n      break\r\n\r\n    case 64515:\r\n      a.errmsg = '当前素材非最新内容，请重新打开并编辑'\r\n      break\r\n\r\n    case 320001:\r\n      a.errmsg = '该素材已被删除，无法保存'\r\n      break\r\n\r\n    case 64702:\r\n      a.errmsg = '标题超出64字长度限制'\r\n      break\r\n\r\n    case 64703:\r\n      a.errmsg = '摘要超出120字长度限制'\r\n      break\r\n\r\n    case 64704:\r\n      a.errmsg = '推荐语超出300字长度限制'\r\n      break\r\n\r\n    case 64708:\r\n      a.errmsg = '推荐语超出140字长度限制'\r\n      break\r\n\r\n    case 64515:\r\n      a.errmsg = '当前素材非最新内容'\r\n      break\r\n\r\n    case 200041:\r\n      a.errmsg = '此素材有文章存在违规，无法编辑'\r\n      break\r\n\r\n    case 64506:\r\n      a.errmsg = '保存失败,链接不合法'\r\n      break\r\n\r\n    case 64507:\r\n      a.errmsg =\r\n        '内容不能包含外部链接，请输入http://或https://开头的公众号相关链接'\r\n      break\r\n\r\n    case 64510:\r\n      a.errmsg = '内容不能包含音频，请调整'\r\n      break\r\n\r\n    case 64511:\r\n      a.errmsg = '内容不能包多个音频，请调整'\r\n      break\r\n\r\n    case 64512:\r\n      a.errmsg = '文章中音频错误,请使用音频添加按钮重新添加。'\r\n      break\r\n\r\n    case 64508:\r\n      a.errmsg = '查看原文链接可能具备安全风险，请检查'\r\n      break\r\n\r\n    case 64550:\r\n      a.errmsg = '请勿插入不合法的图文消息链接'\r\n      break\r\n\r\n    case 64558:\r\n      a.errmsg = '请勿插入图文消息临时链接，链接会在短期失效'\r\n      break\r\n\r\n    case 64559:\r\n      a.errmsg = '请勿插入未群发的图文消息链接'\r\n      break\r\n\r\n    case -99:\r\n      a.errmsg = '内容超出字数，请调整'\r\n      break\r\n\r\n    case 64705:\r\n      a.errmsg = '内容超出字数，请调整'\r\n      break\r\n\r\n    case -1:\r\n      a.errmsg = '系统错误，请注意备份内容后重试'\r\n      break\r\n\r\n    case -2:\r\n    case 200002:\r\n      a.errmsg = '参数错误，请注意备份内容后重试'\r\n      break\r\n\r\n    case 64509:\r\n      a.errmsg = '正文中不能包含超过3个视频，请重新编辑正文后再保存。'\r\n      break\r\n\r\n    case -5:\r\n      a.errmsg = '服务错误，请注意备份内容后重试。'\r\n      break\r\n\r\n    case 64513:\r\n      a.errmsg = '请从正文中选择封面，再尝试保存。'\r\n      break\r\n\r\n    case -206:\r\n      a.errmsg = '目前，服务负荷过大，请稍后重试。'\r\n      break\r\n\r\n    case 10801:\r\n      ;(a.errmsg =\r\n        '标题不能有违反公众平台协议、相关法律法规和政策的内容，请重新编辑。'),\r\n        (a.index = 1 * e.msg)\r\n      break\r\n\r\n    case 10802:\r\n      ;(a.errmsg =\r\n        '作者不能有违反公众平台协议、相关法律法规和政策的内容，请重新编辑。'),\r\n        (a.index = 1 * e.msg)\r\n      break\r\n\r\n    case 10803:\r\n      ;(a.errmsg = '敏感链接，请重新添加。'), (a.index = 1 * e.msg)\r\n      break\r\n\r\n    case 10804:\r\n      ;(a.errmsg =\r\n        '摘要不能有违反公众平台协议、相关法律法规和政策的内容，请重新编辑。'),\r\n        (a.index = 1 * e.msg)\r\n      break\r\n\r\n    case 10806:\r\n      ;(a.errmsg =\r\n        '正文不能有违反公众平台协议、相关法律法规和政策的内容，请重新编辑。'),\r\n        (a.index = 1 * e.msg)\r\n      break\r\n\r\n    case 10808:\r\n      ;(a.errmsg =\r\n        '推荐语不能有违反公众平台协议、相关法律法规和政策的内容，请重新编辑。'),\r\n        (a.index = 1 * e.msg)\r\n      break\r\n\r\n    case 10807:\r\n      a.errmsg = '内容不能违反公众平台协议、相关法律法规和政策，请重新编辑。'\r\n      break\r\n\r\n    case 200003:\r\n      a.errmsg = '登录态超时，请重新登录。'\r\n      break\r\n\r\n    case 64513:\r\n      a.errmsg = '封面必须存在正文中，请检查封面'\r\n      break\r\n\r\n    case 64551:\r\n      a.errmsg = '请检查图文消息中的微视链接后重试。'\r\n      break\r\n\r\n    case 64552:\r\n      a.errmsg = '请检查阅读原文中的链接后重试。'\r\n      break\r\n\r\n    case 64553:\r\n      a.errmsg = '请不要在图文消息中插入超过5张卡券。请删减卡券后重试。'\r\n      break\r\n\r\n    case 64554:\r\n      a.errmsg = '在当前情况下不允许在图文消息中插入卡券，请删除卡券后重试。'\r\n      break\r\n\r\n    case 64555:\r\n      a.errmsg = '请检查图文消息卡片跳转的链接后重试。'\r\n      break\r\n\r\n    case 64556:\r\n      a.errmsg = '卡券不属于该公众号，请删除后重试'\r\n      break\r\n\r\n    case 64557:\r\n      a.errmsg = '卡券无效，请删除后重试。'\r\n      break\r\n\r\n    case 13002:\r\n      ;(a.errmsg = '该广告卡片已过期，删除后才可保存成功'),\r\n        (a.index = 1 * e.msg)\r\n      break\r\n\r\n    case 13003:\r\n      ;(a.errmsg = '已有文章插入过该广告卡片，一个广告卡片仅可插入一篇文章'),\r\n        (a.index = 1 * e.msg)\r\n      break\r\n\r\n    case 13004:\r\n      ;(a.errmsg = '该广告卡片与图文消息位置不一致'), (a.index = 1 * e.msg)\r\n      break\r\n\r\n    case 15801:\r\n    case 15802:\r\n    case 15803:\r\n    case 15804:\r\n    case 15805:\r\n    case 15806:\r\n      a.errmsg =\r\n        e.remind_wording ||\r\n        '你所编辑的内容可能含有违反微信公众平台平台协议、相关法律法规和政策的内容'\r\n      break\r\n\r\n    case 1530503:\r\n      a.errmsg = '请勿添加其他公众号的主页链接'\r\n      break\r\n\r\n    case 1530504:\r\n      a.errmsg = '请勿添加其他公众号的主页链接'\r\n      break\r\n\r\n    case 1530510:\r\n      a.errmsg = '链接已失效，请在手机端重新复制链接'\r\n      break\r\n\r\n    case 153007:\r\n    case 153008:\r\n    case 153009:\r\n    case 153010:\r\n      a.errmsg =\r\n        '很抱歉，原创声明不成功|你的文章内容未达到声明原创的要求，满足以下任一条件可发起声明：<br />1、文章文字字数大于300字，且自己创作的内容大于引用内容<br />2、文章文字字数小于300字，无视频，且图片（包括封面图）均为你已成功声明原创的图片<br />说明：上述要求中，文章文字字数不包含标点符号和空格，请知悉。'\r\n      break\r\n\r\n    case 153200:\r\n      a.errmsg = '无权限声明原创，取消声明后重试'\r\n      break\r\n\r\n    case 1530511:\r\n      a.errmsg = '链接已失效，请在手机端重新复制链接'\r\n      break\r\n\r\n    case 220001:\r\n      a.errmsg = '\"素材管理\"中的存储数量已达到上限，请删除后再操作。'\r\n      break\r\n\r\n    case 220002:\r\n      a.errmsg = '你的图片库已达到存储上限，请进行清理。'\r\n      break\r\n\r\n    case 153012:\r\n      a.errmsg = '请设置转载类型'\r\n      break\r\n\r\n    case 200042:\r\n      a.errmsg = '图文中包含的小程序素材不能多于50个、小程序帐号不能多于10个'\r\n      break\r\n\r\n    case 200043:\r\n      a.errmsg = '图文中包含没有关联的小程序，请删除后再保存'\r\n      break\r\n\r\n    case 64601:\r\n      a.errmsg = '一篇文章只能插入一个广告卡片'\r\n      break\r\n\r\n    case 64602:\r\n      a.errmsg = '尚未开通文中广告位，但文章中有广告'\r\n      break\r\n\r\n    case 64603:\r\n      a.errmsg = '文中广告前不足300字'\r\n      break\r\n\r\n    case 64604:\r\n      a.errmsg = '文中广告后不足300字'\r\n      break\r\n\r\n    case 64605:\r\n      a.errmsg = '文中不能同时插入文中广告和互选广告'\r\n      break\r\n\r\n    case 65101:\r\n      a.errmsg = '图文模版数量已达到上限，请删除后再操作'\r\n      break\r\n\r\n    case 64560:\r\n      a.errmsg = '请勿插入历史图文消息页链接'\r\n      break\r\n\r\n    case 64561:\r\n      a.errmsg = '请勿插入mp.weixin.qq.com域名下的非图文消息链接'\r\n      break\r\n\r\n    case 64562:\r\n      a.errmsg = '请勿插入非mp.weixin.qq.com域名的链接'\r\n      break\r\n\r\n    case 153013:\r\n      a.errmsg = '文章内含有投票，不能设置为开放转载'\r\n      break\r\n\r\n    case 153014:\r\n      a.errmsg = '文章内含有卡券，不能设置为开放转载'\r\n      break\r\n\r\n    case 153015:\r\n      a.errmsg = '文章内含有小程序链接，不能设置为开放转载'\r\n      break\r\n\r\n    case 153016:\r\n      a.errmsg = '文章内含有小程序链接，不能设置为开放转载'\r\n      break\r\n\r\n    case 153017:\r\n      a.errmsg = '文章内含有小程序卡片，不能设置为开放转载'\r\n      break\r\n\r\n    case 153018:\r\n      a.errmsg = '文章内含有商品，不能设置为开放转载'\r\n      break\r\n\r\n    case 153019:\r\n      a.errmsg = '文章内含有广告卡片，不能设置为开放转载'\r\n      break\r\n\r\n    case 153020:\r\n      a.errmsg = '文章内含有广告卡片，不能设置为开放转载'\r\n      break\r\n\r\n    case 153021:\r\n      a.errmsg = '文章内含有广告卡片，不能设置为开放转载'\r\n      break\r\n\r\n    case 153101:\r\n      a.errmsg = '含有原文已删除的转载文章，请删除后重试'\r\n      break\r\n\r\n    case 64707:\r\n      a.errmsg = '赞赏账户授权失效或者状态异常'\r\n      break\r\n\r\n    case 420001:\r\n      a.errmsg = '封面图不支持GIF，请更换'\r\n      break\r\n\r\n    case 353004:\r\n      a.errmsg = '不支持添加商品，请删除后重试'\r\n      break\r\n\r\n    case 442001:\r\n      a.errmsg = '帐号新建/编辑素材能力已被封禁，暂不可使用。'\r\n      break\r\n\r\n    default:\r\n      a.errmsg = '系统繁忙，请稍后重试'\r\n  }\r\n  return a\r\n}\r\n\n\n/***/ }),\n/* 14 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\nclass YiDian {\n  constructor() {\n    this.skipReadImage = true\n  }\n\n  async getMetaData() {\n    var res = await $.ajax({ url: 'https://mp.yidianzixun.com' })\n    var innerDoc = $(res)\n    var doc = $('<div>').append(innerDoc.clone())\n    var code = doc.find('#__val_').text()\n    console.log('YiDian', code)\n    // code = code.substring(code.indexOf(\"window.mpuser\"));\n    // eval(code);\n    var mpuser = new Function(code + '; return window.mpuser;')()\n    var commonData = Object.assign({}, mpuser)\n    console.log(commonData)\n    if (!commonData.id) {\n      throw new Error('未登录')\n    }\n    var metadata = {\n      uid: commonData.id,\n      title: commonData.media_name,\n      commonData: commonData,\n      avatar: commonData.media_pic,\n      type: 'yidian',\n      supportTypes: ['html'],\n      home: 'https://mp.yidianzixun.com',\n      icon: 'https://www.yidianzixun.com/favicon.ico',\n    }\n    return metadata\n  }\n\n  async addPost(post) {\n    return {\n      status: 'success',\n      post_id: 0,\n    }\n  }\n\n  async editPost(post_id, post) {\n    var res = await $.ajax({\n      url: 'https://mp.yidianzixun.com/model/Article',\n      type: 'POST',\n      dataType: 'JSON',\n      data: {\n        title: post.post_title,\n        cate: '',\n        cateB: '',\n        coverType: 'default',\n        covers: [],\n        content: post.post_content,\n        hasSubTitle: 0,\n        subTitle: '',\n        original: 0,\n        reward: 0,\n        videos: [],\n        audios: [],\n        votes: {\n          vote_id: '',\n          vote_options: [],\n          vote_end_time: '',\n          vote_title: '',\n          vote_type: 1,\n          isAdded: false,\n        },\n        images: [],\n        goods: [],\n        is_mobile: 0,\n        status: 0,\n        import_url: '',\n        import_hash: '',\n        image_urls: {},\n        minTimingHour: 3,\n        maxTimingDay: 7,\n        tags: [],\n        isPubed: false,\n        lastSaveTime: '',\n        dirty: false,\n        editorType: 'articleEditor',\n        activity_id: 0,\n        join_activity: 0,\n        notSaveToStore: true,\n      },\n    })\n\n    if (!res.id) {\n      throw new Error('同步错误' + JSON.stringify(res))\n    }\n    return {\n      status: 'success',\n      post_id: res.id,\n      draftLink: 'https://mp.yidianzixun.com/#/Writing/' + res.id,\n    }\n  }\n\n  async uploadFile(file) {\n    var src = file.src\n    var res = await $.get(\n      'https://mp.yidianzixun.com/api/getImageFromUrl?src=' +\n        encodeURIComponent(src)\n    )\n    // throw new Error('fuck');\n    if (res.status != 'success') {\n      throw new Error('图片上传失败 ' + src)\n    }\n    // http only\n    console.log('uploadFile', res)\n    return [\n      {\n        id: '',\n        object_key: '',\n        url: res.inner_addr,\n      },\n    ]\n  }\n\n  async preEditPost(post) {\n    // var div = $(\"<div>\");\n    // $(\"body\").append(div);\n    // div.html(post.content);\n    // // var org = $(post.content);\n    // // var doc = $('<div>').append(org.clone());\n    // var doc = div;\n    // var pres = doc.find(\"a\");\n    // for (let mindex = 0; mindex < pres.length; mindex++) {\n    //   const pre = pres.eq(mindex);\n    //   try {\n    //     pre.after(pre.html()).remove();\n    //   } catch (e) {}\n    // }\n    // var pres = doc.find(\"iframe\");\n    // for (let mindex = 0; mindex < pres.length; mindex++) {\n    //   const pre = pres.eq(mindex);\n    //   try {\n    //     pre.remove();\n    //   } catch (e) {}\n    // }\n    // post.content = $(\"<div>\")\n    //   .append(doc.clone())\n    //   .html();\n    // console.log(\"post\", post);\n  }\n\n  //   editImg(img, source) {\n  //     img.attr(\"web_uri\", source.images[0].origin_web_uri);\n  //   }\n  //   <img class=\"\" src=\"http://p2.pstatp.com/large/pgc-image/bc0a9fc8e595453083d85deb947c3d6e\" data-ic=\"false\" data-ic-uri=\"\" data-height=\"1333\" data-width=\"1000\" image_type=\"1\" web_uri=\"pgc-image/bc0a9fc8e595453083d85deb947c3d6e\" img_width=\"1000\" img_height=\"1333\"></img>\n}\n/* harmony export (immutable) */ __webpack_exports__[\"a\"] = YiDian;\n\n\n\n/***/ }),\n/* 15 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0_turndown__ = __webpack_require__(0);\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__tools_mtd__ = __webpack_require__(16);\nvar metaCache = null\r\n\r\n\r\n// import { markdownToDraft } from 'markdown-draft-js';\r\n\r\n// const axios = require('axios');\r\nconst draftJs = window.draftJs;\r\n// const convertToRaw = draftJs.convertToRaw\r\n// const htmlToDraft = require('html-to-draftjs').default\r\n// const ContentState = draftJs.ContentState\r\n\r\n\r\nconst ImageRegexp = /^!\\[([^\\]]*)]\\s*\\(([^)\"]+)( \"([^)\"]+)\")?\\)/\r\nconst imageBlock = (remarkable) => {\r\n  remarkable.block.ruler.before('paragraph', 'image', (state, startLine, endLine, silent) => {\r\n    const pos = state.bMarks[startLine] + state.tShift[startLine]\r\n    const max = state.eMarks[startLine]\r\n\r\n    if (pos >= max) {\r\n      return false\r\n    }\r\n    if (!state.src) {\r\n      return false\r\n    }\r\n    if (state.src[pos] !== '!') {\r\n      return false\r\n    }\r\n\r\n    var match = ImageRegexp.exec(state.src.slice(pos))\r\n    if (!match) {\r\n      return false\r\n    }\r\n\r\n    // in silent mode it shouldn't output any tokens or modify pending\r\n    if (!silent) {\r\n      state.tokens.push({\r\n        type: 'image_open',\r\n        src: match[2],\r\n        alt: match[1],\r\n        lines: [ startLine, state.line ],\r\n        level: state.level\r\n      })\r\n\r\n      state.tokens.push({\r\n        type: 'image_close',\r\n        level: state.level\r\n      })\r\n    }\r\n\r\n    state.line = startLine + 1\r\n\r\n    return true\r\n  })\r\n}\r\n\r\n\r\nfunction covertHTMLToDraftJs(html) {\r\n    const blocksFromHtml = htmlToDraft(html)\r\n    const { contentBlocks, entityMap } = blocksFromHtml;\r\n    const contentState = ContentState.createFromBlockArray(contentBlocks, entityMap);\r\n    const contentStateString = JSON.stringify(convertToRaw(contentState))\r\n    console.log('contentStateString', contentStateString)\r\n    return contentStateString\r\n}\r\n\r\nfunction getFormData(obj) {\r\n    var map = {};\r\n    obj.find('input').each(function() {\r\n        map[$(this).attr(\"name\")] = $(this).val();\r\n    });\r\n    return map\r\n}\r\n\r\n\r\nclass Douban {\r\n  constructor(config) {\r\n    this.config = config\r\n    this.meta = metaCache\r\n    this.name = 'douban'\r\n  }\r\n\r\n  async getMetaData() {\r\n    var res = await $.ajax({ url: 'https://www.douban.com/note/create' })\r\n    var innerDoc = $(res)\r\n    var doc = $('<div>').append(innerDoc.clone())\r\n    var configScript = innerDoc.filter(function(index, el){ return $(el).text().indexOf('_POST_PARAMS') > -1 });\r\n    if(configScript.length == 0) {\r\n        throw new Error('未登录')\r\n    }\r\n    var code = configScript.text()\r\n    var wx = new Function(\r\n        'Do ={}; Do.add = function() {} '+ code +\r\n        '; return {_USER_AVATAR: _USER_AVATAR, _USER_NAME: _USER_NAME, _NOTE_ID: _NOTE_ID, _TAGS: _TAGS, _POST_PARAMS: _POST_PARAMS};'\r\n    )();\r\n    console.log(code, wx)\r\n\r\n    var metadata = {\r\n      uid: wx._USER_NAME,\r\n      title: wx._USER_NAME,\r\n      commonData: wx,\r\n      avatar: wx._USER_AVATAR,\r\n      type: 'douban',\r\n      supportTypes: ['html'],\r\n      home: 'https://www.douban.com/note/create',\r\n      icon: 'https://img3.doubanio.com/favicon.ico',\r\n      form: getFormData(doc.find('#note-editor-form')),\r\n      _POST_PARAMS: wx._POST_PARAMS\r\n    }\r\n    metaCache = metadata\r\n    this.meta = metaCache\r\n    console.log('metaCache', metaCache)\r\n    return metadata\r\n  }\r\n\r\n  async addPost(post) {\r\n    return {\r\n      status: 'success',\r\n      post_id: 0,\r\n    }\r\n  }\r\n\r\n  async editPost(post_id, post) {\r\n    // console.log('editPost', post.post_thumbnail)\r\n    var turndownService = new __WEBPACK_IMPORTED_MODULE_0_turndown__[\"a\" /* default */]()\r\n    var markdown = turndownService.turndown(post.post_content)\r\n    console\r\n      .log(markdown)\r\n\r\n    // 保证图片换行\r\n    markdown = markdown.split(\"\\n\").map(_ => {\r\n      const imageBlocks = _.split('![]');\r\n      return imageBlocks.length > 1 ? imageBlocks.join('\\n![]') : _\r\n    }).join(\"\\n\");\r\n\r\n    const draftjsState = JSON.stringify(Object(__WEBPACK_IMPORTED_MODULE_1__tools_mtd__[\"a\" /* default */])(markdown, {\r\n      remarkablePlugins: [imageBlock],\r\n      blockTypes: {\r\n        image_open: function(item, generateUniqueKey) {\r\n          console.log('image_open', 'blockTypes', item)\r\n          var key = generateUniqueKey()\r\n          var blockEntities = {}\r\n          // ?#\r\n          var sourcePair =  item.src.split(\"?#\")\r\n          var rawSrc = sourcePair[0]\r\n          var sourceId = sourcePair[1]\r\n          if(sourcePair.length) {\r\n            item.src = rawSrc\r\n          }\r\n          var imageTemplate = {\r\n            id: sourceId,\r\n            src:  item.src,\r\n            thumb: item.src,\r\n            url: item.src,\r\n          }\r\n\r\n          blockEntities[key] = {\r\n            type: 'IMAGE',\r\n            mutability: 'IMMUTABLE',\r\n            data: imageTemplate,\r\n          }\r\n          return {\r\n            type: 'atomic',\r\n            blockEntities: blockEntities,\r\n            inlineStyleRanges: [],\r\n            // \"data\": {\r\n            //     \"page\": 0\r\n            // },\r\n            entityRanges: [\r\n              {\r\n                offset: 0,\r\n                length: 1,\r\n                key: key,\r\n              },\r\n            ],\r\n            text: ' ',\r\n          }\r\n        }\r\n      },\r\n      blockEntities: {\r\n        image: function (item) {\r\n          var sourcePair =  item.src.split(\"?#\")\r\n          if(sourcePair.length) {\r\n            var rawSrc = sourcePair[0]\r\n            var sourceId = sourcePair[1]\r\n            item.id = sourceId\r\n            item.src = rawSrc\r\n          }\r\n          console.log('image_open', 'blockEntities', item)\r\n          return {\r\n            type: 'IMAGE',\r\n            mutability: 'IMMUTABLE',\r\n            data: item\r\n          }\r\n        }\r\n      }\r\n    }));\r\n    console.log(draftjsState)\r\n\r\n    var state = this.config.state;\r\n\r\n    var requestUrl = 'https://www.douban.com/j/note/autosave';\r\n    var draftLink = 'https://www.douban.com/note/create';\r\n    var requestBody = {\r\n      is_rich: 1,\r\n      note_id: this.meta.form.note_id,\r\n      note_title: post.post_title,\r\n      note_text: draftjsState,\r\n      introduction: '',\r\n      note_privacy: 'P',\r\n      cannot_reply: null,\r\n      author_tags: null,\r\n      accept_donation: null,\r\n      donation_notice: null,\r\n      is_original: null,\r\n      ck: this.meta.form.ck\r\n    }\r\n\r\n    // https://music.douban.com/subject/24856133/new_review\r\n    // music review\r\n    // https://music.douban.com/j/review/create\r\n    // is_rich: 1\r\n    // topic_id: \r\n    // review[subject_id]: 24856133\r\n    // review[title]: aaa\r\n    // review[introduction]: \r\n    // review[text]: {\"entityMap\":{},\"blocks\":[{\"key\":\"9riq1\",\"text\":\"aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa\",\"type\":\"unstyled\",\"depth\":0,\"inlineStyleRanges\":[],\"entityRanges\":[],\"data\":{\"page\":0}}]}\r\n    // review[rating]: \r\n    // review[spoiler]: \r\n    // review[donate]: \r\n    // review[original]: \r\n    // ck: O4jk\r\n    if(state.is_review) {\r\n      if(state.subject == 'music') {\r\n        draftLink = state.url;\r\n        requestUrl = 'https://music.douban.com/j/review/create'\r\n        requestBody = {\r\n          is_rich: 1,\r\n          topic_id: '',\r\n          review: {\r\n            subject_id: state.id,\r\n            title:  post.post_title,\r\n            introduction: '',\r\n            text: draftjsState,\r\n            rating: '',\r\n            spoiler: '',\r\n            donate: '',\r\n            original: ''\r\n          },\r\n          ck: this.meta.form.ck\r\n        }\r\n      }\r\n    }\r\n    console.log('state', requestBody)\r\n    // return {\r\n    //   status: 'success',\r\n    //   post_id: 'test',\r\n    //   draftLink: draftLink,\r\n    // }\r\n    // const draftjsState = covertHTMLToDraftJs(post.post_content)\r\n    var res = await $.ajax({\r\n      url: requestUrl,\r\n      type: 'POST',\r\n      dataType: 'JSON',\r\n      data: requestBody,\r\n    })\r\n\r\n    if(res.url) {\r\n      draftLink = res.url\r\n    }\r\n\r\n    return {\r\n      status: 'success',\r\n      post_id: this.meta.form.note_id,\r\n      draftLink: draftLink,\r\n    }\r\n  }\r\n\r\n  editImg(img, source) {\r\n    img.attr('raw-data', JSON.stringify(source.raw))\r\n  }\r\n\r\n  async uploadFile(file) {\r\n\r\n    // https://music.douban.com/j/review/upload_image\r\n    var requestUrl = 'https://www.douban.com/j/note/add_photo';\r\n    var state = this.config.state;\r\n    var formdata = new FormData()\r\n    var blob = new Blob([file.bits], {\r\n      type: file.type\r\n    });\r\n\r\n    if(state.is_review) {\r\n      if(state.subject == 'music') {\r\n        requestUrl =  'https://music.douban.com/j/review/upload_image';\r\n        formdata.append('review_id', '')\r\n        formdata.append('picfile', blob)\r\n      }\r\n    } else {\r\n      formdata.append('note_id', this.meta.form.note_id)\r\n      formdata.append('image_file', blob)\r\n    }\r\n\r\n    formdata.append('ck', this.meta.form.ck)\r\n    formdata.append('upload_auth_token', this.meta._POST_PARAMS.siteCookie.value)\r\n   \r\n    var res = await axios({\r\n      url: requestUrl,\r\n      method: 'post',\r\n      data: formdata,\r\n      headers: { 'Content-Type': 'multipart/form-data' },\r\n    })\r\n\r\n    var url = res.data.photo.url\r\n    if(!res.data.photo) {\r\n        console.log(res.data);\r\n        throw new Error('upload failed')\r\n    }\r\n    //  return url;\r\n    return [\r\n      {\r\n        id: res.data.photo.id,\r\n        object_key: res.data.photo.id,\r\n        url: url + \"?#\" + res.data.photo.id,\r\n        raw: res.data\r\n      },\r\n    ]\r\n  }\r\n\r\n}\n/* harmony export (immutable) */ __webpack_exports__[\"a\"] = Douban;\n\r\n\n\n/***/ }),\n/* 16 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0_remarkable__ = __webpack_require__(17);\n\r\n\r\nconst TRAILING_NEW_LINE = /\\n$/;\r\n\r\n// In DraftJS, string lengths are calculated differently than in JS itself (due\r\n// to surrogate pairs). Instead of importing the entire UnicodeUtils file from\r\n// FBJS, we use a simpler alternative, in the form of `Array.from`.\r\n//\r\n// Alternative:  const { strlen } = require('fbjs/lib/UnicodeUtils');\r\nfunction strlen(str) {\r\n  return Array.from(str).length;\r\n}\r\n\r\n// Block level items, key is Remarkable's key for them, value returned is\r\n// A function that generates the raw draftjs key and block data.\r\n//\r\n// Why a function? Because in some cases (headers) we need additional information\r\n// before we can determine the exact key to return. And blocks may also return data\r\nconst DefaultBlockTypes = {\r\n  paragraph_open: function (item) {\r\n    return {\r\n      type: 'unstyled',\r\n      text: '',\r\n      entityRanges: [],\r\n      inlineStyleRanges: []\r\n    };\r\n  },\r\n\r\n  blockquote_open: function (item) {\r\n    return {\r\n      type: 'blockquote',\r\n      text: ''\r\n    };\r\n  },\r\n\r\n  ordered_list_item_open: function () {\r\n    return {\r\n      type: 'ordered-list-item',\r\n      text: ''\r\n    };\r\n  },\r\n\r\n  unordered_list_item_open: function () {\r\n    return {\r\n      type: 'unordered-list-item',\r\n      text: ''\r\n    };\r\n  },\r\n\r\n  fence: function (item) {\r\n    return {\r\n      type: 'code-block',\r\n      data: {\r\n        language: item.params || ''\r\n      },\r\n      text: (item.content || '').replace(TRAILING_NEW_LINE, ''), // remarkable seems to always append an erronious trailing newline to its codeblock content, so we need to trim it out.\r\n      entityRanges: [],\r\n      inlineStyleRanges: []\r\n    };\r\n  },\r\n\r\n  heading_open: function (item) {\r\n    var type = 'header-' + ({\r\n      1: 'one',\r\n      2: 'two',\r\n      3: 'three',\r\n      4: 'four',\r\n      5: 'five',\r\n      6: 'six'\r\n    })[item.hLevel];\r\n\r\n    return {\r\n      type: type,\r\n      text: ''\r\n    };\r\n  }\r\n};\r\n\r\n// Entity types. These are things like links or images that require\r\n// additional data and will be added to the `entityMap`\r\n// again. In this case, key is remarkable key, value is\r\n// meethod that returns the draftjs key + any data needed.\r\nconst DefaultBlockEntities = {\r\n  link_open: function (item) {\r\n    return {\r\n      type: 'LINK',\r\n      mutability: 'MUTABLE',\r\n      data: {\r\n        url: item.href,\r\n        href: item.href\r\n      }\r\n    };\r\n  }\r\n};\r\n\r\n// Entity styles. Simple Inline styles that aren't added to entityMap\r\n// key is remarkable key, value is draftjs raw key\r\nconst DefaultBlockStyles = {\r\n  strong_open: 'BOLD',\r\n  em_open: 'ITALIC',\r\n  code: 'CODE'\r\n};\r\n\r\n// Key generator for entityMap items\r\nvar idCounter = -1;\r\nfunction generateUniqueKey() {\r\n  idCounter++;\r\n  return idCounter;\r\n}\r\n\r\n/*\r\n * Handle inline content in a block level item\r\n * parses for BlockEntities (links, images) and BlockStyles (em, strong)\r\n * doesn't handle block level items (blockquote, ordered list, etc)\r\n *\r\n * @param <Object> inlineItem - single object from remarkable data representation of markdown\r\n * @param <Object> BlockEntities - key-value object of mappable block entity items. Passed in as param so users can include their own custom stuff\r\n * @param <Object> BlockStyles - key-value object of mappable block styles items. Passed in as param so users can include their own custom stuff\r\n *\r\n * @return <Object>\r\n *  content: Entire text content for the inline item,\r\n *  blockEntities: New block eneities to be added to global block entity map\r\n *  blockEntityRanges: block-level representation of block entities including key to access the block entity from the global map\r\n *  blockStyleRanges: block-level representation of styles (eg strong, em)\r\n*/\r\nfunction parseInline(inlineItem, BlockEntities, BlockStyles) {\r\n  var content = '', blockEntities = {}, blockEntityRanges = [], blockInlineStyleRanges = [];\r\n  inlineItem.children.forEach(function (child) {\r\n    if (child.type === 'text') {\r\n      content += child.content;\r\n    } else if (child.type === 'softbreak') {\r\n      content += '\\n';\r\n    } else if (child.type === 'hardbreak') {\r\n      content += '\\n';\r\n    } else if (BlockStyles[child.type]) {\r\n      var key = generateUniqueKey();\r\n      var styleBlock = {\r\n        offset: strlen(content) || 0,\r\n        length: 0,\r\n        style: BlockStyles[child.type]\r\n      };\r\n\r\n      // Edge case hack because code items don't have inline content or open/close, unlike everything else\r\n      if (child.type === 'code') {\r\n        styleBlock.length = strlen(child.content);\r\n        content += child.content;\r\n      }\r\n\r\n      blockInlineStyleRanges.push(styleBlock);\r\n    } else if (BlockEntities[child.type]) {\r\n      var key = generateUniqueKey();\r\n\r\n      blockEntities[key] = BlockEntities[child.type](child);\r\n\r\n      blockEntityRanges.push({\r\n        offset: strlen(content) || 0,\r\n        length: 0,\r\n        key: key\r\n      });\r\n    } else if (child.type.indexOf('_close') !== -1 && BlockEntities[child.type.replace('_close', '_open')]) {\r\n      blockEntityRanges[blockEntityRanges.length - 1].length = strlen(content) - blockEntityRanges[blockEntityRanges.length - 1].offset;\r\n    } else if (child.type.indexOf('_close') !== -1 && BlockStyles[child.type.replace('_close', '_open')]) {\r\n      var type = BlockStyles[child.type.replace('_close', '_open')]\r\n      blockInlineStyleRanges = blockInlineStyleRanges\r\n        .map(style => {\r\n          if (style.length === 0 && style.style === type) {\r\n            style.length = strlen(content) - style.offset;\r\n          }\r\n          return style;\r\n        });\r\n    }\r\n  });\r\n\r\n  return {content, blockEntities, blockEntityRanges, blockInlineStyleRanges};\r\n}\r\n\r\n/**\r\n * Convert markdown into raw draftjs object\r\n *\r\n * @param {String} markdown - markdown to convert into raw draftjs object\r\n * @param {Object} options - optional additional data, see readme for what options can be passed in.\r\n *\r\n * @return {Object} rawDraftObject\r\n**/\r\nfunction markdownToDraft(string, options = {}) {\r\n  const remarkablePreset = options.remarkablePreset || options.remarkableOptions;\r\n  const remarkableOptions = typeof options.remarkableOptions === 'object' ? options.remarkableOptions : null;\r\n  const md = new __WEBPACK_IMPORTED_MODULE_0_remarkable__[\"a\" /* Remarkable */](remarkablePreset, remarkableOptions);\r\n\r\n  // if tables are not explicitly enabled, disable them by default\r\n  if (\r\n    !remarkableOptions ||\r\n    !remarkableOptions.enable ||\r\n    !remarkableOptions.enable.block ||\r\n    remarkableOptions.enable.block !== 'table' ||\r\n    remarkableOptions.enable.block.includes('table') === false\r\n  ) {\r\n    md.block.ruler.disable('table');\r\n  }\r\n\r\n  // disable the specified rules\r\n  if (remarkableOptions && remarkableOptions.disable) {\r\n    for (let [key, value] of Object.entries(remarkableOptions.disable)) {\r\n      md[key].ruler.disable(value);\r\n    }\r\n  }\r\n\r\n  // enable the specified rules\r\n  if (remarkableOptions && remarkableOptions.enable) {\r\n    for (let [key, value] of Object.entries(remarkableOptions.enable)) {\r\n      md[key].ruler.enable(value);\r\n    }\r\n  }\r\n\r\n  // If users want to define custom remarkable plugins for custom markdown, they can be added here\r\n  if (options.remarkablePlugins) {\r\n    options.remarkablePlugins.forEach(function (plugin) {\r\n      md.use(plugin, {});\r\n    });\r\n  }\r\n\r\n  var blocks = []; // blocks will be returned as part of the final draftjs raw object\r\n  var entityMap = {}; // entitymap will be returned as part of the final draftjs raw object\r\n  var parsedData = md.parse(string, {}); // remarkable js takes markdown and makes it an array of style objects for us to easily parse\r\n  var currentListType = null; // Because of how remarkable's data is formatted, we need to cache what kind of list we're currently dealing with\r\n  var previousBlockEndingLine = 0;\r\n\r\n  // Allow user to define custom BlockTypes and Entities if they so wish\r\n  const BlockTypes = Object.assign({}, DefaultBlockTypes, options.blockTypes || {});\r\n  const BlockEntities = Object.assign({}, DefaultBlockEntities, options.blockEntities || {});\r\n  const BlockStyles = Object.assign({}, DefaultBlockStyles, options.blockStyles || {});\r\n\r\n  console.log('parsedData', parsedData)\r\n\r\n  parsedData.forEach(function (item) {\r\n    // Because of how remarkable's data is formatted, we need to cache what kind of list we're currently dealing with\r\n    if (item.type === 'bullet_list_open') {\r\n      currentListType = 'unordered_list_item_open';\r\n    } else if (item.type === 'ordered_list_open') {\r\n      currentListType = 'ordered_list_item_open';\r\n    }\r\n\r\n    var itemType = item.type;\r\n    if (itemType === 'list_item_open') {\r\n      itemType = currentListType;\r\n    }\r\n\r\n    if (itemType === 'inline') {\r\n      // Parse inline content and apply it to the most recently created block level item,\r\n      // which is where the inline content will belong.\r\n      var {content, blockEntities, blockEntityRanges, blockInlineStyleRanges} = parseInline(item, BlockEntities, BlockStyles);\r\n      var blockToModify = blocks[blocks.length - 1];\r\n      blockToModify.text = content;\r\n      blockToModify.inlineStyleRanges = blockInlineStyleRanges;\r\n      blockToModify.entityRanges = blockEntityRanges;\r\n\r\n      // The entity map is a master object separate from the block so just add any entities created for this block to the master object\r\n      Object.assign(entityMap, blockEntities);\r\n    } else if ((itemType.indexOf('_open') !== -1 || itemType === 'fence' || itemType === 'hr') && BlockTypes[itemType]) {\r\n      var depth = 0;\r\n      var block;\r\n\r\n      if (item.level > 0) {\r\n        depth = Math.floor(item.level / 2);\r\n      }\r\n\r\n      // var key = generateUniqueKey();\r\n      // blockEntities[key] = BlockEntities[child.type](child);\r\n      // blockEntityRanges.push({\r\n      //   offset: strlen(content) || 0,\r\n      //   length: 0,\r\n      //   key: key\r\n      // });\r\n      // Draftjs only supports 1 level of blocks, hence the item.level === 0 check\r\n      // List items will always be at least `level==1` though so we need a separate check for that\r\n      // If there’s nested block level items deeper than that, we need to make sure we capture this by cloning the topmost block\r\n      // otherwise we’ll accidentally overwrite its text. (eg if there's a blockquote with 3 nested paragraphs with inline text, without this check, only the last paragraph would be reflected)\r\n      if (item.level === 0 || item.type === 'list_item_open') {\r\n        block = Object.assign({\r\n          depth: depth\r\n        }, BlockTypes[itemType](item, generateUniqueKey));\r\n        \r\n        if(block.blockEntities) {\r\n          Object.assign(entityMap, block.blockEntities);\r\n          delete block.blockEntities\r\n        }\r\n      } else if (item.level > 0 && blocks[blocks.length - 1].text) {\r\n        block = Object.assign({}, blocks[blocks.length - 1]);\r\n      }\r\n\r\n      if (block && options.preserveNewlines) {\r\n        // Re: previousBlockEndingLine.... omg.\r\n        // So remarkable strips out empty newlines and doesn't make any entities to parse to restore them\r\n        // the only solution I could find is that there's a 2-value array on each block item called \"lines\" which is the start and end line of the block element.\r\n        // by keeping track of the PREVIOUS block element ending line and the NEXT block element starting line, we can find the difference between the new lines and insert\r\n        // an appropriate number of extra paragraphs to re-create those newlines in draftjs.\r\n        // This is probably my least favourite thing in this file, but not sure what could be better.\r\n        var totalEmptyParagraphsToCreate = item.lines[0] - previousBlockEndingLine;\r\n        for (var i = 0; i < totalEmptyParagraphsToCreate; i++) {\r\n          blocks.push(DefaultBlockTypes.paragraph_open());\r\n        }\r\n      }\r\n\r\n      if (block) {\r\n        previousBlockEndingLine = item.lines[1];\r\n        blocks.push(block);\r\n      }\r\n    }\r\n\r\n  });\r\n\r\n  // EditorState.createWithContent will error if there's no blocks defined\r\n  // Remarkable returns an empty array though. So we have to generate a 'fake'\r\n  // empty block in this case. 😑\r\n  if (!blocks.length) {\r\n    blocks.push(DefaultBlockTypes.paragraph_open());\r\n  }\r\n\r\n  return {\r\n    entityMap,\r\n    blocks\r\n  };\r\n}\r\n\r\n/* harmony default export */ __webpack_exports__[\"a\"] = (markdownToDraft);\n\n/***/ }),\n/* 17 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"a\", function() { return Remarkable; });\n/* unused harmony export utils */\nvar textarea;\n\nfunction decodeEntity(name) {\n  textarea = textarea || document.createElement('textarea');\n  textarea.innerHTML = '&' + name + ';';\n  return textarea.value;\n}\n\n/**\n * Utility functions\n */\n\nfunction typeOf(obj) {\n  return Object.prototype.toString.call(obj);\n}\n\nfunction isString(obj) {\n  return typeOf(obj) === '[object String]';\n}\n\nvar hasOwn = Object.prototype.hasOwnProperty;\n\nfunction has(object, key) {\n  return object\n    ? hasOwn.call(object, key)\n    : false;\n}\n\n// Extend objects\n//\nfunction assign(obj /*from1, from2, from3, ...*/) {\n  var sources = [].slice.call(arguments, 1);\n\n  sources.forEach(function (source) {\n    if (!source) { return; }\n\n    if (typeof source !== 'object') {\n      throw new TypeError(source + 'must be object');\n    }\n\n    Object.keys(source).forEach(function (key) {\n      obj[key] = source[key];\n    });\n  });\n\n  return obj;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\nvar UNESCAPE_MD_RE = /\\\\([\\\\!\"#$%&'()*+,.\\/:;<=>?@[\\]^_`{|}~-])/g;\n\nfunction unescapeMd(str) {\n  if (str.indexOf('\\\\') < 0) { return str; }\n  return str.replace(UNESCAPE_MD_RE, '$1');\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\nfunction isValidEntityCode(c) {\n  /*eslint no-bitwise:0*/\n  // broken sequence\n  if (c >= 0xD800 && c <= 0xDFFF) { return false; }\n  // never used\n  if (c >= 0xFDD0 && c <= 0xFDEF) { return false; }\n  if ((c & 0xFFFF) === 0xFFFF || (c & 0xFFFF) === 0xFFFE) { return false; }\n  // control codes\n  if (c >= 0x00 && c <= 0x08) { return false; }\n  if (c === 0x0B) { return false; }\n  if (c >= 0x0E && c <= 0x1F) { return false; }\n  if (c >= 0x7F && c <= 0x9F) { return false; }\n  // out of range\n  if (c > 0x10FFFF) { return false; }\n  return true;\n}\n\nfunction fromCodePoint(c) {\n  /*eslint no-bitwise:0*/\n  if (c > 0xffff) {\n    c -= 0x10000;\n    var surrogate1 = 0xd800 + (c >> 10),\n        surrogate2 = 0xdc00 + (c & 0x3ff);\n\n    return String.fromCharCode(surrogate1, surrogate2);\n  }\n  return String.fromCharCode(c);\n}\n\nvar NAMED_ENTITY_RE   = /&([a-z#][a-z0-9]{1,31});/gi;\nvar DIGITAL_ENTITY_TEST_RE = /^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))/i;\n\nfunction replaceEntityPattern(match, name) {\n  var code = 0;\n  var decoded = decodeEntity(name);\n\n  if (name !== decoded) {\n    return decoded;\n  } else if (name.charCodeAt(0) === 0x23/* # */ && DIGITAL_ENTITY_TEST_RE.test(name)) {\n    code = name[1].toLowerCase() === 'x' ?\n      parseInt(name.slice(2), 16)\n    :\n      parseInt(name.slice(1), 10);\n    if (isValidEntityCode(code)) {\n      return fromCodePoint(code);\n    }\n  }\n  return match;\n}\n\nfunction replaceEntities(str) {\n  if (str.indexOf('&') < 0) { return str; }\n\n  return str.replace(NAMED_ENTITY_RE, replaceEntityPattern);\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\nvar HTML_ESCAPE_TEST_RE = /[&<>\"]/;\nvar HTML_ESCAPE_REPLACE_RE = /[&<>\"]/g;\nvar HTML_REPLACEMENTS = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;'\n};\n\nfunction replaceUnsafeChar(ch) {\n  return HTML_REPLACEMENTS[ch];\n}\n\nfunction escapeHtml(str) {\n  if (HTML_ESCAPE_TEST_RE.test(str)) {\n    return str.replace(HTML_ESCAPE_REPLACE_RE, replaceUnsafeChar);\n  }\n  return str;\n}\n\nvar utils = /*#__PURE__*/Object.freeze({\n  isString: isString,\n  has: has,\n  assign: assign,\n  unescapeMd: unescapeMd,\n  isValidEntityCode: isValidEntityCode,\n  fromCodePoint: fromCodePoint,\n  replaceEntities: replaceEntities,\n  escapeHtml: escapeHtml\n});\n\n/**\n * Renderer rules cache\n */\n\nvar rules = {};\n\n/**\n * Blockquotes\n */\n\nrules.blockquote_open = function(/* tokens, idx, options, env */) {\n  return '<blockquote>\\n';\n};\n\nrules.blockquote_close = function(tokens, idx /*, options, env */) {\n  return '</blockquote>' + getBreak(tokens, idx);\n};\n\n/**\n * Code\n */\n\nrules.code = function(tokens, idx /*, options, env */) {\n  if (tokens[idx].block) {\n    return '<pre><code>' + escapeHtml(tokens[idx].content) + '</code></pre>' + getBreak(tokens, idx);\n  }\n  return '<code>' + escapeHtml(tokens[idx].content) + '</code>';\n};\n\n/**\n * Fenced code blocks\n */\n\nrules.fence = function(tokens, idx, options, env, instance) {\n  var token = tokens[idx];\n  var langClass = '';\n  var langPrefix = options.langPrefix;\n  var langName = '', fences, fenceName;\n  var highlighted;\n\n  if (token.params) {\n\n    //\n    // ```foo bar\n    //\n    // Try custom renderer \"foo\" first. That will simplify overwrite\n    // for diagrams, latex, and any other fenced block with custom look\n    //\n\n    fences = token.params.split(/\\s+/g);\n    fenceName = fences.join(' ');\n\n    if (has(instance.rules.fence_custom, fences[0])) {\n      return instance.rules.fence_custom[fences[0]](tokens, idx, options, env, instance);\n    }\n\n    langName = escapeHtml(replaceEntities(unescapeMd(fenceName)));\n    langClass = ' class=\"' + langPrefix + langName + '\"';\n  }\n\n  if (options.highlight) {\n    highlighted = options.highlight.apply(options.highlight, [ token.content ].concat(fences))\n      || escapeHtml(token.content);\n  } else {\n    highlighted = escapeHtml(token.content);\n  }\n\n  return '<pre><code' + langClass + '>'\n        + highlighted\n        + '</code></pre>'\n        + getBreak(tokens, idx);\n};\n\nrules.fence_custom = {};\n\n/**\n * Headings\n */\n\nrules.heading_open = function(tokens, idx /*, options, env */) {\n  return '<h' + tokens[idx].hLevel + '>';\n};\nrules.heading_close = function(tokens, idx /*, options, env */) {\n  return '</h' + tokens[idx].hLevel + '>\\n';\n};\n\n/**\n * Horizontal rules\n */\n\nrules.hr = function(tokens, idx, options /*, env */) {\n  return (options.xhtmlOut ? '<hr />' : '<hr>') + getBreak(tokens, idx);\n};\n\n/**\n * Bullets\n */\n\nrules.bullet_list_open = function(/* tokens, idx, options, env */) {\n  return '<ul>\\n';\n};\nrules.bullet_list_close = function(tokens, idx /*, options, env */) {\n  return '</ul>' + getBreak(tokens, idx);\n};\n\n/**\n * List items\n */\n\nrules.list_item_open = function(/* tokens, idx, options, env */) {\n  return '<li>';\n};\nrules.list_item_close = function(/* tokens, idx, options, env */) {\n  return '</li>\\n';\n};\n\n/**\n * Ordered list items\n */\n\nrules.ordered_list_open = function(tokens, idx /*, options, env */) {\n  var token = tokens[idx];\n  var order = token.order > 1 ? ' start=\"' + token.order + '\"' : '';\n  return '<ol' + order + '>\\n';\n};\nrules.ordered_list_close = function(tokens, idx /*, options, env */) {\n  return '</ol>' + getBreak(tokens, idx);\n};\n\n/**\n * Paragraphs\n */\n\nrules.paragraph_open = function(tokens, idx /*, options, env */) {\n  return tokens[idx].tight ? '' : '<p>';\n};\nrules.paragraph_close = function(tokens, idx /*, options, env */) {\n  var addBreak = !(tokens[idx].tight && idx && tokens[idx - 1].type === 'inline' && !tokens[idx - 1].content);\n  return (tokens[idx].tight ? '' : '</p>') + (addBreak ? getBreak(tokens, idx) : '');\n};\n\n/**\n * Links\n */\n\nrules.link_open = function(tokens, idx, options /* env */) {\n  var title = tokens[idx].title ? (' title=\"' + escapeHtml(replaceEntities(tokens[idx].title)) + '\"') : '';\n  var target = options.linkTarget ? (' target=\"' + options.linkTarget + '\"') : '';\n  return '<a href=\"' + escapeHtml(tokens[idx].href) + '\"' + title + target + '>';\n};\nrules.link_close = function(/* tokens, idx, options, env */) {\n  return '</a>';\n};\n\n/**\n * Images\n */\n\nrules.image = function(tokens, idx, options /*, env */) {\n  var src = ' src=\"' + escapeHtml(tokens[idx].src) + '\"';\n  var title = tokens[idx].title ? (' title=\"' + escapeHtml(replaceEntities(tokens[idx].title)) + '\"') : '';\n  var alt = ' alt=\"' + (tokens[idx].alt ? escapeHtml(replaceEntities(unescapeMd(tokens[idx].alt))) : '') + '\"';\n  var suffix = options.xhtmlOut ? ' /' : '';\n  return '<img' + src + alt + title + suffix + '>';\n};\n\n/**\n * Tables\n */\n\nrules.table_open = function(/* tokens, idx, options, env */) {\n  return '<table>\\n';\n};\nrules.table_close = function(/* tokens, idx, options, env */) {\n  return '</table>\\n';\n};\nrules.thead_open = function(/* tokens, idx, options, env */) {\n  return '<thead>\\n';\n};\nrules.thead_close = function(/* tokens, idx, options, env */) {\n  return '</thead>\\n';\n};\nrules.tbody_open = function(/* tokens, idx, options, env */) {\n  return '<tbody>\\n';\n};\nrules.tbody_close = function(/* tokens, idx, options, env */) {\n  return '</tbody>\\n';\n};\nrules.tr_open = function(/* tokens, idx, options, env */) {\n  return '<tr>';\n};\nrules.tr_close = function(/* tokens, idx, options, env */) {\n  return '</tr>\\n';\n};\nrules.th_open = function(tokens, idx /*, options, env */) {\n  var token = tokens[idx];\n  return '<th'\n    + (token.align ? ' style=\"text-align:' + token.align + '\"' : '')\n    + '>';\n};\nrules.th_close = function(/* tokens, idx, options, env */) {\n  return '</th>';\n};\nrules.td_open = function(tokens, idx /*, options, env */) {\n  var token = tokens[idx];\n  return '<td'\n    + (token.align ? ' style=\"text-align:' + token.align + '\"' : '')\n    + '>';\n};\nrules.td_close = function(/* tokens, idx, options, env */) {\n  return '</td>';\n};\n\n/**\n * Bold\n */\n\nrules.strong_open = function(/* tokens, idx, options, env */) {\n  return '<strong>';\n};\nrules.strong_close = function(/* tokens, idx, options, env */) {\n  return '</strong>';\n};\n\n/**\n * Italicize\n */\n\nrules.em_open = function(/* tokens, idx, options, env */) {\n  return '<em>';\n};\nrules.em_close = function(/* tokens, idx, options, env */) {\n  return '</em>';\n};\n\n/**\n * Strikethrough\n */\n\nrules.del_open = function(/* tokens, idx, options, env */) {\n  return '<del>';\n};\nrules.del_close = function(/* tokens, idx, options, env */) {\n  return '</del>';\n};\n\n/**\n * Insert\n */\n\nrules.ins_open = function(/* tokens, idx, options, env */) {\n  return '<ins>';\n};\nrules.ins_close = function(/* tokens, idx, options, env */) {\n  return '</ins>';\n};\n\n/**\n * Highlight\n */\n\nrules.mark_open = function(/* tokens, idx, options, env */) {\n  return '<mark>';\n};\nrules.mark_close = function(/* tokens, idx, options, env */) {\n  return '</mark>';\n};\n\n/**\n * Super- and sub-script\n */\n\nrules.sub = function(tokens, idx /*, options, env */) {\n  return '<sub>' + escapeHtml(tokens[idx].content) + '</sub>';\n};\nrules.sup = function(tokens, idx /*, options, env */) {\n  return '<sup>' + escapeHtml(tokens[idx].content) + '</sup>';\n};\n\n/**\n * Breaks\n */\n\nrules.hardbreak = function(tokens, idx, options /*, env */) {\n  return options.xhtmlOut ? '<br />\\n' : '<br>\\n';\n};\nrules.softbreak = function(tokens, idx, options /*, env */) {\n  return options.breaks ? (options.xhtmlOut ? '<br />\\n' : '<br>\\n') : '\\n';\n};\n\n/**\n * Text\n */\n\nrules.text = function(tokens, idx /*, options, env */) {\n  return escapeHtml(tokens[idx].content);\n};\n\n/**\n * Content\n */\n\nrules.htmlblock = function(tokens, idx /*, options, env */) {\n  return tokens[idx].content;\n};\nrules.htmltag = function(tokens, idx /*, options, env */) {\n  return tokens[idx].content;\n};\n\n/**\n * Abbreviations, initialism\n */\n\nrules.abbr_open = function(tokens, idx /*, options, env */) {\n  return '<abbr title=\"' + escapeHtml(replaceEntities(tokens[idx].title)) + '\">';\n};\nrules.abbr_close = function(/* tokens, idx, options, env */) {\n  return '</abbr>';\n};\n\n/**\n * Footnotes\n */\n\nrules.footnote_ref = function(tokens, idx) {\n  var n = Number(tokens[idx].id + 1).toString();\n  var id = 'fnref' + n;\n  if (tokens[idx].subId > 0) {\n    id += ':' + tokens[idx].subId;\n  }\n  return '<sup class=\"footnote-ref\"><a href=\"#fn' + n + '\" id=\"' + id + '\">[' + n + ']</a></sup>';\n};\nrules.footnote_block_open = function(tokens, idx, options) {\n  var hr = options.xhtmlOut\n    ? '<hr class=\"footnotes-sep\" />\\n'\n    : '<hr class=\"footnotes-sep\">\\n';\n  return hr + '<section class=\"footnotes\">\\n<ol class=\"footnotes-list\">\\n';\n};\nrules.footnote_block_close = function() {\n  return '</ol>\\n</section>\\n';\n};\nrules.footnote_open = function(tokens, idx) {\n  var id = Number(tokens[idx].id + 1).toString();\n  return '<li id=\"fn' + id + '\"  class=\"footnote-item\">';\n};\nrules.footnote_close = function() {\n  return '</li>\\n';\n};\nrules.footnote_anchor = function(tokens, idx) {\n  var n = Number(tokens[idx].id + 1).toString();\n  var id = 'fnref' + n;\n  if (tokens[idx].subId > 0) {\n    id += ':' + tokens[idx].subId;\n  }\n  return ' <a href=\"#' + id + '\" class=\"footnote-backref\">↩</a>';\n};\n\n/**\n * Definition lists\n */\n\nrules.dl_open = function() {\n  return '<dl>\\n';\n};\nrules.dt_open = function() {\n  return '<dt>';\n};\nrules.dd_open = function() {\n  return '<dd>';\n};\nrules.dl_close = function() {\n  return '</dl>\\n';\n};\nrules.dt_close = function() {\n  return '</dt>\\n';\n};\nrules.dd_close = function() {\n  return '</dd>\\n';\n};\n\n/**\n * Helper functions\n */\n\nfunction nextToken(tokens, idx) {\n  if (++idx >= tokens.length - 2) {\n    return idx;\n  }\n  if ((tokens[idx].type === 'paragraph_open' && tokens[idx].tight) &&\n      (tokens[idx + 1].type === 'inline' && tokens[idx + 1].content.length === 0) &&\n      (tokens[idx + 2].type === 'paragraph_close' && tokens[idx + 2].tight)) {\n    return nextToken(tokens, idx + 2);\n  }\n  return idx;\n}\n\n/**\n * Check to see if `\\n` is needed before the next token.\n *\n * @param  {Array} `tokens`\n * @param  {Number} `idx`\n * @return {String} Empty string or newline\n * @api private\n */\n\nvar getBreak = rules.getBreak = function getBreak(tokens, idx) {\n  idx = nextToken(tokens, idx);\n  if (idx < tokens.length && tokens[idx].type === 'list_item_close') {\n    return '';\n  }\n  return '\\n';\n};\n\n/**\n * Renderer class. Renders HTML and exposes `rules` to allow\n * local modifications.\n */\n\nfunction Renderer() {\n  this.rules = assign({}, rules);\n\n  // exported helper, for custom rules only\n  this.getBreak = rules.getBreak;\n}\n\n/**\n * Render a string of inline HTML with the given `tokens` and\n * `options`.\n *\n * @param  {Array} `tokens`\n * @param  {Object} `options`\n * @param  {Object} `env`\n * @return {String}\n * @api public\n */\n\nRenderer.prototype.renderInline = function (tokens, options, env) {\n  var _rules = this.rules;\n  var len = tokens.length, i = 0;\n  var result = '';\n\n  while (len--) {\n    result += _rules[tokens[i].type](tokens, i++, options, env, this);\n  }\n\n  return result;\n};\n\n/**\n * Render a string of HTML with the given `tokens` and\n * `options`.\n *\n * @param  {Array} `tokens`\n * @param  {Object} `options`\n * @param  {Object} `env`\n * @return {String}\n * @api public\n */\n\nRenderer.prototype.render = function (tokens, options, env) {\n  var _rules = this.rules;\n  var len = tokens.length, i = -1;\n  var result = '';\n\n  while (++i < len) {\n    if (tokens[i].type === 'inline') {\n      result += this.renderInline(tokens[i].children, options, env);\n    } else {\n      result += _rules[tokens[i].type](tokens, i, options, env, this);\n    }\n  }\n  return result;\n};\n\n/**\n * Ruler is a helper class for building responsibility chains from\n * parse rules. It allows:\n *\n *   - easy stack rules chains\n *   - getting main chain and named chains content (as arrays of functions)\n *\n * Helper methods, should not be used directly.\n * @api private\n */\n\nfunction Ruler() {\n  // List of added rules. Each element is:\n  //\n  // { name: XXX,\n  //   enabled: Boolean,\n  //   fn: Function(),\n  //   alt: [ name2, name3 ] }\n  //\n  this.__rules__ = [];\n\n  // Cached rule chains.\n  //\n  // First level - chain name, '' for default.\n  // Second level - digital anchor for fast filtering by charcodes.\n  //\n  this.__cache__ = null;\n}\n\n/**\n * Find the index of a rule by `name`.\n *\n * @param  {String} `name`\n * @return {Number} Index of the given `name`\n * @api private\n */\n\nRuler.prototype.__find__ = function (name) {\n  var len = this.__rules__.length;\n  var i = -1;\n\n  while (len--) {\n    if (this.__rules__[++i].name === name) {\n      return i;\n    }\n  }\n  return -1;\n};\n\n/**\n * Build the rules lookup cache\n *\n * @api private\n */\n\nRuler.prototype.__compile__ = function () {\n  var self = this;\n  var chains = [ '' ];\n\n  // collect unique names\n  self.__rules__.forEach(function (rule) {\n    if (!rule.enabled) {\n      return;\n    }\n\n    rule.alt.forEach(function (altName) {\n      if (chains.indexOf(altName) < 0) {\n        chains.push(altName);\n      }\n    });\n  });\n\n  self.__cache__ = {};\n\n  chains.forEach(function (chain) {\n    self.__cache__[chain] = [];\n    self.__rules__.forEach(function (rule) {\n      if (!rule.enabled) {\n        return;\n      }\n\n      if (chain && rule.alt.indexOf(chain) < 0) {\n        return;\n      }\n      self.__cache__[chain].push(rule.fn);\n    });\n  });\n};\n\n/**\n * Ruler public methods\n * ------------------------------------------------\n */\n\n/**\n * Replace rule function\n *\n * @param  {String} `name` Rule name\n * @param  {Function `fn`\n * @param  {Object} `options`\n * @api private\n */\n\nRuler.prototype.at = function (name, fn, options) {\n  var idx = this.__find__(name);\n  var opt = options || {};\n\n  if (idx === -1) {\n    throw new Error('Parser rule not found: ' + name);\n  }\n\n  this.__rules__[idx].fn = fn;\n  this.__rules__[idx].alt = opt.alt || [];\n  this.__cache__ = null;\n};\n\n/**\n * Add a rule to the chain before given the `ruleName`.\n *\n * @param  {String}   `beforeName`\n * @param  {String}   `ruleName`\n * @param  {Function} `fn`\n * @param  {Object}   `options`\n * @api private\n */\n\nRuler.prototype.before = function (beforeName, ruleName, fn, options) {\n  var idx = this.__find__(beforeName);\n  var opt = options || {};\n\n  if (idx === -1) {\n    throw new Error('Parser rule not found: ' + beforeName);\n  }\n\n  this.__rules__.splice(idx, 0, {\n    name: ruleName,\n    enabled: true,\n    fn: fn,\n    alt: opt.alt || []\n  });\n\n  this.__cache__ = null;\n};\n\n/**\n * Add a rule to the chain after the given `ruleName`.\n *\n * @param  {String}   `afterName`\n * @param  {String}   `ruleName`\n * @param  {Function} `fn`\n * @param  {Object}   `options`\n * @api private\n */\n\nRuler.prototype.after = function (afterName, ruleName, fn, options) {\n  var idx = this.__find__(afterName);\n  var opt = options || {};\n\n  if (idx === -1) {\n    throw new Error('Parser rule not found: ' + afterName);\n  }\n\n  this.__rules__.splice(idx + 1, 0, {\n    name: ruleName,\n    enabled: true,\n    fn: fn,\n    alt: opt.alt || []\n  });\n\n  this.__cache__ = null;\n};\n\n/**\n * Add a rule to the end of chain.\n *\n * @param  {String}   `ruleName`\n * @param  {Function} `fn`\n * @param  {Object}   `options`\n * @return {String}\n */\n\nRuler.prototype.push = function (ruleName, fn, options) {\n  var opt = options || {};\n\n  this.__rules__.push({\n    name: ruleName,\n    enabled: true,\n    fn: fn,\n    alt: opt.alt || []\n  });\n\n  this.__cache__ = null;\n};\n\n/**\n * Enable a rule or list of rules.\n *\n * @param  {String|Array} `list` Name or array of rule names to enable\n * @param  {Boolean} `strict` If `true`, all non listed rules will be disabled.\n * @api private\n */\n\nRuler.prototype.enable = function (list, strict) {\n  list = !Array.isArray(list)\n    ? [ list ]\n    : list;\n\n  // In strict mode disable all existing rules first\n  if (strict) {\n    this.__rules__.forEach(function (rule) {\n      rule.enabled = false;\n    });\n  }\n\n  // Search by name and enable\n  list.forEach(function (name) {\n    var idx = this.__find__(name);\n    if (idx < 0) {\n      throw new Error('Rules manager: invalid rule name ' + name);\n    }\n    this.__rules__[idx].enabled = true;\n  }, this);\n\n  this.__cache__ = null;\n};\n\n\n/**\n * Disable a rule or list of rules.\n *\n * @param  {String|Array} `list` Name or array of rule names to disable\n * @api private\n */\n\nRuler.prototype.disable = function (list) {\n  list = !Array.isArray(list)\n    ? [ list ]\n    : list;\n\n  // Search by name and disable\n  list.forEach(function (name) {\n    var idx = this.__find__(name);\n    if (idx < 0) {\n      throw new Error('Rules manager: invalid rule name ' + name);\n    }\n    this.__rules__[idx].enabled = false;\n  }, this);\n\n  this.__cache__ = null;\n};\n\n/**\n * Get a rules list as an array of functions.\n *\n * @param  {String} `chainName`\n * @return {Object}\n * @api private\n */\n\nRuler.prototype.getRules = function (chainName) {\n  if (this.__cache__ === null) {\n    this.__compile__();\n  }\n  return this.__cache__[chainName] || [];\n};\n\nfunction block(state) {\n\n  if (state.inlineMode) {\n    state.tokens.push({\n      type: 'inline',\n      content: state.src.replace(/\\n/g, ' ').trim(),\n      level: 0,\n      lines: [ 0, 1 ],\n      children: []\n    });\n\n  } else {\n    state.block.parse(state.src, state.options, state.env, state.tokens);\n  }\n}\n\n// Inline parser state\n\nfunction StateInline(src, parserInline, options, env, outTokens) {\n  this.src = src;\n  this.env = env;\n  this.options = options;\n  this.parser = parserInline;\n  this.tokens = outTokens;\n  this.pos = 0;\n  this.posMax = this.src.length;\n  this.level = 0;\n  this.pending = '';\n  this.pendingLevel = 0;\n\n  this.cache = [];        // Stores { start: end } pairs. Useful for backtrack\n                          // optimization of pairs parse (emphasis, strikes).\n\n  // Link parser state vars\n\n  this.isInLabel = false; // Set true when seek link label - we should disable\n                          // \"paired\" rules (emphasis, strikes) to not skip\n                          // tailing `]`\n\n  this.linkLevel = 0;     // Increment for each nesting link. Used to prevent\n                          // nesting in definitions\n\n  this.linkContent = '';  // Temporary storage for link url\n\n  this.labelUnmatchedScopes = 0; // Track unpaired `[` for link labels\n                                 // (backtrack optimization)\n}\n\n// Flush pending text\n//\nStateInline.prototype.pushPending = function () {\n  this.tokens.push({\n    type: 'text',\n    content: this.pending,\n    level: this.pendingLevel\n  });\n  this.pending = '';\n};\n\n// Push new token to \"stream\".\n// If pending text exists - flush it as text token\n//\nStateInline.prototype.push = function (token) {\n  if (this.pending) {\n    this.pushPending();\n  }\n\n  this.tokens.push(token);\n  this.pendingLevel = this.level;\n};\n\n// Store value to cache.\n// !!! Implementation has parser-specific optimizations\n// !!! keys MUST be integer, >= 0; values MUST be integer, > 0\n//\nStateInline.prototype.cacheSet = function (key, val) {\n  for (var i = this.cache.length; i <= key; i++) {\n    this.cache.push(0);\n  }\n\n  this.cache[key] = val;\n};\n\n// Get cache value\n//\nStateInline.prototype.cacheGet = function (key) {\n  return key < this.cache.length ? this.cache[key] : 0;\n};\n\n/**\n * Parse link labels\n *\n * This function assumes that first character (`[`) already matches;\n * returns the end of the label.\n *\n * @param  {Object} state\n * @param  {Number} start\n * @api private\n */\n\nfunction parseLinkLabel(state, start) {\n  var level, found, marker,\n      labelEnd = -1,\n      max = state.posMax,\n      oldPos = state.pos,\n      oldFlag = state.isInLabel;\n\n  if (state.isInLabel) { return -1; }\n\n  if (state.labelUnmatchedScopes) {\n    state.labelUnmatchedScopes--;\n    return -1;\n  }\n\n  state.pos = start + 1;\n  state.isInLabel = true;\n  level = 1;\n\n  while (state.pos < max) {\n    marker = state.src.charCodeAt(state.pos);\n    if (marker === 0x5B /* [ */) {\n      level++;\n    } else if (marker === 0x5D /* ] */) {\n      level--;\n      if (level === 0) {\n        found = true;\n        break;\n      }\n    }\n\n    state.parser.skipToken(state);\n  }\n\n  if (found) {\n    labelEnd = state.pos;\n    state.labelUnmatchedScopes = 0;\n  } else {\n    state.labelUnmatchedScopes = level - 1;\n  }\n\n  // restore old state\n  state.pos = oldPos;\n  state.isInLabel = oldFlag;\n\n  return labelEnd;\n}\n\n// Parse abbreviation definitions, i.e. `*[abbr]: description`\n\n\nfunction parseAbbr(str, parserInline, options, env) {\n  var state, labelEnd, pos, max, label, title;\n\n  if (str.charCodeAt(0) !== 0x2A/* * */) { return -1; }\n  if (str.charCodeAt(1) !== 0x5B/* [ */) { return -1; }\n\n  if (str.indexOf(']:') === -1) { return -1; }\n\n  state = new StateInline(str, parserInline, options, env, []);\n  labelEnd = parseLinkLabel(state, 1);\n\n  if (labelEnd < 0 || str.charCodeAt(labelEnd + 1) !== 0x3A/* : */) { return -1; }\n\n  max = state.posMax;\n\n  // abbr title is always one line, so looking for ending \"\\n\" here\n  for (pos = labelEnd + 2; pos < max; pos++) {\n    if (state.src.charCodeAt(pos) === 0x0A) { break; }\n  }\n\n  label = str.slice(2, labelEnd);\n  title = str.slice(labelEnd + 2, pos).trim();\n  if (title.length === 0) { return -1; }\n  if (!env.abbreviations) { env.abbreviations = {}; }\n  // prepend ':' to avoid conflict with Object.prototype members\n  if (typeof env.abbreviations[':' + label] === 'undefined') {\n    env.abbreviations[':' + label] = title;\n  }\n\n  return pos;\n}\n\nfunction abbr(state) {\n  var tokens = state.tokens, i, l, content, pos;\n\n  if (state.inlineMode) {\n    return;\n  }\n\n  // Parse inlines\n  for (i = 1, l = tokens.length - 1; i < l; i++) {\n    if (tokens[i - 1].type === 'paragraph_open' &&\n        tokens[i].type === 'inline' &&\n        tokens[i + 1].type === 'paragraph_close') {\n\n      content = tokens[i].content;\n      while (content.length) {\n        pos = parseAbbr(content, state.inline, state.options, state.env);\n        if (pos < 0) { break; }\n        content = content.slice(pos).trim();\n      }\n\n      tokens[i].content = content;\n      if (!content.length) {\n        tokens[i - 1].tight = true;\n        tokens[i + 1].tight = true;\n      }\n    }\n  }\n}\n\nfunction normalizeLink(url) {\n  var normalized = replaceEntities(url);\n  // We shouldn't care about the result of malformed URIs,\n  // and should not throw an exception.\n  try {\n    normalized = decodeURI(normalized);\n  } catch (err) {}\n  return encodeURI(normalized);\n}\n\n/**\n * Parse link destination\n *\n *   - on success it returns a string and updates state.pos;\n *   - on failure it returns null\n *\n * @param  {Object} state\n * @param  {Number} pos\n * @api private\n */\n\nfunction parseLinkDestination(state, pos) {\n  var code, level, link,\n      start = pos,\n      max = state.posMax;\n\n  if (state.src.charCodeAt(pos) === 0x3C /* < */) {\n    pos++;\n    while (pos < max) {\n      code = state.src.charCodeAt(pos);\n      if (code === 0x0A /* \\n */) { return false; }\n      if (code === 0x3E /* > */) {\n        link = normalizeLink(unescapeMd(state.src.slice(start + 1, pos)));\n        if (!state.parser.validateLink(link)) { return false; }\n        state.pos = pos + 1;\n        state.linkContent = link;\n        return true;\n      }\n      if (code === 0x5C /* \\ */ && pos + 1 < max) {\n        pos += 2;\n        continue;\n      }\n\n      pos++;\n    }\n\n    // no closing '>'\n    return false;\n  }\n\n  // this should be ... } else { ... branch\n\n  level = 0;\n  while (pos < max) {\n    code = state.src.charCodeAt(pos);\n\n    if (code === 0x20) { break; }\n\n    // ascii control chars\n    if (code < 0x20 || code === 0x7F) { break; }\n\n    if (code === 0x5C /* \\ */ && pos + 1 < max) {\n      pos += 2;\n      continue;\n    }\n\n    if (code === 0x28 /* ( */) {\n      level++;\n      if (level > 1) { break; }\n    }\n\n    if (code === 0x29 /* ) */) {\n      level--;\n      if (level < 0) { break; }\n    }\n\n    pos++;\n  }\n\n  if (start === pos) { return false; }\n\n  link = unescapeMd(state.src.slice(start, pos));\n  if (!state.parser.validateLink(link)) { return false; }\n\n  state.linkContent = link;\n  state.pos = pos;\n  return true;\n}\n\n/**\n * Parse link title\n *\n *   - on success it returns a string and updates state.pos;\n *   - on failure it returns null\n *\n * @param  {Object} state\n * @param  {Number} pos\n * @api private\n */\n\nfunction parseLinkTitle(state, pos) {\n  var code,\n      start = pos,\n      max = state.posMax,\n      marker = state.src.charCodeAt(pos);\n\n  if (marker !== 0x22 /* \" */ && marker !== 0x27 /* ' */ && marker !== 0x28 /* ( */) { return false; }\n\n  pos++;\n\n  // if opening marker is \"(\", switch it to closing marker \")\"\n  if (marker === 0x28) { marker = 0x29; }\n\n  while (pos < max) {\n    code = state.src.charCodeAt(pos);\n    if (code === marker) {\n      state.pos = pos + 1;\n      state.linkContent = unescapeMd(state.src.slice(start + 1, pos));\n      return true;\n    }\n    if (code === 0x5C /* \\ */ && pos + 1 < max) {\n      pos += 2;\n      continue;\n    }\n\n    pos++;\n  }\n\n  return false;\n}\n\nfunction normalizeReference(str) {\n  // use .toUpperCase() instead of .toLowerCase()\n  // here to avoid a conflict with Object.prototype\n  // members (most notably, `__proto__`)\n  return str.trim().replace(/\\s+/g, ' ').toUpperCase();\n}\n\nfunction parseReference(str, parser, options, env) {\n  var state, labelEnd, pos, max, code, start, href, title, label;\n\n  if (str.charCodeAt(0) !== 0x5B/* [ */) { return -1; }\n\n  if (str.indexOf(']:') === -1) { return -1; }\n\n  state = new StateInline(str, parser, options, env, []);\n  labelEnd = parseLinkLabel(state, 0);\n\n  if (labelEnd < 0 || str.charCodeAt(labelEnd + 1) !== 0x3A/* : */) { return -1; }\n\n  max = state.posMax;\n\n  // [label]:   destination   'title'\n  //         ^^^ skip optional whitespace here\n  for (pos = labelEnd + 2; pos < max; pos++) {\n    code = state.src.charCodeAt(pos);\n    if (code !== 0x20 && code !== 0x0A) { break; }\n  }\n\n  // [label]:   destination   'title'\n  //            ^^^^^^^^^^^ parse this\n  if (!parseLinkDestination(state, pos)) { return -1; }\n  href = state.linkContent;\n  pos = state.pos;\n\n  // [label]:   destination   'title'\n  //                       ^^^ skipping those spaces\n  start = pos;\n  for (pos = pos + 1; pos < max; pos++) {\n    code = state.src.charCodeAt(pos);\n    if (code !== 0x20 && code !== 0x0A) { break; }\n  }\n\n  // [label]:   destination   'title'\n  //                          ^^^^^^^ parse this\n  if (pos < max && start !== pos && parseLinkTitle(state, pos)) {\n    title = state.linkContent;\n    pos = state.pos;\n  } else {\n    title = '';\n    pos = start;\n  }\n\n  // ensure that the end of the line is empty\n  while (pos < max && state.src.charCodeAt(pos) === 0x20/* space */) { pos++; }\n  if (pos < max && state.src.charCodeAt(pos) !== 0x0A) { return -1; }\n\n  label = normalizeReference(str.slice(1, labelEnd));\n  if (typeof env.references[label] === 'undefined') {\n    env.references[label] = { title: title, href: href };\n  }\n\n  return pos;\n}\n\n\nfunction references(state) {\n  var tokens = state.tokens, i, l, content, pos;\n\n  state.env.references = state.env.references || {};\n\n  if (state.inlineMode) {\n    return;\n  }\n\n  // Scan definitions in paragraph inlines\n  for (i = 1, l = tokens.length - 1; i < l; i++) {\n    if (tokens[i].type === 'inline' &&\n        tokens[i - 1].type === 'paragraph_open' &&\n        tokens[i + 1].type === 'paragraph_close') {\n\n      content = tokens[i].content;\n      while (content.length) {\n        pos = parseReference(content, state.inline, state.options, state.env);\n        if (pos < 0) { break; }\n        content = content.slice(pos).trim();\n      }\n\n      tokens[i].content = content;\n      if (!content.length) {\n        tokens[i - 1].tight = true;\n        tokens[i + 1].tight = true;\n      }\n    }\n  }\n}\n\nfunction inline(state) {\n  var tokens = state.tokens, tok, i, l;\n\n  // Parse inlines\n  for (i = 0, l = tokens.length; i < l; i++) {\n    tok = tokens[i];\n    if (tok.type === 'inline') {\n      state.inline.parse(tok.content, state.options, state.env, tok.children);\n    }\n  }\n}\n\nfunction footnote_block(state) {\n  var i, l, j, t, lastParagraph, list, tokens, current, currentLabel,\n      level = 0,\n      insideRef = false,\n      refTokens = {};\n\n  if (!state.env.footnotes) { return; }\n\n  state.tokens = state.tokens.filter(function(tok) {\n    if (tok.type === 'footnote_reference_open') {\n      insideRef = true;\n      current = [];\n      currentLabel = tok.label;\n      return false;\n    }\n    if (tok.type === 'footnote_reference_close') {\n      insideRef = false;\n      // prepend ':' to avoid conflict with Object.prototype members\n      refTokens[':' + currentLabel] = current;\n      return false;\n    }\n    if (insideRef) { current.push(tok); }\n    return !insideRef;\n  });\n\n  if (!state.env.footnotes.list) { return; }\n  list = state.env.footnotes.list;\n\n  state.tokens.push({\n    type: 'footnote_block_open',\n    level: level++\n  });\n  for (i = 0, l = list.length; i < l; i++) {\n    state.tokens.push({\n      type: 'footnote_open',\n      id: i,\n      level: level++\n    });\n\n    if (list[i].tokens) {\n      tokens = [];\n      tokens.push({\n        type: 'paragraph_open',\n        tight: false,\n        level: level++\n      });\n      tokens.push({\n        type: 'inline',\n        content: '',\n        level: level,\n        children: list[i].tokens\n      });\n      tokens.push({\n        type: 'paragraph_close',\n        tight: false,\n        level: --level\n      });\n    } else if (list[i].label) {\n      tokens = refTokens[':' + list[i].label];\n    }\n\n    state.tokens = state.tokens.concat(tokens);\n    if (state.tokens[state.tokens.length - 1].type === 'paragraph_close') {\n      lastParagraph = state.tokens.pop();\n    } else {\n      lastParagraph = null;\n    }\n\n    t = list[i].count > 0 ? list[i].count : 1;\n    for (j = 0; j < t; j++) {\n      state.tokens.push({\n        type: 'footnote_anchor',\n        id: i,\n        subId: j,\n        level: level\n      });\n    }\n\n    if (lastParagraph) {\n      state.tokens.push(lastParagraph);\n    }\n\n    state.tokens.push({\n      type: 'footnote_close',\n      level: --level\n    });\n  }\n  state.tokens.push({\n    type: 'footnote_block_close',\n    level: --level\n  });\n}\n\n// Enclose abbreviations in <abbr> tags\n//\n\nvar PUNCT_CHARS = ' \\n()[]\\'\".,!?-';\n\n\n// from Google closure library\n// http://closure-library.googlecode.com/git-history/docs/local_closure_goog_string_string.js.source.html#line1021\nfunction regEscape(s) {\n  return s.replace(/([-()\\[\\]{}+?*.$\\^|,:#<!\\\\])/g, '\\\\$1');\n}\n\n\nfunction abbr2(state) {\n  var i, j, l, tokens, token, text, nodes, pos, level, reg, m, regText,\n      blockTokens = state.tokens;\n\n  if (!state.env.abbreviations) { return; }\n  if (!state.env.abbrRegExp) {\n    regText = '(^|[' + PUNCT_CHARS.split('').map(regEscape).join('') + '])'\n            + '(' + Object.keys(state.env.abbreviations).map(function (x) {\n                      return x.substr(1);\n                    }).sort(function (a, b) {\n                      return b.length - a.length;\n                    }).map(regEscape).join('|') + ')'\n            + '($|[' + PUNCT_CHARS.split('').map(regEscape).join('') + '])';\n    state.env.abbrRegExp = new RegExp(regText, 'g');\n  }\n  reg = state.env.abbrRegExp;\n\n  for (j = 0, l = blockTokens.length; j < l; j++) {\n    if (blockTokens[j].type !== 'inline') { continue; }\n    tokens = blockTokens[j].children;\n\n    // We scan from the end, to keep position when new tags added.\n    for (i = tokens.length - 1; i >= 0; i--) {\n      token = tokens[i];\n      if (token.type !== 'text') { continue; }\n\n      pos = 0;\n      text = token.content;\n      reg.lastIndex = 0;\n      level = token.level;\n      nodes = [];\n\n      while ((m = reg.exec(text))) {\n        if (reg.lastIndex > pos) {\n          nodes.push({\n            type: 'text',\n            content: text.slice(pos, m.index + m[1].length),\n            level: level\n          });\n        }\n\n        nodes.push({\n          type: 'abbr_open',\n          title: state.env.abbreviations[':' + m[2]],\n          level: level++\n        });\n        nodes.push({\n          type: 'text',\n          content: m[2],\n          level: level\n        });\n        nodes.push({\n          type: 'abbr_close',\n          level: --level\n        });\n        pos = reg.lastIndex - m[3].length;\n      }\n\n      if (!nodes.length) { continue; }\n\n      if (pos < text.length) {\n        nodes.push({\n          type: 'text',\n          content: text.slice(pos),\n          level: level\n        });\n      }\n\n      // replace current node\n      blockTokens[j].children = tokens = [].concat(tokens.slice(0, i), nodes, tokens.slice(i + 1));\n    }\n  }\n}\n\n// Simple typographical replacements\n//\n// TODO:\n// - fractionals 1/2, 1/4, 3/4 -> ½, ¼, ¾\n// - miltiplication 2 x 4 -> 2 × 4\n\nvar RARE_RE = /\\+-|\\.\\.|\\?\\?\\?\\?|!!!!|,,|--/;\n\nvar SCOPED_ABBR_RE = /\\((c|tm|r|p)\\)/ig;\nvar SCOPED_ABBR = {\n  'c': '©',\n  'r': '®',\n  'p': '§',\n  'tm': '™'\n};\n\nfunction replaceScopedAbbr(str) {\n  if (str.indexOf('(') < 0) { return str; }\n\n  return str.replace(SCOPED_ABBR_RE, function(match, name) {\n    return SCOPED_ABBR[name.toLowerCase()];\n  });\n}\n\n\nfunction replace(state) {\n  var i, token, text, inlineTokens, blkIdx;\n\n  if (!state.options.typographer) { return; }\n\n  for (blkIdx = state.tokens.length - 1; blkIdx >= 0; blkIdx--) {\n\n    if (state.tokens[blkIdx].type !== 'inline') { continue; }\n\n    inlineTokens = state.tokens[blkIdx].children;\n\n    for (i = inlineTokens.length - 1; i >= 0; i--) {\n      token = inlineTokens[i];\n      if (token.type === 'text') {\n        text = token.content;\n\n        text = replaceScopedAbbr(text);\n\n        if (RARE_RE.test(text)) {\n          text = text\n            .replace(/\\+-/g, '±')\n            // .., ..., ....... -> …\n            // but ?..... & !..... -> ?.. & !..\n            .replace(/\\.{2,}/g, '…').replace(/([?!])…/g, '$1..')\n            .replace(/([?!]){4,}/g, '$1$1$1').replace(/,{2,}/g, ',')\n            // em-dash\n            .replace(/(^|[^-])---([^-]|$)/mg, '$1\\u2014$2')\n            // en-dash\n            .replace(/(^|\\s)--(\\s|$)/mg, '$1\\u2013$2')\n            .replace(/(^|[^-\\s])--([^-\\s]|$)/mg, '$1\\u2013$2');\n        }\n\n        token.content = text;\n      }\n    }\n  }\n}\n\n// Convert straight quotation marks to typographic ones\n//\n\nvar QUOTE_TEST_RE = /['\"]/;\nvar QUOTE_RE = /['\"]/g;\nvar PUNCT_RE = /[-\\s()\\[\\]]/;\nvar APOSTROPHE = '’';\n\n// This function returns true if the character at `pos`\n// could be inside a word.\nfunction isLetter(str, pos) {\n  if (pos < 0 || pos >= str.length) { return false; }\n  return !PUNCT_RE.test(str[pos]);\n}\n\n\nfunction replaceAt(str, index, ch) {\n  return str.substr(0, index) + ch + str.substr(index + 1);\n}\n\n\nfunction smartquotes(state) {\n  /*eslint max-depth:0*/\n  var i, token, text, t, pos, max, thisLevel, lastSpace, nextSpace, item,\n      canOpen, canClose, j, isSingle, blkIdx, tokens,\n      stack;\n\n  if (!state.options.typographer) { return; }\n\n  stack = [];\n\n  for (blkIdx = state.tokens.length - 1; blkIdx >= 0; blkIdx--) {\n\n    if (state.tokens[blkIdx].type !== 'inline') { continue; }\n\n    tokens = state.tokens[blkIdx].children;\n    stack.length = 0;\n\n    for (i = 0; i < tokens.length; i++) {\n      token = tokens[i];\n\n      if (token.type !== 'text' || QUOTE_TEST_RE.test(token.text)) { continue; }\n\n      thisLevel = tokens[i].level;\n\n      for (j = stack.length - 1; j >= 0; j--) {\n        if (stack[j].level <= thisLevel) { break; }\n      }\n      stack.length = j + 1;\n\n      text = token.content;\n      pos = 0;\n      max = text.length;\n\n      /*eslint no-labels:0,block-scoped-var:0*/\n      OUTER:\n      while (pos < max) {\n        QUOTE_RE.lastIndex = pos;\n        t = QUOTE_RE.exec(text);\n        if (!t) { break; }\n\n        lastSpace = !isLetter(text, t.index - 1);\n        pos = t.index + 1;\n        isSingle = (t[0] === \"'\");\n        nextSpace = !isLetter(text, pos);\n\n        if (!nextSpace && !lastSpace) {\n          // middle of word\n          if (isSingle) {\n            token.content = replaceAt(token.content, t.index, APOSTROPHE);\n          }\n          continue;\n        }\n\n        canOpen = !nextSpace;\n        canClose = !lastSpace;\n\n        if (canClose) {\n          // this could be a closing quote, rewind the stack to get a match\n          for (j = stack.length - 1; j >= 0; j--) {\n            item = stack[j];\n            if (stack[j].level < thisLevel) { break; }\n            if (item.single === isSingle && stack[j].level === thisLevel) {\n              item = stack[j];\n              if (isSingle) {\n                tokens[item.token].content = replaceAt(tokens[item.token].content, item.pos, state.options.quotes[2]);\n                token.content = replaceAt(token.content, t.index, state.options.quotes[3]);\n              } else {\n                tokens[item.token].content = replaceAt(tokens[item.token].content, item.pos, state.options.quotes[0]);\n                token.content = replaceAt(token.content, t.index, state.options.quotes[1]);\n              }\n              stack.length = j;\n              continue OUTER;\n            }\n          }\n        }\n\n        if (canOpen) {\n          stack.push({\n            token: i,\n            pos: t.index,\n            single: isSingle,\n            level: thisLevel\n          });\n        } else if (canClose && isSingle) {\n          token.content = replaceAt(token.content, t.index, APOSTROPHE);\n        }\n      }\n    }\n  }\n}\n\n/**\n * Core parser `rules`\n */\n\nvar _rules = [\n  [ 'block',          block          ],\n  [ 'abbr',           abbr           ],\n  [ 'references',     references     ],\n  [ 'inline',         inline         ],\n  [ 'footnote_tail',  footnote_block  ],\n  [ 'abbr2',          abbr2          ],\n  [ 'replacements',   replace   ],\n  [ 'smartquotes',    smartquotes    ],\n];\n\n/**\n * Class for top level (`core`) parser rules\n *\n * @api private\n */\n\nfunction Core() {\n  this.options = {};\n  this.ruler = new Ruler();\n  for (var i = 0; i < _rules.length; i++) {\n    this.ruler.push(_rules[i][0], _rules[i][1]);\n  }\n}\n\n/**\n * Process rules with the given `state`\n *\n * @param  {Object} `state`\n * @api private\n */\n\nCore.prototype.process = function (state) {\n  var i, l, rules;\n  rules = this.ruler.getRules('');\n  for (i = 0, l = rules.length; i < l; i++) {\n    rules[i](state);\n  }\n};\n\n// Parser state class\n\nfunction StateBlock(src, parser, options, env, tokens) {\n  var ch, s, start, pos, len, indent, indent_found;\n\n  this.src = src;\n\n  // Shortcuts to simplify nested calls\n  this.parser = parser;\n\n  this.options = options;\n\n  this.env = env;\n\n  //\n  // Internal state vartiables\n  //\n\n  this.tokens = tokens;\n\n  this.bMarks = [];  // line begin offsets for fast jumps\n  this.eMarks = [];  // line end offsets for fast jumps\n  this.tShift = [];  // indent for each line\n\n  // block parser variables\n  this.blkIndent  = 0; // required block content indent\n                       // (for example, if we are in list)\n  this.line       = 0; // line index in src\n  this.lineMax    = 0; // lines count\n  this.tight      = false;  // loose/tight mode for lists\n  this.parentType = 'root'; // if `list`, block parser stops on two newlines\n  this.ddIndent   = -1; // indent of the current dd block (-1 if there isn't any)\n\n  this.level = 0;\n\n  // renderer\n  this.result = '';\n\n  // Create caches\n  // Generate markers.\n  s = this.src;\n  indent = 0;\n  indent_found = false;\n\n  for (start = pos = indent = 0, len = s.length; pos < len; pos++) {\n    ch = s.charCodeAt(pos);\n\n    if (!indent_found) {\n      if (ch === 0x20/* space */) {\n        indent++;\n        continue;\n      } else {\n        indent_found = true;\n      }\n    }\n\n    if (ch === 0x0A || pos === len - 1) {\n      if (ch !== 0x0A) { pos++; }\n      this.bMarks.push(start);\n      this.eMarks.push(pos);\n      this.tShift.push(indent);\n\n      indent_found = false;\n      indent = 0;\n      start = pos + 1;\n    }\n  }\n\n  // Push fake entry to simplify cache bounds checks\n  this.bMarks.push(s.length);\n  this.eMarks.push(s.length);\n  this.tShift.push(0);\n\n  this.lineMax = this.bMarks.length - 1; // don't count last fake line\n}\n\nStateBlock.prototype.isEmpty = function isEmpty(line) {\n  return this.bMarks[line] + this.tShift[line] >= this.eMarks[line];\n};\n\nStateBlock.prototype.skipEmptyLines = function skipEmptyLines(from) {\n  for (var max = this.lineMax; from < max; from++) {\n    if (this.bMarks[from] + this.tShift[from] < this.eMarks[from]) {\n      break;\n    }\n  }\n  return from;\n};\n\n// Skip spaces from given position.\nStateBlock.prototype.skipSpaces = function skipSpaces(pos) {\n  for (var max = this.src.length; pos < max; pos++) {\n    if (this.src.charCodeAt(pos) !== 0x20/* space */) { break; }\n  }\n  return pos;\n};\n\n// Skip char codes from given position\nStateBlock.prototype.skipChars = function skipChars(pos, code) {\n  for (var max = this.src.length; pos < max; pos++) {\n    if (this.src.charCodeAt(pos) !== code) { break; }\n  }\n  return pos;\n};\n\n// Skip char codes reverse from given position - 1\nStateBlock.prototype.skipCharsBack = function skipCharsBack(pos, code, min) {\n  if (pos <= min) { return pos; }\n\n  while (pos > min) {\n    if (code !== this.src.charCodeAt(--pos)) { return pos + 1; }\n  }\n  return pos;\n};\n\n// cut lines range from source.\nStateBlock.prototype.getLines = function getLines(begin, end, indent, keepLastLF) {\n  var i, first, last, queue, shift,\n      line = begin;\n\n  if (begin >= end) {\n    return '';\n  }\n\n  // Opt: don't use push queue for single line;\n  if (line + 1 === end) {\n    first = this.bMarks[line] + Math.min(this.tShift[line], indent);\n    last = keepLastLF ? this.eMarks[line] + 1 : this.eMarks[line];\n    return this.src.slice(first, last);\n  }\n\n  queue = new Array(end - begin);\n\n  for (i = 0; line < end; line++, i++) {\n    shift = this.tShift[line];\n    if (shift > indent) { shift = indent; }\n    if (shift < 0) { shift = 0; }\n\n    first = this.bMarks[line] + shift;\n\n    if (line + 1 < end || keepLastLF) {\n      // No need for bounds check because we have fake entry on tail.\n      last = this.eMarks[line] + 1;\n    } else {\n      last = this.eMarks[line];\n    }\n\n    queue[i] = this.src.slice(first, last);\n  }\n\n  return queue.join('');\n};\n\n// Code block (4 spaces padded)\n\nfunction code(state, startLine, endLine/*, silent*/) {\n  var nextLine, last;\n\n  if (state.tShift[startLine] - state.blkIndent < 4) { return false; }\n\n  last = nextLine = startLine + 1;\n\n  while (nextLine < endLine) {\n    if (state.isEmpty(nextLine)) {\n      nextLine++;\n      continue;\n    }\n    if (state.tShift[nextLine] - state.blkIndent >= 4) {\n      nextLine++;\n      last = nextLine;\n      continue;\n    }\n    break;\n  }\n\n  state.line = nextLine;\n  state.tokens.push({\n    type: 'code',\n    content: state.getLines(startLine, last, 4 + state.blkIndent, true),\n    block: true,\n    lines: [ startLine, state.line ],\n    level: state.level\n  });\n\n  return true;\n}\n\n// fences (``` lang, ~~~ lang)\n\nfunction fences(state, startLine, endLine, silent) {\n  var marker, len, params, nextLine, mem,\n      haveEndMarker = false,\n      pos = state.bMarks[startLine] + state.tShift[startLine],\n      max = state.eMarks[startLine];\n\n  if (pos + 3 > max) { return false; }\n\n  marker = state.src.charCodeAt(pos);\n\n  if (marker !== 0x7E/* ~ */ && marker !== 0x60 /* ` */) {\n    return false;\n  }\n\n  // scan marker length\n  mem = pos;\n  pos = state.skipChars(pos, marker);\n\n  len = pos - mem;\n\n  if (len < 3) { return false; }\n\n  params = state.src.slice(pos, max).trim();\n\n  if (params.indexOf('`') >= 0) { return false; }\n\n  // Since start is found, we can report success here in validation mode\n  if (silent) { return true; }\n\n  // search end of block\n  nextLine = startLine;\n\n  for (;;) {\n    nextLine++;\n    if (nextLine >= endLine) {\n      // unclosed block should be autoclosed by end of document.\n      // also block seems to be autoclosed by end of parent\n      break;\n    }\n\n    pos = mem = state.bMarks[nextLine] + state.tShift[nextLine];\n    max = state.eMarks[nextLine];\n\n    if (pos < max && state.tShift[nextLine] < state.blkIndent) {\n      // non-empty line with negative indent should stop the list:\n      // - ```\n      //  test\n      break;\n    }\n\n    if (state.src.charCodeAt(pos) !== marker) { continue; }\n\n    if (state.tShift[nextLine] - state.blkIndent >= 4) {\n      // closing fence should be indented less than 4 spaces\n      continue;\n    }\n\n    pos = state.skipChars(pos, marker);\n\n    // closing code fence must be at least as long as the opening one\n    if (pos - mem < len) { continue; }\n\n    // make sure tail has spaces only\n    pos = state.skipSpaces(pos);\n\n    if (pos < max) { continue; }\n\n    haveEndMarker = true;\n    // found!\n    break;\n  }\n\n  // If a fence has heading spaces, they should be removed from its inner block\n  len = state.tShift[startLine];\n\n  state.line = nextLine + (haveEndMarker ? 1 : 0);\n  state.tokens.push({\n    type: 'fence',\n    params: params,\n    content: state.getLines(startLine + 1, nextLine, len, true),\n    lines: [ startLine, state.line ],\n    level: state.level\n  });\n\n  return true;\n}\n\n// Block quotes\n\nfunction blockquote(state, startLine, endLine, silent) {\n  var nextLine, lastLineEmpty, oldTShift, oldBMarks, oldIndent, oldParentType, lines,\n      terminatorRules,\n      i, l, terminate,\n      pos = state.bMarks[startLine] + state.tShift[startLine],\n      max = state.eMarks[startLine];\n\n  if (pos > max) { return false; }\n\n  // check the block quote marker\n  if (state.src.charCodeAt(pos++) !== 0x3E/* > */) { return false; }\n\n  if (state.level >= state.options.maxNesting) { return false; }\n\n  // we know that it's going to be a valid blockquote,\n  // so no point trying to find the end of it in silent mode\n  if (silent) { return true; }\n\n  // skip one optional space after '>'\n  if (state.src.charCodeAt(pos) === 0x20) { pos++; }\n\n  oldIndent = state.blkIndent;\n  state.blkIndent = 0;\n\n  oldBMarks = [ state.bMarks[startLine] ];\n  state.bMarks[startLine] = pos;\n\n  // check if we have an empty blockquote\n  pos = pos < max ? state.skipSpaces(pos) : pos;\n  lastLineEmpty = pos >= max;\n\n  oldTShift = [ state.tShift[startLine] ];\n  state.tShift[startLine] = pos - state.bMarks[startLine];\n\n  terminatorRules = state.parser.ruler.getRules('blockquote');\n\n  // Search the end of the block\n  //\n  // Block ends with either:\n  //  1. an empty line outside:\n  //     ```\n  //     > test\n  //\n  //     ```\n  //  2. an empty line inside:\n  //     ```\n  //     >\n  //     test\n  //     ```\n  //  3. another tag\n  //     ```\n  //     > test\n  //      - - -\n  //     ```\n  for (nextLine = startLine + 1; nextLine < endLine; nextLine++) {\n    pos = state.bMarks[nextLine] + state.tShift[nextLine];\n    max = state.eMarks[nextLine];\n\n    if (pos >= max) {\n      // Case 1: line is not inside the blockquote, and this line is empty.\n      break;\n    }\n\n    if (state.src.charCodeAt(pos++) === 0x3E/* > */) {\n      // This line is inside the blockquote.\n\n      // skip one optional space after '>'\n      if (state.src.charCodeAt(pos) === 0x20) { pos++; }\n\n      oldBMarks.push(state.bMarks[nextLine]);\n      state.bMarks[nextLine] = pos;\n\n      pos = pos < max ? state.skipSpaces(pos) : pos;\n      lastLineEmpty = pos >= max;\n\n      oldTShift.push(state.tShift[nextLine]);\n      state.tShift[nextLine] = pos - state.bMarks[nextLine];\n      continue;\n    }\n\n    // Case 2: line is not inside the blockquote, and the last line was empty.\n    if (lastLineEmpty) { break; }\n\n    // Case 3: another tag found.\n    terminate = false;\n    for (i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true;\n        break;\n      }\n    }\n    if (terminate) { break; }\n\n    oldBMarks.push(state.bMarks[nextLine]);\n    oldTShift.push(state.tShift[nextLine]);\n\n    // A negative number means that this is a paragraph continuation;\n    //\n    // Any negative number will do the job here, but it's better for it\n    // to be large enough to make any bugs obvious.\n    state.tShift[nextLine] = -1337;\n  }\n\n  oldParentType = state.parentType;\n  state.parentType = 'blockquote';\n  state.tokens.push({\n    type: 'blockquote_open',\n    lines: lines = [ startLine, 0 ],\n    level: state.level++\n  });\n  state.parser.tokenize(state, startLine, nextLine);\n  state.tokens.push({\n    type: 'blockquote_close',\n    level: --state.level\n  });\n  state.parentType = oldParentType;\n  lines[1] = state.line;\n\n  // Restore original tShift; this might not be necessary since the parser\n  // has already been here, but just to make sure we can do that.\n  for (i = 0; i < oldTShift.length; i++) {\n    state.bMarks[i + startLine] = oldBMarks[i];\n    state.tShift[i + startLine] = oldTShift[i];\n  }\n  state.blkIndent = oldIndent;\n\n  return true;\n}\n\n// Horizontal rule\n\nfunction hr(state, startLine, endLine, silent) {\n  var marker, cnt, ch,\n      pos = state.bMarks[startLine],\n      max = state.eMarks[startLine];\n\n  pos += state.tShift[startLine];\n\n  if (pos > max) { return false; }\n\n  marker = state.src.charCodeAt(pos++);\n\n  // Check hr marker\n  if (marker !== 0x2A/* * */ &&\n      marker !== 0x2D/* - */ &&\n      marker !== 0x5F/* _ */) {\n    return false;\n  }\n\n  // markers can be mixed with spaces, but there should be at least 3 one\n\n  cnt = 1;\n  while (pos < max) {\n    ch = state.src.charCodeAt(pos++);\n    if (ch !== marker && ch !== 0x20/* space */) { return false; }\n    if (ch === marker) { cnt++; }\n  }\n\n  if (cnt < 3) { return false; }\n\n  if (silent) { return true; }\n\n  state.line = startLine + 1;\n  state.tokens.push({\n    type: 'hr',\n    lines: [ startLine, state.line ],\n    level: state.level\n  });\n\n  return true;\n}\n\n// Lists\n\n// Search `[-+*][\\n ]`, returns next pos arter marker on success\n// or -1 on fail.\nfunction skipBulletListMarker(state, startLine) {\n  var marker, pos, max;\n\n  pos = state.bMarks[startLine] + state.tShift[startLine];\n  max = state.eMarks[startLine];\n\n  if (pos >= max) { return -1; }\n\n  marker = state.src.charCodeAt(pos++);\n  // Check bullet\n  if (marker !== 0x2A/* * */ &&\n      marker !== 0x2D/* - */ &&\n      marker !== 0x2B/* + */) {\n    return -1;\n  }\n\n  if (pos < max && state.src.charCodeAt(pos) !== 0x20) {\n    // \" 1.test \" - is not a list item\n    return -1;\n  }\n\n  return pos;\n}\n\n// Search `\\d+[.)][\\n ]`, returns next pos arter marker on success\n// or -1 on fail.\nfunction skipOrderedListMarker(state, startLine) {\n  var ch,\n      pos = state.bMarks[startLine] + state.tShift[startLine],\n      max = state.eMarks[startLine];\n\n  if (pos + 1 >= max) { return -1; }\n\n  ch = state.src.charCodeAt(pos++);\n\n  if (ch < 0x30/* 0 */ || ch > 0x39/* 9 */) { return -1; }\n\n  for (;;) {\n    // EOL -> fail\n    if (pos >= max) { return -1; }\n\n    ch = state.src.charCodeAt(pos++);\n\n    if (ch >= 0x30/* 0 */ && ch <= 0x39/* 9 */) {\n      continue;\n    }\n\n    // found valid marker\n    if (ch === 0x29/* ) */ || ch === 0x2e/* . */) {\n      break;\n    }\n\n    return -1;\n  }\n\n\n  if (pos < max && state.src.charCodeAt(pos) !== 0x20/* space */) {\n    // \" 1.test \" - is not a list item\n    return -1;\n  }\n  return pos;\n}\n\nfunction markTightParagraphs(state, idx) {\n  var i, l,\n      level = state.level + 2;\n\n  for (i = idx + 2, l = state.tokens.length - 2; i < l; i++) {\n    if (state.tokens[i].level === level && state.tokens[i].type === 'paragraph_open') {\n      state.tokens[i + 2].tight = true;\n      state.tokens[i].tight = true;\n      i += 2;\n    }\n  }\n}\n\n\nfunction list(state, startLine, endLine, silent) {\n  var nextLine,\n      indent,\n      oldTShift,\n      oldIndent,\n      oldTight,\n      oldParentType,\n      start,\n      posAfterMarker,\n      max,\n      indentAfterMarker,\n      markerValue,\n      markerCharCode,\n      isOrdered,\n      contentStart,\n      listTokIdx,\n      prevEmptyEnd,\n      listLines,\n      itemLines,\n      tight = true,\n      terminatorRules,\n      i, l, terminate;\n\n  // Detect list type and position after marker\n  if ((posAfterMarker = skipOrderedListMarker(state, startLine)) >= 0) {\n    isOrdered = true;\n  } else if ((posAfterMarker = skipBulletListMarker(state, startLine)) >= 0) {\n    isOrdered = false;\n  } else {\n    return false;\n  }\n\n  if (state.level >= state.options.maxNesting) { return false; }\n\n  // We should terminate list on style change. Remember first one to compare.\n  markerCharCode = state.src.charCodeAt(posAfterMarker - 1);\n\n  // For validation mode we can terminate immediately\n  if (silent) { return true; }\n\n  // Start list\n  listTokIdx = state.tokens.length;\n\n  if (isOrdered) {\n    start = state.bMarks[startLine] + state.tShift[startLine];\n    markerValue = Number(state.src.substr(start, posAfterMarker - start - 1));\n\n    state.tokens.push({\n      type: 'ordered_list_open',\n      order: markerValue,\n      lines: listLines = [ startLine, 0 ],\n      level: state.level++\n    });\n\n  } else {\n    state.tokens.push({\n      type: 'bullet_list_open',\n      lines: listLines = [ startLine, 0 ],\n      level: state.level++\n    });\n  }\n\n  //\n  // Iterate list items\n  //\n\n  nextLine = startLine;\n  prevEmptyEnd = false;\n  terminatorRules = state.parser.ruler.getRules('list');\n\n  while (nextLine < endLine) {\n    contentStart = state.skipSpaces(posAfterMarker);\n    max = state.eMarks[nextLine];\n\n    if (contentStart >= max) {\n      // trimming space in \"-    \\n  3\" case, indent is 1 here\n      indentAfterMarker = 1;\n    } else {\n      indentAfterMarker = contentStart - posAfterMarker;\n    }\n\n    // If we have more than 4 spaces, the indent is 1\n    // (the rest is just indented code block)\n    if (indentAfterMarker > 4) { indentAfterMarker = 1; }\n\n    // If indent is less than 1, assume that it's one, example:\n    //  \"-\\n  test\"\n    if (indentAfterMarker < 1) { indentAfterMarker = 1; }\n\n    // \"  -  test\"\n    //  ^^^^^ - calculating total length of this thing\n    indent = (posAfterMarker - state.bMarks[nextLine]) + indentAfterMarker;\n\n    // Run subparser & write tokens\n    state.tokens.push({\n      type: 'list_item_open',\n      lines: itemLines = [ startLine, 0 ],\n      level: state.level++\n    });\n\n    oldIndent = state.blkIndent;\n    oldTight = state.tight;\n    oldTShift = state.tShift[startLine];\n    oldParentType = state.parentType;\n    state.tShift[startLine] = contentStart - state.bMarks[startLine];\n    state.blkIndent = indent;\n    state.tight = true;\n    state.parentType = 'list';\n\n    state.parser.tokenize(state, startLine, endLine, true);\n\n    // If any of list item is tight, mark list as tight\n    if (!state.tight || prevEmptyEnd) {\n      tight = false;\n    }\n    // Item become loose if finish with empty line,\n    // but we should filter last element, because it means list finish\n    prevEmptyEnd = (state.line - startLine) > 1 && state.isEmpty(state.line - 1);\n\n    state.blkIndent = oldIndent;\n    state.tShift[startLine] = oldTShift;\n    state.tight = oldTight;\n    state.parentType = oldParentType;\n\n    state.tokens.push({\n      type: 'list_item_close',\n      level: --state.level\n    });\n\n    nextLine = startLine = state.line;\n    itemLines[1] = nextLine;\n    contentStart = state.bMarks[startLine];\n\n    if (nextLine >= endLine) { break; }\n\n    if (state.isEmpty(nextLine)) {\n      break;\n    }\n\n    //\n    // Try to check if list is terminated or continued.\n    //\n    if (state.tShift[nextLine] < state.blkIndent) { break; }\n\n    // fail if terminating block found\n    terminate = false;\n    for (i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true;\n        break;\n      }\n    }\n    if (terminate) { break; }\n\n    // fail if list has another type\n    if (isOrdered) {\n      posAfterMarker = skipOrderedListMarker(state, nextLine);\n      if (posAfterMarker < 0) { break; }\n    } else {\n      posAfterMarker = skipBulletListMarker(state, nextLine);\n      if (posAfterMarker < 0) { break; }\n    }\n\n    if (markerCharCode !== state.src.charCodeAt(posAfterMarker - 1)) { break; }\n  }\n\n  // Finilize list\n  state.tokens.push({\n    type: isOrdered ? 'ordered_list_close' : 'bullet_list_close',\n    level: --state.level\n  });\n  listLines[1] = nextLine;\n\n  state.line = nextLine;\n\n  // mark paragraphs tight if needed\n  if (tight) {\n    markTightParagraphs(state, listTokIdx);\n  }\n\n  return true;\n}\n\n// Process footnote reference list\n\nfunction footnote(state, startLine, endLine, silent) {\n  var oldBMark, oldTShift, oldParentType, pos, label,\n      start = state.bMarks[startLine] + state.tShift[startLine],\n      max = state.eMarks[startLine];\n\n  // line should be at least 5 chars - \"[^x]:\"\n  if (start + 4 > max) { return false; }\n\n  if (state.src.charCodeAt(start) !== 0x5B/* [ */) { return false; }\n  if (state.src.charCodeAt(start + 1) !== 0x5E/* ^ */) { return false; }\n  if (state.level >= state.options.maxNesting) { return false; }\n\n  for (pos = start + 2; pos < max; pos++) {\n    if (state.src.charCodeAt(pos) === 0x20) { return false; }\n    if (state.src.charCodeAt(pos) === 0x5D /* ] */) {\n      break;\n    }\n  }\n\n  if (pos === start + 2) { return false; } // no empty footnote labels\n  if (pos + 1 >= max || state.src.charCodeAt(++pos) !== 0x3A /* : */) { return false; }\n  if (silent) { return true; }\n  pos++;\n\n  if (!state.env.footnotes) { state.env.footnotes = {}; }\n  if (!state.env.footnotes.refs) { state.env.footnotes.refs = {}; }\n  label = state.src.slice(start + 2, pos - 2);\n  state.env.footnotes.refs[':' + label] = -1;\n\n  state.tokens.push({\n    type: 'footnote_reference_open',\n    label: label,\n    level: state.level++\n  });\n\n  oldBMark = state.bMarks[startLine];\n  oldTShift = state.tShift[startLine];\n  oldParentType = state.parentType;\n  state.tShift[startLine] = state.skipSpaces(pos) - pos;\n  state.bMarks[startLine] = pos;\n  state.blkIndent += 4;\n  state.parentType = 'footnote';\n\n  if (state.tShift[startLine] < state.blkIndent) {\n    state.tShift[startLine] += state.blkIndent;\n    state.bMarks[startLine] -= state.blkIndent;\n  }\n\n  state.parser.tokenize(state, startLine, endLine, true);\n\n  state.parentType = oldParentType;\n  state.blkIndent -= 4;\n  state.tShift[startLine] = oldTShift;\n  state.bMarks[startLine] = oldBMark;\n\n  state.tokens.push({\n    type: 'footnote_reference_close',\n    level: --state.level\n  });\n\n  return true;\n}\n\n// heading (#, ##, ...)\n\nfunction heading(state, startLine, endLine, silent) {\n  var ch, level, tmp,\n      pos = state.bMarks[startLine] + state.tShift[startLine],\n      max = state.eMarks[startLine];\n\n  if (pos >= max) { return false; }\n\n  ch  = state.src.charCodeAt(pos);\n\n  if (ch !== 0x23/* # */ || pos >= max) { return false; }\n\n  // count heading level\n  level = 1;\n  ch = state.src.charCodeAt(++pos);\n  while (ch === 0x23/* # */ && pos < max && level <= 6) {\n    level++;\n    ch = state.src.charCodeAt(++pos);\n  }\n\n  if (level > 6 || (pos < max && ch !== 0x20/* space */)) { return false; }\n\n  if (silent) { return true; }\n\n  // Let's cut tails like '    ###  ' from the end of string\n\n  max = state.skipCharsBack(max, 0x20, pos); // space\n  tmp = state.skipCharsBack(max, 0x23, pos); // #\n  if (tmp > pos && state.src.charCodeAt(tmp - 1) === 0x20/* space */) {\n    max = tmp;\n  }\n\n  state.line = startLine + 1;\n\n  state.tokens.push({ type: 'heading_open',\n    hLevel: level,\n    lines: [ startLine, state.line ],\n    level: state.level\n  });\n\n  // only if header is not empty\n  if (pos < max) {\n    state.tokens.push({\n      type: 'inline',\n      content: state.src.slice(pos, max).trim(),\n      level: state.level + 1,\n      lines: [ startLine, state.line ],\n      children: []\n    });\n  }\n  state.tokens.push({ type: 'heading_close', hLevel: level, level: state.level });\n\n  return true;\n}\n\n// lheading (---, ===)\n\nfunction lheading(state, startLine, endLine/*, silent*/) {\n  var marker, pos, max,\n      next = startLine + 1;\n\n  if (next >= endLine) { return false; }\n  if (state.tShift[next] < state.blkIndent) { return false; }\n\n  // Scan next line\n\n  if (state.tShift[next] - state.blkIndent > 3) { return false; }\n\n  pos = state.bMarks[next] + state.tShift[next];\n  max = state.eMarks[next];\n\n  if (pos >= max) { return false; }\n\n  marker = state.src.charCodeAt(pos);\n\n  if (marker !== 0x2D/* - */ && marker !== 0x3D/* = */) { return false; }\n\n  pos = state.skipChars(pos, marker);\n\n  pos = state.skipSpaces(pos);\n\n  if (pos < max) { return false; }\n\n  pos = state.bMarks[startLine] + state.tShift[startLine];\n\n  state.line = next + 1;\n  state.tokens.push({\n    type: 'heading_open',\n    hLevel: marker === 0x3D/* = */ ? 1 : 2,\n    lines: [ startLine, state.line ],\n    level: state.level\n  });\n  state.tokens.push({\n    type: 'inline',\n    content: state.src.slice(pos, state.eMarks[startLine]).trim(),\n    level: state.level + 1,\n    lines: [ startLine, state.line - 1 ],\n    children: []\n  });\n  state.tokens.push({\n    type: 'heading_close',\n    hLevel: marker === 0x3D/* = */ ? 1 : 2,\n    level: state.level\n  });\n\n  return true;\n}\n\n// List of valid html blocks names, accorting to commonmark spec\n// http://jgm.github.io/CommonMark/spec.html#html-blocks\n\nvar html_blocks = {};\n\n[\n  'article',\n  'aside',\n  'button',\n  'blockquote',\n  'body',\n  'canvas',\n  'caption',\n  'col',\n  'colgroup',\n  'dd',\n  'div',\n  'dl',\n  'dt',\n  'embed',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'header',\n  'hgroup',\n  'hr',\n  'iframe',\n  'li',\n  'map',\n  'object',\n  'ol',\n  'output',\n  'p',\n  'pre',\n  'progress',\n  'script',\n  'section',\n  'style',\n  'table',\n  'tbody',\n  'td',\n  'textarea',\n  'tfoot',\n  'th',\n  'tr',\n  'thead',\n  'ul',\n  'video'\n].forEach(function (name) { html_blocks[name] = true; });\n\n// HTML block\n\n\nvar HTML_TAG_OPEN_RE = /^<([a-zA-Z]{1,15})[\\s\\/>]/;\nvar HTML_TAG_CLOSE_RE = /^<\\/([a-zA-Z]{1,15})[\\s>]/;\n\nfunction isLetter$1(ch) {\n  /*eslint no-bitwise:0*/\n  var lc = ch | 0x20; // to lower case\n  return (lc >= 0x61/* a */) && (lc <= 0x7a/* z */);\n}\n\nfunction htmlblock(state, startLine, endLine, silent) {\n  var ch, match, nextLine,\n      pos = state.bMarks[startLine],\n      max = state.eMarks[startLine],\n      shift = state.tShift[startLine];\n\n  pos += shift;\n\n  if (!state.options.html) { return false; }\n\n  if (shift > 3 || pos + 2 >= max) { return false; }\n\n  if (state.src.charCodeAt(pos) !== 0x3C/* < */) { return false; }\n\n  ch = state.src.charCodeAt(pos + 1);\n\n  if (ch === 0x21/* ! */ || ch === 0x3F/* ? */) {\n    // Directive start / comment start / processing instruction start\n    if (silent) { return true; }\n\n  } else if (ch === 0x2F/* / */ || isLetter$1(ch)) {\n\n    // Probably start or end of tag\n    if (ch === 0x2F/* \\ */) {\n      // closing tag\n      match = state.src.slice(pos, max).match(HTML_TAG_CLOSE_RE);\n      if (!match) { return false; }\n    } else {\n      // opening tag\n      match = state.src.slice(pos, max).match(HTML_TAG_OPEN_RE);\n      if (!match) { return false; }\n    }\n    // Make sure tag name is valid\n    if (html_blocks[match[1].toLowerCase()] !== true) { return false; }\n    if (silent) { return true; }\n\n  } else {\n    return false;\n  }\n\n  // If we are here - we detected HTML block.\n  // Let's roll down till empty line (block end).\n  nextLine = startLine + 1;\n  while (nextLine < state.lineMax && !state.isEmpty(nextLine)) {\n    nextLine++;\n  }\n\n  state.line = nextLine;\n  state.tokens.push({\n    type: 'htmlblock',\n    level: state.level,\n    lines: [ startLine, state.line ],\n    content: state.getLines(startLine, nextLine, 0, true)\n  });\n\n  return true;\n}\n\n// GFM table, non-standard\n\nfunction getLine(state, line) {\n  var pos = state.bMarks[line] + state.blkIndent,\n      max = state.eMarks[line];\n\n  return state.src.substr(pos, max - pos);\n}\n\nfunction table(state, startLine, endLine, silent) {\n  var ch, lineText, pos, i, nextLine, rows, cell,\n      aligns, t, tableLines, tbodyLines;\n\n  // should have at least three lines\n  if (startLine + 2 > endLine) { return false; }\n\n  nextLine = startLine + 1;\n\n  if (state.tShift[nextLine] < state.blkIndent) { return false; }\n\n  // first character of the second line should be '|' or '-'\n\n  pos = state.bMarks[nextLine] + state.tShift[nextLine];\n  if (pos >= state.eMarks[nextLine]) { return false; }\n\n  ch = state.src.charCodeAt(pos);\n  if (ch !== 0x7C/* | */ && ch !== 0x2D/* - */ && ch !== 0x3A/* : */) { return false; }\n\n  lineText = getLine(state, startLine + 1);\n  if (!/^[-:| ]+$/.test(lineText)) { return false; }\n\n  rows = lineText.split('|');\n  if (rows <= 2) { return false; }\n  aligns = [];\n  for (i = 0; i < rows.length; i++) {\n    t = rows[i].trim();\n    if (!t) {\n      // allow empty columns before and after table, but not in between columns;\n      // e.g. allow ` |---| `, disallow ` ---||--- `\n      if (i === 0 || i === rows.length - 1) {\n        continue;\n      } else {\n        return false;\n      }\n    }\n\n    if (!/^:?-+:?$/.test(t)) { return false; }\n    if (t.charCodeAt(t.length - 1) === 0x3A/* : */) {\n      aligns.push(t.charCodeAt(0) === 0x3A/* : */ ? 'center' : 'right');\n    } else if (t.charCodeAt(0) === 0x3A/* : */) {\n      aligns.push('left');\n    } else {\n      aligns.push('');\n    }\n  }\n\n  lineText = getLine(state, startLine).trim();\n  if (lineText.indexOf('|') === -1) { return false; }\n  rows = lineText.replace(/^\\||\\|$/g, '').split('|');\n  if (aligns.length !== rows.length) { return false; }\n  if (silent) { return true; }\n\n  state.tokens.push({\n    type: 'table_open',\n    lines: tableLines = [ startLine, 0 ],\n    level: state.level++\n  });\n  state.tokens.push({\n    type: 'thead_open',\n    lines: [ startLine, startLine + 1 ],\n    level: state.level++\n  });\n\n  state.tokens.push({\n    type: 'tr_open',\n    lines: [ startLine, startLine + 1 ],\n    level: state.level++\n  });\n  for (i = 0; i < rows.length; i++) {\n    state.tokens.push({\n      type: 'th_open',\n      align: aligns[i],\n      lines: [ startLine, startLine + 1 ],\n      level: state.level++\n    });\n    state.tokens.push({\n      type: 'inline',\n      content: rows[i].trim(),\n      lines: [ startLine, startLine + 1 ],\n      level: state.level,\n      children: []\n    });\n    state.tokens.push({ type: 'th_close', level: --state.level });\n  }\n  state.tokens.push({ type: 'tr_close', level: --state.level });\n  state.tokens.push({ type: 'thead_close', level: --state.level });\n\n  state.tokens.push({\n    type: 'tbody_open',\n    lines: tbodyLines = [ startLine + 2, 0 ],\n    level: state.level++\n  });\n\n  for (nextLine = startLine + 2; nextLine < endLine; nextLine++) {\n    if (state.tShift[nextLine] < state.blkIndent) { break; }\n\n    lineText = getLine(state, nextLine).trim();\n    if (lineText.indexOf('|') === -1) { break; }\n    rows = lineText.replace(/^\\||\\|$/g, '').split('|');\n\n    state.tokens.push({ type: 'tr_open', level: state.level++ });\n    for (i = 0; i < rows.length; i++) {\n      state.tokens.push({ type: 'td_open', align: aligns[i], level: state.level++ });\n      // 0x7c === '|'\n      cell = rows[i].substring(\n          rows[i].charCodeAt(0) === 0x7c ? 1 : 0,\n          rows[i].charCodeAt(rows[i].length - 1) === 0x7c ? rows[i].length - 1 : rows[i].length\n      ).trim();\n      state.tokens.push({\n        type: 'inline',\n        content: cell,\n        level: state.level,\n        children: []\n      });\n      state.tokens.push({ type: 'td_close', level: --state.level });\n    }\n    state.tokens.push({ type: 'tr_close', level: --state.level });\n  }\n  state.tokens.push({ type: 'tbody_close', level: --state.level });\n  state.tokens.push({ type: 'table_close', level: --state.level });\n\n  tableLines[1] = tbodyLines[1] = nextLine;\n  state.line = nextLine;\n  return true;\n}\n\n// Definition lists\n\n// Search `[:~][\\n ]`, returns next pos after marker on success\n// or -1 on fail.\nfunction skipMarker(state, line) {\n  var pos, marker,\n      start = state.bMarks[line] + state.tShift[line],\n      max = state.eMarks[line];\n\n  if (start >= max) { return -1; }\n\n  // Check bullet\n  marker = state.src.charCodeAt(start++);\n  if (marker !== 0x7E/* ~ */ && marker !== 0x3A/* : */) { return -1; }\n\n  pos = state.skipSpaces(start);\n\n  // require space after \":\"\n  if (start === pos) { return -1; }\n\n  // no empty definitions, e.g. \"  : \"\n  if (pos >= max) { return -1; }\n\n  return pos;\n}\n\nfunction markTightParagraphs$1(state, idx) {\n  var i, l,\n      level = state.level + 2;\n\n  for (i = idx + 2, l = state.tokens.length - 2; i < l; i++) {\n    if (state.tokens[i].level === level && state.tokens[i].type === 'paragraph_open') {\n      state.tokens[i + 2].tight = true;\n      state.tokens[i].tight = true;\n      i += 2;\n    }\n  }\n}\n\nfunction deflist(state, startLine, endLine, silent) {\n  var contentStart,\n      ddLine,\n      dtLine,\n      itemLines,\n      listLines,\n      listTokIdx,\n      nextLine,\n      oldIndent,\n      oldDDIndent,\n      oldParentType,\n      oldTShift,\n      oldTight,\n      prevEmptyEnd,\n      tight;\n\n  if (silent) {\n    // quirk: validation mode validates a dd block only, not a whole deflist\n    if (state.ddIndent < 0) { return false; }\n    return skipMarker(state, startLine) >= 0;\n  }\n\n  nextLine = startLine + 1;\n  if (state.isEmpty(nextLine)) {\n    if (++nextLine > endLine) { return false; }\n  }\n\n  if (state.tShift[nextLine] < state.blkIndent) { return false; }\n  contentStart = skipMarker(state, nextLine);\n  if (contentStart < 0) { return false; }\n\n  if (state.level >= state.options.maxNesting) { return false; }\n\n  // Start list\n  listTokIdx = state.tokens.length;\n\n  state.tokens.push({\n    type: 'dl_open',\n    lines: listLines = [ startLine, 0 ],\n    level: state.level++\n  });\n\n  //\n  // Iterate list items\n  //\n\n  dtLine = startLine;\n  ddLine = nextLine;\n\n  // One definition list can contain multiple DTs,\n  // and one DT can be followed by multiple DDs.\n  //\n  // Thus, there is two loops here, and label is\n  // needed to break out of the second one\n  //\n  /*eslint no-labels:0,block-scoped-var:0*/\n  OUTER:\n  for (;;) {\n    tight = true;\n    prevEmptyEnd = false;\n\n    state.tokens.push({\n      type: 'dt_open',\n      lines: [ dtLine, dtLine ],\n      level: state.level++\n    });\n    state.tokens.push({\n      type: 'inline',\n      content: state.getLines(dtLine, dtLine + 1, state.blkIndent, false).trim(),\n      level: state.level + 1,\n      lines: [ dtLine, dtLine ],\n      children: []\n    });\n    state.tokens.push({\n      type: 'dt_close',\n      level: --state.level\n    });\n\n    for (;;) {\n      state.tokens.push({\n        type: 'dd_open',\n        lines: itemLines = [ nextLine, 0 ],\n        level: state.level++\n      });\n\n      oldTight = state.tight;\n      oldDDIndent = state.ddIndent;\n      oldIndent = state.blkIndent;\n      oldTShift = state.tShift[ddLine];\n      oldParentType = state.parentType;\n      state.blkIndent = state.ddIndent = state.tShift[ddLine] + 2;\n      state.tShift[ddLine] = contentStart - state.bMarks[ddLine];\n      state.tight = true;\n      state.parentType = 'deflist';\n\n      state.parser.tokenize(state, ddLine, endLine, true);\n\n      // If any of list item is tight, mark list as tight\n      if (!state.tight || prevEmptyEnd) {\n        tight = false;\n      }\n      // Item become loose if finish with empty line,\n      // but we should filter last element, because it means list finish\n      prevEmptyEnd = (state.line - ddLine) > 1 && state.isEmpty(state.line - 1);\n\n      state.tShift[ddLine] = oldTShift;\n      state.tight = oldTight;\n      state.parentType = oldParentType;\n      state.blkIndent = oldIndent;\n      state.ddIndent = oldDDIndent;\n\n      state.tokens.push({\n        type: 'dd_close',\n        level: --state.level\n      });\n\n      itemLines[1] = nextLine = state.line;\n\n      if (nextLine >= endLine) { break OUTER; }\n\n      if (state.tShift[nextLine] < state.blkIndent) { break OUTER; }\n      contentStart = skipMarker(state, nextLine);\n      if (contentStart < 0) { break; }\n\n      ddLine = nextLine;\n\n      // go to the next loop iteration:\n      // insert DD tag and repeat checking\n    }\n\n    if (nextLine >= endLine) { break; }\n    dtLine = nextLine;\n\n    if (state.isEmpty(dtLine)) { break; }\n    if (state.tShift[dtLine] < state.blkIndent) { break; }\n\n    ddLine = dtLine + 1;\n    if (ddLine >= endLine) { break; }\n    if (state.isEmpty(ddLine)) { ddLine++; }\n    if (ddLine >= endLine) { break; }\n\n    if (state.tShift[ddLine] < state.blkIndent) { break; }\n    contentStart = skipMarker(state, ddLine);\n    if (contentStart < 0) { break; }\n\n    // go to the next loop iteration:\n    // insert DT and DD tags and repeat checking\n  }\n\n  // Finilize list\n  state.tokens.push({\n    type: 'dl_close',\n    level: --state.level\n  });\n  listLines[1] = nextLine;\n\n  state.line = nextLine;\n\n  // mark paragraphs tight if needed\n  if (tight) {\n    markTightParagraphs$1(state, listTokIdx);\n  }\n\n  return true;\n}\n\n// Paragraph\n\nfunction paragraph(state, startLine/*, endLine*/) {\n  var endLine, content, terminate, i, l,\n      nextLine = startLine + 1,\n      terminatorRules;\n\n  endLine = state.lineMax;\n\n  // jump line-by-line until empty one or EOF\n  if (nextLine < endLine && !state.isEmpty(nextLine)) {\n    terminatorRules = state.parser.ruler.getRules('paragraph');\n\n    for (; nextLine < endLine && !state.isEmpty(nextLine); nextLine++) {\n      // this would be a code block normally, but after paragraph\n      // it's considered a lazy continuation regardless of what's there\n      if (state.tShift[nextLine] - state.blkIndent > 3) { continue; }\n\n      // Some tags can terminate paragraph without empty line.\n      terminate = false;\n      for (i = 0, l = terminatorRules.length; i < l; i++) {\n        if (terminatorRules[i](state, nextLine, endLine, true)) {\n          terminate = true;\n          break;\n        }\n      }\n      if (terminate) { break; }\n    }\n  }\n\n  content = state.getLines(startLine, nextLine, state.blkIndent, false).trim();\n\n  state.line = nextLine;\n  if (content.length) {\n    state.tokens.push({\n      type: 'paragraph_open',\n      tight: false,\n      lines: [ startLine, state.line ],\n      level: state.level\n    });\n    state.tokens.push({\n      type: 'inline',\n      content: content,\n      level: state.level + 1,\n      lines: [ startLine, state.line ],\n      children: []\n    });\n    state.tokens.push({\n      type: 'paragraph_close',\n      tight: false,\n      level: state.level\n    });\n  }\n\n  return true;\n}\n\n/**\n * Parser rules\n */\n\nvar _rules$1 = [\n  [ 'code',       code ],\n  [ 'fences',     fences,     [ 'paragraph', 'blockquote', 'list' ] ],\n  [ 'blockquote', blockquote, [ 'paragraph', 'blockquote', 'list' ] ],\n  [ 'hr',         hr,         [ 'paragraph', 'blockquote', 'list' ] ],\n  [ 'list',       list,       [ 'paragraph', 'blockquote' ] ],\n  [ 'footnote',   footnote,   [ 'paragraph' ] ],\n  [ 'heading',    heading,    [ 'paragraph', 'blockquote' ] ],\n  [ 'lheading',   lheading ],\n  [ 'htmlblock',  htmlblock,  [ 'paragraph', 'blockquote' ] ],\n  [ 'table',      table,      [ 'paragraph' ] ],\n  [ 'deflist',    deflist,    [ 'paragraph' ] ],\n  [ 'paragraph',  paragraph ]\n];\n\n/**\n * Block Parser class\n *\n * @api private\n */\n\nfunction ParserBlock() {\n  this.ruler = new Ruler();\n  for (var i = 0; i < _rules$1.length; i++) {\n    this.ruler.push(_rules$1[i][0], _rules$1[i][1], {\n      alt: (_rules$1[i][2] || []).slice()\n    });\n  }\n}\n\n/**\n * Generate tokens for the given input range.\n *\n * @param  {Object} `state` Has properties like `src`, `parser`, `options` etc\n * @param  {Number} `startLine`\n * @param  {Number} `endLine`\n * @api private\n */\n\nParserBlock.prototype.tokenize = function (state, startLine, endLine) {\n  var rules = this.ruler.getRules('');\n  var len = rules.length;\n  var line = startLine;\n  var hasEmptyLines = false;\n  var ok, i;\n\n  while (line < endLine) {\n    state.line = line = state.skipEmptyLines(line);\n    if (line >= endLine) {\n      break;\n    }\n\n    // Termination condition for nested calls.\n    // Nested calls currently used for blockquotes & lists\n    if (state.tShift[line] < state.blkIndent) {\n      break;\n    }\n\n    // Try all possible rules.\n    // On success, rule should:\n    //\n    // - update `state.line`\n    // - update `state.tokens`\n    // - return true\n\n    for (i = 0; i < len; i++) {\n      ok = rules[i](state, line, endLine, false);\n      if (ok) {\n        break;\n      }\n    }\n\n    // set state.tight iff we had an empty line before current tag\n    // i.e. latest empty line should not count\n    state.tight = !hasEmptyLines;\n\n    // paragraph might \"eat\" one newline after it in nested lists\n    if (state.isEmpty(state.line - 1)) {\n      hasEmptyLines = true;\n    }\n\n    line = state.line;\n\n    if (line < endLine && state.isEmpty(line)) {\n      hasEmptyLines = true;\n      line++;\n\n      // two empty lines should stop the parser in list mode\n      if (line < endLine && state.parentType === 'list' && state.isEmpty(line)) { break; }\n      state.line = line;\n    }\n  }\n};\n\nvar TABS_SCAN_RE = /[\\n\\t]/g;\nvar NEWLINES_RE  = /\\r[\\n\\u0085]|[\\u2424\\u2028\\u0085]/g;\nvar SPACES_RE    = /\\u00a0/g;\n\n/**\n * Tokenize the given `str`.\n *\n * @param  {String} `str` Source string\n * @param  {Object} `options`\n * @param  {Object} `env`\n * @param  {Array} `outTokens`\n * @api private\n */\n\nParserBlock.prototype.parse = function (str, options, env, outTokens) {\n  var state, lineStart = 0, lastTabPos = 0;\n  if (!str) { return []; }\n\n  // Normalize spaces\n  str = str.replace(SPACES_RE, ' ');\n\n  // Normalize newlines\n  str = str.replace(NEWLINES_RE, '\\n');\n\n  // Replace tabs with proper number of spaces (1..4)\n  if (str.indexOf('\\t') >= 0) {\n    str = str.replace(TABS_SCAN_RE, function (match, offset) {\n      var result;\n      if (str.charCodeAt(offset) === 0x0A) {\n        lineStart = offset + 1;\n        lastTabPos = 0;\n        return match;\n      }\n      result = '    '.slice((offset - lineStart - lastTabPos) % 4);\n      lastTabPos = offset - lineStart + 1;\n      return result;\n    });\n  }\n\n  state = new StateBlock(str, this, options, env, outTokens);\n  this.tokenize(state, state.line, state.lineMax);\n};\n\n// Skip text characters for text token, place those to pending buffer\n// and increment current pos\n\n// Rule to skip pure text\n// '{}$%@~+=:' reserved for extentions\n\nfunction isTerminatorChar(ch) {\n  switch (ch) {\n    case 0x0A/* \\n */:\n    case 0x5C/* \\ */:\n    case 0x60/* ` */:\n    case 0x2A/* * */:\n    case 0x5F/* _ */:\n    case 0x5E/* ^ */:\n    case 0x5B/* [ */:\n    case 0x5D/* ] */:\n    case 0x21/* ! */:\n    case 0x26/* & */:\n    case 0x3C/* < */:\n    case 0x3E/* > */:\n    case 0x7B/* { */:\n    case 0x7D/* } */:\n    case 0x24/* $ */:\n    case 0x25/* % */:\n    case 0x40/* @ */:\n    case 0x7E/* ~ */:\n    case 0x2B/* + */:\n    case 0x3D/* = */:\n    case 0x3A/* : */:\n      return true;\n    default:\n      return false;\n  }\n}\n\nfunction text(state, silent) {\n  var pos = state.pos;\n\n  while (pos < state.posMax && !isTerminatorChar(state.src.charCodeAt(pos))) {\n    pos++;\n  }\n\n  if (pos === state.pos) { return false; }\n\n  if (!silent) { state.pending += state.src.slice(state.pos, pos); }\n\n  state.pos = pos;\n\n  return true;\n}\n\n// Proceess '\\n'\n\nfunction newline(state, silent) {\n  var pmax, max, pos = state.pos;\n\n  if (state.src.charCodeAt(pos) !== 0x0A/* \\n */) { return false; }\n\n  pmax = state.pending.length - 1;\n  max = state.posMax;\n\n  // '  \\n' -> hardbreak\n  // Lookup in pending chars is bad practice! Don't copy to other rules!\n  // Pending string is stored in concat mode, indexed lookups will cause\n  // convertion to flat mode.\n  if (!silent) {\n    if (pmax >= 0 && state.pending.charCodeAt(pmax) === 0x20) {\n      if (pmax >= 1 && state.pending.charCodeAt(pmax - 1) === 0x20) {\n        // Strip out all trailing spaces on this line.\n        for (var i = pmax - 2; i >= 0; i--) {\n          if (state.pending.charCodeAt(i) !== 0x20) {\n            state.pending = state.pending.substring(0, i + 1);\n            break;\n          }\n        }\n        state.push({\n          type: 'hardbreak',\n          level: state.level\n        });\n      } else {\n        state.pending = state.pending.slice(0, -1);\n        state.push({\n          type: 'softbreak',\n          level: state.level\n        });\n      }\n\n    } else {\n      state.push({\n        type: 'softbreak',\n        level: state.level\n      });\n    }\n  }\n\n  pos++;\n\n  // skip heading spaces for next line\n  while (pos < max && state.src.charCodeAt(pos) === 0x20) { pos++; }\n\n  state.pos = pos;\n  return true;\n}\n\n// Proceess escaped chars and hardbreaks\n\nvar ESCAPED = [];\n\nfor (var i = 0; i < 256; i++) { ESCAPED.push(0); }\n\n'\\\\!\"#$%&\\'()*+,./:;<=>?@[]^_`{|}~-'\n  .split('').forEach(function(ch) { ESCAPED[ch.charCodeAt(0)] = 1; });\n\n\nfunction escape(state, silent) {\n  var ch, pos = state.pos, max = state.posMax;\n\n  if (state.src.charCodeAt(pos) !== 0x5C/* \\ */) { return false; }\n\n  pos++;\n\n  if (pos < max) {\n    ch = state.src.charCodeAt(pos);\n\n    if (ch < 256 && ESCAPED[ch] !== 0) {\n      if (!silent) { state.pending += state.src[pos]; }\n      state.pos += 2;\n      return true;\n    }\n\n    if (ch === 0x0A) {\n      if (!silent) {\n        state.push({\n          type: 'hardbreak',\n          level: state.level\n        });\n      }\n\n      pos++;\n      // skip leading whitespaces from next line\n      while (pos < max && state.src.charCodeAt(pos) === 0x20) { pos++; }\n\n      state.pos = pos;\n      return true;\n    }\n  }\n\n  if (!silent) { state.pending += '\\\\'; }\n  state.pos++;\n  return true;\n}\n\n// Parse backticks\n\nfunction backticks(state, silent) {\n  var start, max, marker, matchStart, matchEnd,\n      pos = state.pos,\n      ch = state.src.charCodeAt(pos);\n\n  if (ch !== 0x60/* ` */) { return false; }\n\n  start = pos;\n  pos++;\n  max = state.posMax;\n\n  while (pos < max && state.src.charCodeAt(pos) === 0x60/* ` */) { pos++; }\n\n  marker = state.src.slice(start, pos);\n\n  matchStart = matchEnd = pos;\n\n  while ((matchStart = state.src.indexOf('`', matchEnd)) !== -1) {\n    matchEnd = matchStart + 1;\n\n    while (matchEnd < max && state.src.charCodeAt(matchEnd) === 0x60/* ` */) { matchEnd++; }\n\n    if (matchEnd - matchStart === marker.length) {\n      if (!silent) {\n        state.push({\n          type: 'code',\n          content: state.src.slice(pos, matchStart)\n                              .replace(/[ \\n]+/g, ' ')\n                              .trim(),\n          block: false,\n          level: state.level\n        });\n      }\n      state.pos = matchEnd;\n      return true;\n    }\n  }\n\n  if (!silent) { state.pending += marker; }\n  state.pos += marker.length;\n  return true;\n}\n\n// Process ~~deleted text~~\n\nfunction del(state, silent) {\n  var found,\n      pos,\n      stack,\n      max = state.posMax,\n      start = state.pos,\n      lastChar,\n      nextChar;\n\n  if (state.src.charCodeAt(start) !== 0x7E/* ~ */) { return false; }\n  if (silent) { return false; } // don't run any pairs in validation mode\n  if (start + 4 >= max) { return false; }\n  if (state.src.charCodeAt(start + 1) !== 0x7E/* ~ */) { return false; }\n  if (state.level >= state.options.maxNesting) { return false; }\n\n  lastChar = start > 0 ? state.src.charCodeAt(start - 1) : -1;\n  nextChar = state.src.charCodeAt(start + 2);\n\n  if (lastChar === 0x7E/* ~ */) { return false; }\n  if (nextChar === 0x7E/* ~ */) { return false; }\n  if (nextChar === 0x20 || nextChar === 0x0A) { return false; }\n\n  pos = start + 2;\n  while (pos < max && state.src.charCodeAt(pos) === 0x7E/* ~ */) { pos++; }\n  if (pos > start + 3) {\n    // sequence of 4+ markers taking as literal, same as in a emphasis\n    state.pos += pos - start;\n    if (!silent) { state.pending += state.src.slice(start, pos); }\n    return true;\n  }\n\n  state.pos = start + 2;\n  stack = 1;\n\n  while (state.pos + 1 < max) {\n    if (state.src.charCodeAt(state.pos) === 0x7E/* ~ */) {\n      if (state.src.charCodeAt(state.pos + 1) === 0x7E/* ~ */) {\n        lastChar = state.src.charCodeAt(state.pos - 1);\n        nextChar = state.pos + 2 < max ? state.src.charCodeAt(state.pos + 2) : -1;\n        if (nextChar !== 0x7E/* ~ */ && lastChar !== 0x7E/* ~ */) {\n          if (lastChar !== 0x20 && lastChar !== 0x0A) {\n            // closing '~~'\n            stack--;\n          } else if (nextChar !== 0x20 && nextChar !== 0x0A) {\n            // opening '~~'\n            stack++;\n          } // else {\n            //  // standalone ' ~~ ' indented with spaces\n            // }\n          if (stack <= 0) {\n            found = true;\n            break;\n          }\n        }\n      }\n    }\n\n    state.parser.skipToken(state);\n  }\n\n  if (!found) {\n    // parser failed to find ending tag, so it's not valid emphasis\n    state.pos = start;\n    return false;\n  }\n\n  // found!\n  state.posMax = state.pos;\n  state.pos = start + 2;\n\n  if (!silent) {\n    state.push({ type: 'del_open', level: state.level++ });\n    state.parser.tokenize(state);\n    state.push({ type: 'del_close', level: --state.level });\n  }\n\n  state.pos = state.posMax + 2;\n  state.posMax = max;\n  return true;\n}\n\n// Process ++inserted text++\n\nfunction ins(state, silent) {\n  var found,\n      pos,\n      stack,\n      max = state.posMax,\n      start = state.pos,\n      lastChar,\n      nextChar;\n\n  if (state.src.charCodeAt(start) !== 0x2B/* + */) { return false; }\n  if (silent) { return false; } // don't run any pairs in validation mode\n  if (start + 4 >= max) { return false; }\n  if (state.src.charCodeAt(start + 1) !== 0x2B/* + */) { return false; }\n  if (state.level >= state.options.maxNesting) { return false; }\n\n  lastChar = start > 0 ? state.src.charCodeAt(start - 1) : -1;\n  nextChar = state.src.charCodeAt(start + 2);\n\n  if (lastChar === 0x2B/* + */) { return false; }\n  if (nextChar === 0x2B/* + */) { return false; }\n  if (nextChar === 0x20 || nextChar === 0x0A) { return false; }\n\n  pos = start + 2;\n  while (pos < max && state.src.charCodeAt(pos) === 0x2B/* + */) { pos++; }\n  if (pos !== start + 2) {\n    // sequence of 3+ markers taking as literal, same as in a emphasis\n    state.pos += pos - start;\n    if (!silent) { state.pending += state.src.slice(start, pos); }\n    return true;\n  }\n\n  state.pos = start + 2;\n  stack = 1;\n\n  while (state.pos + 1 < max) {\n    if (state.src.charCodeAt(state.pos) === 0x2B/* + */) {\n      if (state.src.charCodeAt(state.pos + 1) === 0x2B/* + */) {\n        lastChar = state.src.charCodeAt(state.pos - 1);\n        nextChar = state.pos + 2 < max ? state.src.charCodeAt(state.pos + 2) : -1;\n        if (nextChar !== 0x2B/* + */ && lastChar !== 0x2B/* + */) {\n          if (lastChar !== 0x20 && lastChar !== 0x0A) {\n            // closing '++'\n            stack--;\n          } else if (nextChar !== 0x20 && nextChar !== 0x0A) {\n            // opening '++'\n            stack++;\n          } // else {\n            //  // standalone ' ++ ' indented with spaces\n            // }\n          if (stack <= 0) {\n            found = true;\n            break;\n          }\n        }\n      }\n    }\n\n    state.parser.skipToken(state);\n  }\n\n  if (!found) {\n    // parser failed to find ending tag, so it's not valid emphasis\n    state.pos = start;\n    return false;\n  }\n\n  // found!\n  state.posMax = state.pos;\n  state.pos = start + 2;\n\n  if (!silent) {\n    state.push({ type: 'ins_open', level: state.level++ });\n    state.parser.tokenize(state);\n    state.push({ type: 'ins_close', level: --state.level });\n  }\n\n  state.pos = state.posMax + 2;\n  state.posMax = max;\n  return true;\n}\n\n// Process ==highlighted text==\n\nfunction mark(state, silent) {\n  var found,\n      pos,\n      stack,\n      max = state.posMax,\n      start = state.pos,\n      lastChar,\n      nextChar;\n\n  if (state.src.charCodeAt(start) !== 0x3D/* = */) { return false; }\n  if (silent) { return false; } // don't run any pairs in validation mode\n  if (start + 4 >= max) { return false; }\n  if (state.src.charCodeAt(start + 1) !== 0x3D/* = */) { return false; }\n  if (state.level >= state.options.maxNesting) { return false; }\n\n  lastChar = start > 0 ? state.src.charCodeAt(start - 1) : -1;\n  nextChar = state.src.charCodeAt(start + 2);\n\n  if (lastChar === 0x3D/* = */) { return false; }\n  if (nextChar === 0x3D/* = */) { return false; }\n  if (nextChar === 0x20 || nextChar === 0x0A) { return false; }\n\n  pos = start + 2;\n  while (pos < max && state.src.charCodeAt(pos) === 0x3D/* = */) { pos++; }\n  if (pos !== start + 2) {\n    // sequence of 3+ markers taking as literal, same as in a emphasis\n    state.pos += pos - start;\n    if (!silent) { state.pending += state.src.slice(start, pos); }\n    return true;\n  }\n\n  state.pos = start + 2;\n  stack = 1;\n\n  while (state.pos + 1 < max) {\n    if (state.src.charCodeAt(state.pos) === 0x3D/* = */) {\n      if (state.src.charCodeAt(state.pos + 1) === 0x3D/* = */) {\n        lastChar = state.src.charCodeAt(state.pos - 1);\n        nextChar = state.pos + 2 < max ? state.src.charCodeAt(state.pos + 2) : -1;\n        if (nextChar !== 0x3D/* = */ && lastChar !== 0x3D/* = */) {\n          if (lastChar !== 0x20 && lastChar !== 0x0A) {\n            // closing '=='\n            stack--;\n          } else if (nextChar !== 0x20 && nextChar !== 0x0A) {\n            // opening '=='\n            stack++;\n          } // else {\n            //  // standalone ' == ' indented with spaces\n            // }\n          if (stack <= 0) {\n            found = true;\n            break;\n          }\n        }\n      }\n    }\n\n    state.parser.skipToken(state);\n  }\n\n  if (!found) {\n    // parser failed to find ending tag, so it's not valid emphasis\n    state.pos = start;\n    return false;\n  }\n\n  // found!\n  state.posMax = state.pos;\n  state.pos = start + 2;\n\n  if (!silent) {\n    state.push({ type: 'mark_open', level: state.level++ });\n    state.parser.tokenize(state);\n    state.push({ type: 'mark_close', level: --state.level });\n  }\n\n  state.pos = state.posMax + 2;\n  state.posMax = max;\n  return true;\n}\n\n// Process *this* and _that_\n\nfunction isAlphaNum(code) {\n  return (code >= 0x30 /* 0 */ && code <= 0x39 /* 9 */) ||\n         (code >= 0x41 /* A */ && code <= 0x5A /* Z */) ||\n         (code >= 0x61 /* a */ && code <= 0x7A /* z */);\n}\n\n// parse sequence of emphasis markers,\n// \"start\" should point at a valid marker\nfunction scanDelims(state, start) {\n  var pos = start, lastChar, nextChar, count,\n      can_open = true,\n      can_close = true,\n      max = state.posMax,\n      marker = state.src.charCodeAt(start);\n\n  lastChar = start > 0 ? state.src.charCodeAt(start - 1) : -1;\n\n  while (pos < max && state.src.charCodeAt(pos) === marker) { pos++; }\n  if (pos >= max) { can_open = false; }\n  count = pos - start;\n\n  if (count >= 4) {\n    // sequence of four or more unescaped markers can't start/end an emphasis\n    can_open = can_close = false;\n  } else {\n    nextChar = pos < max ? state.src.charCodeAt(pos) : -1;\n\n    // check whitespace conditions\n    if (nextChar === 0x20 || nextChar === 0x0A) { can_open = false; }\n    if (lastChar === 0x20 || lastChar === 0x0A) { can_close = false; }\n\n    if (marker === 0x5F /* _ */) {\n      // check if we aren't inside the word\n      if (isAlphaNum(lastChar)) { can_open = false; }\n      if (isAlphaNum(nextChar)) { can_close = false; }\n    }\n  }\n\n  return {\n    can_open: can_open,\n    can_close: can_close,\n    delims: count\n  };\n}\n\nfunction emphasis(state, silent) {\n  var startCount,\n      count,\n      found,\n      oldCount,\n      newCount,\n      stack,\n      res,\n      max = state.posMax,\n      start = state.pos,\n      marker = state.src.charCodeAt(start);\n\n  if (marker !== 0x5F/* _ */ && marker !== 0x2A /* * */) { return false; }\n  if (silent) { return false; } // don't run any pairs in validation mode\n\n  res = scanDelims(state, start);\n  startCount = res.delims;\n  if (!res.can_open) {\n    state.pos += startCount;\n    if (!silent) { state.pending += state.src.slice(start, state.pos); }\n    return true;\n  }\n\n  if (state.level >= state.options.maxNesting) { return false; }\n\n  state.pos = start + startCount;\n  stack = [ startCount ];\n\n  while (state.pos < max) {\n    if (state.src.charCodeAt(state.pos) === marker) {\n      res = scanDelims(state, state.pos);\n      count = res.delims;\n      if (res.can_close) {\n        oldCount = stack.pop();\n        newCount = count;\n\n        while (oldCount !== newCount) {\n          if (newCount < oldCount) {\n            stack.push(oldCount - newCount);\n            break;\n          }\n\n          // assert(newCount > oldCount)\n          newCount -= oldCount;\n\n          if (stack.length === 0) { break; }\n          state.pos += oldCount;\n          oldCount = stack.pop();\n        }\n\n        if (stack.length === 0) {\n          startCount = oldCount;\n          found = true;\n          break;\n        }\n        state.pos += count;\n        continue;\n      }\n\n      if (res.can_open) { stack.push(count); }\n      state.pos += count;\n      continue;\n    }\n\n    state.parser.skipToken(state);\n  }\n\n  if (!found) {\n    // parser failed to find ending tag, so it's not valid emphasis\n    state.pos = start;\n    return false;\n  }\n\n  // found!\n  state.posMax = state.pos;\n  state.pos = start + startCount;\n\n  if (!silent) {\n    if (startCount === 2 || startCount === 3) {\n      state.push({ type: 'strong_open', level: state.level++ });\n    }\n    if (startCount === 1 || startCount === 3) {\n      state.push({ type: 'em_open', level: state.level++ });\n    }\n\n    state.parser.tokenize(state);\n\n    if (startCount === 1 || startCount === 3) {\n      state.push({ type: 'em_close', level: --state.level });\n    }\n    if (startCount === 2 || startCount === 3) {\n      state.push({ type: 'strong_close', level: --state.level });\n    }\n  }\n\n  state.pos = state.posMax + startCount;\n  state.posMax = max;\n  return true;\n}\n\n// Process ~subscript~\n\n// same as UNESCAPE_MD_RE plus a space\nvar UNESCAPE_RE = /\\\\([ \\\\!\"#$%&'()*+,.\\/:;<=>?@[\\]^_`{|}~-])/g;\n\nfunction sub(state, silent) {\n  var found,\n      content,\n      max = state.posMax,\n      start = state.pos;\n\n  if (state.src.charCodeAt(start) !== 0x7E/* ~ */) { return false; }\n  if (silent) { return false; } // don't run any pairs in validation mode\n  if (start + 2 >= max) { return false; }\n  if (state.level >= state.options.maxNesting) { return false; }\n\n  state.pos = start + 1;\n\n  while (state.pos < max) {\n    if (state.src.charCodeAt(state.pos) === 0x7E/* ~ */) {\n      found = true;\n      break;\n    }\n\n    state.parser.skipToken(state);\n  }\n\n  if (!found || start + 1 === state.pos) {\n    state.pos = start;\n    return false;\n  }\n\n  content = state.src.slice(start + 1, state.pos);\n\n  // don't allow unescaped spaces/newlines inside\n  if (content.match(/(^|[^\\\\])(\\\\\\\\)*\\s/)) {\n    state.pos = start;\n    return false;\n  }\n\n  // found!\n  state.posMax = state.pos;\n  state.pos = start + 1;\n\n  if (!silent) {\n    state.push({\n      type: 'sub',\n      level: state.level,\n      content: content.replace(UNESCAPE_RE, '$1')\n    });\n  }\n\n  state.pos = state.posMax + 1;\n  state.posMax = max;\n  return true;\n}\n\n// Process ^superscript^\n\n// same as UNESCAPE_MD_RE plus a space\nvar UNESCAPE_RE$1 = /\\\\([ \\\\!\"#$%&'()*+,.\\/:;<=>?@[\\]^_`{|}~-])/g;\n\nfunction sup(state, silent) {\n  var found,\n      content,\n      max = state.posMax,\n      start = state.pos;\n\n  if (state.src.charCodeAt(start) !== 0x5E/* ^ */) { return false; }\n  if (silent) { return false; } // don't run any pairs in validation mode\n  if (start + 2 >= max) { return false; }\n  if (state.level >= state.options.maxNesting) { return false; }\n\n  state.pos = start + 1;\n\n  while (state.pos < max) {\n    if (state.src.charCodeAt(state.pos) === 0x5E/* ^ */) {\n      found = true;\n      break;\n    }\n\n    state.parser.skipToken(state);\n  }\n\n  if (!found || start + 1 === state.pos) {\n    state.pos = start;\n    return false;\n  }\n\n  content = state.src.slice(start + 1, state.pos);\n\n  // don't allow unescaped spaces/newlines inside\n  if (content.match(/(^|[^\\\\])(\\\\\\\\)*\\s/)) {\n    state.pos = start;\n    return false;\n  }\n\n  // found!\n  state.posMax = state.pos;\n  state.pos = start + 1;\n\n  if (!silent) {\n    state.push({\n      type: 'sup',\n      level: state.level,\n      content: content.replace(UNESCAPE_RE$1, '$1')\n    });\n  }\n\n  state.pos = state.posMax + 1;\n  state.posMax = max;\n  return true;\n}\n\n// Process [links](<to> \"stuff\")\n\n\nfunction links(state, silent) {\n  var labelStart,\n      labelEnd,\n      label,\n      href,\n      title,\n      pos,\n      ref,\n      code,\n      isImage = false,\n      oldPos = state.pos,\n      max = state.posMax,\n      start = state.pos,\n      marker = state.src.charCodeAt(start);\n\n  if (marker === 0x21/* ! */) {\n    isImage = true;\n    marker = state.src.charCodeAt(++start);\n  }\n\n  if (marker !== 0x5B/* [ */) { return false; }\n  if (state.level >= state.options.maxNesting) { return false; }\n\n  labelStart = start + 1;\n  labelEnd = parseLinkLabel(state, start);\n\n  // parser failed to find ']', so it's not a valid link\n  if (labelEnd < 0) { return false; }\n\n  pos = labelEnd + 1;\n  if (pos < max && state.src.charCodeAt(pos) === 0x28/* ( */) {\n    //\n    // Inline link\n    //\n\n    // [link](  <href>  \"title\"  )\n    //        ^^ skipping these spaces\n    pos++;\n    for (; pos < max; pos++) {\n      code = state.src.charCodeAt(pos);\n      if (code !== 0x20 && code !== 0x0A) { break; }\n    }\n    if (pos >= max) { return false; }\n\n    // [link](  <href>  \"title\"  )\n    //          ^^^^^^ parsing link destination\n    start = pos;\n    if (parseLinkDestination(state, pos)) {\n      href = state.linkContent;\n      pos = state.pos;\n    } else {\n      href = '';\n    }\n\n    // [link](  <href>  \"title\"  )\n    //                ^^ skipping these spaces\n    start = pos;\n    for (; pos < max; pos++) {\n      code = state.src.charCodeAt(pos);\n      if (code !== 0x20 && code !== 0x0A) { break; }\n    }\n\n    // [link](  <href>  \"title\"  )\n    //                  ^^^^^^^ parsing link title\n    if (pos < max && start !== pos && parseLinkTitle(state, pos)) {\n      title = state.linkContent;\n      pos = state.pos;\n\n      // [link](  <href>  \"title\"  )\n      //                         ^^ skipping these spaces\n      for (; pos < max; pos++) {\n        code = state.src.charCodeAt(pos);\n        if (code !== 0x20 && code !== 0x0A) { break; }\n      }\n    } else {\n      title = '';\n    }\n\n    if (pos >= max || state.src.charCodeAt(pos) !== 0x29/* ) */) {\n      state.pos = oldPos;\n      return false;\n    }\n    pos++;\n  } else {\n    //\n    // Link reference\n    //\n\n    // do not allow nested reference links\n    if (state.linkLevel > 0) { return false; }\n\n    // [foo]  [bar]\n    //      ^^ optional whitespace (can include newlines)\n    for (; pos < max; pos++) {\n      code = state.src.charCodeAt(pos);\n      if (code !== 0x20 && code !== 0x0A) { break; }\n    }\n\n    if (pos < max && state.src.charCodeAt(pos) === 0x5B/* [ */) {\n      start = pos + 1;\n      pos = parseLinkLabel(state, pos);\n      if (pos >= 0) {\n        label = state.src.slice(start, pos++);\n      } else {\n        pos = start - 1;\n      }\n    }\n\n    // covers label === '' and label === undefined\n    // (collapsed reference link and shortcut reference link respectively)\n    if (!label) {\n      if (typeof label === 'undefined') {\n        pos = labelEnd + 1;\n      }\n      label = state.src.slice(labelStart, labelEnd);\n    }\n\n    ref = state.env.references[normalizeReference(label)];\n    if (!ref) {\n      state.pos = oldPos;\n      return false;\n    }\n    href = ref.href;\n    title = ref.title;\n  }\n\n  //\n  // We found the end of the link, and know for a fact it's a valid link;\n  // so all that's left to do is to call tokenizer.\n  //\n  if (!silent) {\n    state.pos = labelStart;\n    state.posMax = labelEnd;\n\n    if (isImage) {\n      state.push({\n        type: 'image',\n        src: href,\n        title: title,\n        alt: state.src.substr(labelStart, labelEnd - labelStart),\n        level: state.level\n      });\n    } else {\n      state.push({\n        type: 'link_open',\n        href: href,\n        title: title,\n        level: state.level++\n      });\n      state.linkLevel++;\n      state.parser.tokenize(state);\n      state.linkLevel--;\n      state.push({ type: 'link_close', level: --state.level });\n    }\n  }\n\n  state.pos = pos;\n  state.posMax = max;\n  return true;\n}\n\n// Process inline footnotes (^[...])\n\n\nfunction footnote_inline(state, silent) {\n  var labelStart,\n      labelEnd,\n      footnoteId,\n      oldLength,\n      max = state.posMax,\n      start = state.pos;\n\n  if (start + 2 >= max) { return false; }\n  if (state.src.charCodeAt(start) !== 0x5E/* ^ */) { return false; }\n  if (state.src.charCodeAt(start + 1) !== 0x5B/* [ */) { return false; }\n  if (state.level >= state.options.maxNesting) { return false; }\n\n  labelStart = start + 2;\n  labelEnd = parseLinkLabel(state, start + 1);\n\n  // parser failed to find ']', so it's not a valid note\n  if (labelEnd < 0) { return false; }\n\n  // We found the end of the link, and know for a fact it's a valid link;\n  // so all that's left to do is to call tokenizer.\n  //\n  if (!silent) {\n    if (!state.env.footnotes) { state.env.footnotes = {}; }\n    if (!state.env.footnotes.list) { state.env.footnotes.list = []; }\n    footnoteId = state.env.footnotes.list.length;\n\n    state.pos = labelStart;\n    state.posMax = labelEnd;\n\n    state.push({\n      type: 'footnote_ref',\n      id: footnoteId,\n      level: state.level\n    });\n    state.linkLevel++;\n    oldLength = state.tokens.length;\n    state.parser.tokenize(state);\n    state.env.footnotes.list[footnoteId] = { tokens: state.tokens.splice(oldLength) };\n    state.linkLevel--;\n  }\n\n  state.pos = labelEnd + 1;\n  state.posMax = max;\n  return true;\n}\n\n// Process footnote references ([^...])\n\nfunction footnote_ref(state, silent) {\n  var label,\n      pos,\n      footnoteId,\n      footnoteSubId,\n      max = state.posMax,\n      start = state.pos;\n\n  // should be at least 4 chars - \"[^x]\"\n  if (start + 3 > max) { return false; }\n\n  if (!state.env.footnotes || !state.env.footnotes.refs) { return false; }\n  if (state.src.charCodeAt(start) !== 0x5B/* [ */) { return false; }\n  if (state.src.charCodeAt(start + 1) !== 0x5E/* ^ */) { return false; }\n  if (state.level >= state.options.maxNesting) { return false; }\n\n  for (pos = start + 2; pos < max; pos++) {\n    if (state.src.charCodeAt(pos) === 0x20) { return false; }\n    if (state.src.charCodeAt(pos) === 0x0A) { return false; }\n    if (state.src.charCodeAt(pos) === 0x5D /* ] */) {\n      break;\n    }\n  }\n\n  if (pos === start + 2) { return false; } // no empty footnote labels\n  if (pos >= max) { return false; }\n  pos++;\n\n  label = state.src.slice(start + 2, pos - 1);\n  if (typeof state.env.footnotes.refs[':' + label] === 'undefined') { return false; }\n\n  if (!silent) {\n    if (!state.env.footnotes.list) { state.env.footnotes.list = []; }\n\n    if (state.env.footnotes.refs[':' + label] < 0) {\n      footnoteId = state.env.footnotes.list.length;\n      state.env.footnotes.list[footnoteId] = { label: label, count: 0 };\n      state.env.footnotes.refs[':' + label] = footnoteId;\n    } else {\n      footnoteId = state.env.footnotes.refs[':' + label];\n    }\n\n    footnoteSubId = state.env.footnotes.list[footnoteId].count;\n    state.env.footnotes.list[footnoteId].count++;\n\n    state.push({\n      type: 'footnote_ref',\n      id: footnoteId,\n      subId: footnoteSubId,\n      level: state.level\n    });\n  }\n\n  state.pos = pos;\n  state.posMax = max;\n  return true;\n}\n\n// List of valid url schemas, accorting to commonmark spec\n// http://jgm.github.io/CommonMark/spec.html#autolinks\n\nvar url_schemas = [\n  'coap',\n  'doi',\n  'javascript',\n  'aaa',\n  'aaas',\n  'about',\n  'acap',\n  'cap',\n  'cid',\n  'crid',\n  'data',\n  'dav',\n  'dict',\n  'dns',\n  'file',\n  'ftp',\n  'geo',\n  'go',\n  'gopher',\n  'h323',\n  'http',\n  'https',\n  'iax',\n  'icap',\n  'im',\n  'imap',\n  'info',\n  'ipp',\n  'iris',\n  'iris.beep',\n  'iris.xpc',\n  'iris.xpcs',\n  'iris.lwz',\n  'ldap',\n  'mailto',\n  'mid',\n  'msrp',\n  'msrps',\n  'mtqp',\n  'mupdate',\n  'news',\n  'nfs',\n  'ni',\n  'nih',\n  'nntp',\n  'opaquelocktoken',\n  'pop',\n  'pres',\n  'rtsp',\n  'service',\n  'session',\n  'shttp',\n  'sieve',\n  'sip',\n  'sips',\n  'sms',\n  'snmp',\n  'soap.beep',\n  'soap.beeps',\n  'tag',\n  'tel',\n  'telnet',\n  'tftp',\n  'thismessage',\n  'tn3270',\n  'tip',\n  'tv',\n  'urn',\n  'vemmi',\n  'ws',\n  'wss',\n  'xcon',\n  'xcon-userid',\n  'xmlrpc.beep',\n  'xmlrpc.beeps',\n  'xmpp',\n  'z39.50r',\n  'z39.50s',\n  'adiumxtra',\n  'afp',\n  'afs',\n  'aim',\n  'apt',\n  'attachment',\n  'aw',\n  'beshare',\n  'bitcoin',\n  'bolo',\n  'callto',\n  'chrome',\n  'chrome-extension',\n  'com-eventbrite-attendee',\n  'content',\n  'cvs',\n  'dlna-playsingle',\n  'dlna-playcontainer',\n  'dtn',\n  'dvb',\n  'ed2k',\n  'facetime',\n  'feed',\n  'finger',\n  'fish',\n  'gg',\n  'git',\n  'gizmoproject',\n  'gtalk',\n  'hcp',\n  'icon',\n  'ipn',\n  'irc',\n  'irc6',\n  'ircs',\n  'itms',\n  'jar',\n  'jms',\n  'keyparc',\n  'lastfm',\n  'ldaps',\n  'magnet',\n  'maps',\n  'market',\n  'message',\n  'mms',\n  'ms-help',\n  'msnim',\n  'mumble',\n  'mvn',\n  'notes',\n  'oid',\n  'palm',\n  'paparazzi',\n  'platform',\n  'proxy',\n  'psyc',\n  'query',\n  'res',\n  'resource',\n  'rmi',\n  'rsync',\n  'rtmp',\n  'secondlife',\n  'sftp',\n  'sgn',\n  'skype',\n  'smb',\n  'soldat',\n  'spotify',\n  'ssh',\n  'steam',\n  'svn',\n  'teamspeak',\n  'things',\n  'udp',\n  'unreal',\n  'ut2004',\n  'ventrilo',\n  'view-source',\n  'webcal',\n  'wtai',\n  'wyciwyg',\n  'xfire',\n  'xri',\n  'ymsgr'\n];\n\n// Process autolinks '<protocol:...>'\n\n\n/*eslint max-len:0*/\nvar EMAIL_RE    = /^<([a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)>/;\nvar AUTOLINK_RE = /^<([a-zA-Z.\\-]{1,25}):([^<>\\x00-\\x20]*)>/;\n\n\nfunction autolink(state, silent) {\n  var tail, linkMatch, emailMatch, url, fullUrl, pos = state.pos;\n\n  if (state.src.charCodeAt(pos) !== 0x3C/* < */) { return false; }\n\n  tail = state.src.slice(pos);\n\n  if (tail.indexOf('>') < 0) { return false; }\n\n  linkMatch = tail.match(AUTOLINK_RE);\n\n  if (linkMatch) {\n    if (url_schemas.indexOf(linkMatch[1].toLowerCase()) < 0) { return false; }\n\n    url = linkMatch[0].slice(1, -1);\n    fullUrl = normalizeLink(url);\n    if (!state.parser.validateLink(url)) { return false; }\n\n    if (!silent) {\n      state.push({\n        type: 'link_open',\n        href: fullUrl,\n        level: state.level\n      });\n      state.push({\n        type: 'text',\n        content: url,\n        level: state.level + 1\n      });\n      state.push({ type: 'link_close', level: state.level });\n    }\n\n    state.pos += linkMatch[0].length;\n    return true;\n  }\n\n  emailMatch = tail.match(EMAIL_RE);\n\n  if (emailMatch) {\n\n    url = emailMatch[0].slice(1, -1);\n\n    fullUrl = normalizeLink('mailto:' + url);\n    if (!state.parser.validateLink(fullUrl)) { return false; }\n\n    if (!silent) {\n      state.push({\n        type: 'link_open',\n        href: fullUrl,\n        level: state.level\n      });\n      state.push({\n        type: 'text',\n        content: url,\n        level: state.level + 1\n      });\n      state.push({ type: 'link_close', level: state.level });\n    }\n\n    state.pos += emailMatch[0].length;\n    return true;\n  }\n\n  return false;\n}\n\n// Regexps to match html elements\n\nfunction replace$1(regex, options) {\n  regex = regex.source;\n  options = options || '';\n\n  return function self(name, val) {\n    if (!name) {\n      return new RegExp(regex, options);\n    }\n    val = val.source || val;\n    regex = regex.replace(name, val);\n    return self;\n  };\n}\n\n\nvar attr_name     = /[a-zA-Z_:][a-zA-Z0-9:._-]*/;\n\nvar unquoted      = /[^\"'=<>`\\x00-\\x20]+/;\nvar single_quoted = /'[^']*'/;\nvar double_quoted = /\"[^\"]*\"/;\n\n/*eslint no-spaced-func:0*/\nvar attr_value  = replace$1(/(?:unquoted|single_quoted|double_quoted)/)\n                    ('unquoted', unquoted)\n                    ('single_quoted', single_quoted)\n                    ('double_quoted', double_quoted)\n                    ();\n\nvar attribute   = replace$1(/(?:\\s+attr_name(?:\\s*=\\s*attr_value)?)/)\n                    ('attr_name', attr_name)\n                    ('attr_value', attr_value)\n                    ();\n\nvar open_tag    = replace$1(/<[A-Za-z][A-Za-z0-9]*attribute*\\s*\\/?>/)\n                    ('attribute', attribute)\n                    ();\n\nvar close_tag   = /<\\/[A-Za-z][A-Za-z0-9]*\\s*>/;\nvar comment     = /<!---->|<!--(?:-?[^>-])(?:-?[^-])*-->/;\nvar processing  = /<[?].*?[?]>/;\nvar declaration = /<![A-Z]+\\s+[^>]*>/;\nvar cdata       = /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/;\n\nvar HTML_TAG_RE = replace$1(/^(?:open_tag|close_tag|comment|processing|declaration|cdata)/)\n  ('open_tag', open_tag)\n  ('close_tag', close_tag)\n  ('comment', comment)\n  ('processing', processing)\n  ('declaration', declaration)\n  ('cdata', cdata)\n  ();\n\n// Process html tags\n\n\nfunction isLetter$2(ch) {\n  /*eslint no-bitwise:0*/\n  var lc = ch | 0x20; // to lower case\n  return (lc >= 0x61/* a */) && (lc <= 0x7a/* z */);\n}\n\n\nfunction htmltag(state, silent) {\n  var ch, match, max, pos = state.pos;\n\n  if (!state.options.html) { return false; }\n\n  // Check start\n  max = state.posMax;\n  if (state.src.charCodeAt(pos) !== 0x3C/* < */ ||\n      pos + 2 >= max) {\n    return false;\n  }\n\n  // Quick fail on second char\n  ch = state.src.charCodeAt(pos + 1);\n  if (ch !== 0x21/* ! */ &&\n      ch !== 0x3F/* ? */ &&\n      ch !== 0x2F/* / */ &&\n      !isLetter$2(ch)) {\n    return false;\n  }\n\n  match = state.src.slice(pos).match(HTML_TAG_RE);\n  if (!match) { return false; }\n\n  if (!silent) {\n    state.push({\n      type: 'htmltag',\n      content: state.src.slice(pos, pos + match[0].length),\n      level: state.level\n    });\n  }\n  state.pos += match[0].length;\n  return true;\n}\n\n// Process html entity - &#123;, &#xAF;, &quot;, ...\n\n\nvar DIGITAL_RE = /^&#((?:x[a-f0-9]{1,8}|[0-9]{1,8}));/i;\nvar NAMED_RE   = /^&([a-z][a-z0-9]{1,31});/i;\n\n\nfunction entity(state, silent) {\n  var ch, code, match, pos = state.pos, max = state.posMax;\n\n  if (state.src.charCodeAt(pos) !== 0x26/* & */) { return false; }\n\n  if (pos + 1 < max) {\n    ch = state.src.charCodeAt(pos + 1);\n\n    if (ch === 0x23 /* # */) {\n      match = state.src.slice(pos).match(DIGITAL_RE);\n      if (match) {\n        if (!silent) {\n          code = match[1][0].toLowerCase() === 'x' ? parseInt(match[1].slice(1), 16) : parseInt(match[1], 10);\n          state.pending += isValidEntityCode(code) ? fromCodePoint(code) : fromCodePoint(0xFFFD);\n        }\n        state.pos += match[0].length;\n        return true;\n      }\n    } else {\n      match = state.src.slice(pos).match(NAMED_RE);\n      if (match) {\n        var decoded = decodeEntity(match[1]);\n        if (match[1] !== decoded) {\n          if (!silent) { state.pending += decoded; }\n          state.pos += match[0].length;\n          return true;\n        }\n      }\n    }\n  }\n\n  if (!silent) { state.pending += '&'; }\n  state.pos++;\n  return true;\n}\n\n/**\n * Inline Parser `rules`\n */\n\nvar _rules$2 = [\n  [ 'text',            text ],\n  [ 'newline',         newline ],\n  [ 'escape',          escape ],\n  [ 'backticks',       backticks ],\n  [ 'del',             del ],\n  [ 'ins',             ins ],\n  [ 'mark',            mark ],\n  [ 'emphasis',        emphasis ],\n  [ 'sub',             sub ],\n  [ 'sup',             sup ],\n  [ 'links',           links ],\n  [ 'footnote_inline', footnote_inline ],\n  [ 'footnote_ref',    footnote_ref ],\n  [ 'autolink',        autolink ],\n  [ 'htmltag',         htmltag ],\n  [ 'entity',          entity ]\n];\n\n/**\n * Inline Parser class. Note that link validation is stricter\n * in Remarkable than what is specified by CommonMark. If you\n * want to change this you can use a custom validator.\n *\n * @api private\n */\n\nfunction ParserInline() {\n  this.ruler = new Ruler();\n  for (var i = 0; i < _rules$2.length; i++) {\n    this.ruler.push(_rules$2[i][0], _rules$2[i][1]);\n  }\n\n  // Can be overridden with a custom validator\n  this.validateLink = validateLink;\n}\n\n/**\n * Skip a single token by running all rules in validation mode.\n * Returns `true` if any rule reports success.\n *\n * @param  {Object} `state`\n * @api privage\n */\n\nParserInline.prototype.skipToken = function (state) {\n  var rules = this.ruler.getRules('');\n  var len = rules.length;\n  var pos = state.pos;\n  var i, cached_pos;\n\n  if ((cached_pos = state.cacheGet(pos)) > 0) {\n    state.pos = cached_pos;\n    return;\n  }\n\n  for (i = 0; i < len; i++) {\n    if (rules[i](state, true)) {\n      state.cacheSet(pos, state.pos);\n      return;\n    }\n  }\n\n  state.pos++;\n  state.cacheSet(pos, state.pos);\n};\n\n/**\n * Generate tokens for the given input range.\n *\n * @param  {Object} `state`\n * @api private\n */\n\nParserInline.prototype.tokenize = function (state) {\n  var rules = this.ruler.getRules('');\n  var len = rules.length;\n  var end = state.posMax;\n  var ok, i;\n\n  while (state.pos < end) {\n\n    // Try all possible rules.\n    // On success, the rule should:\n    //\n    // - update `state.pos`\n    // - update `state.tokens`\n    // - return true\n    for (i = 0; i < len; i++) {\n      ok = rules[i](state, false);\n\n      if (ok) {\n        break;\n      }\n    }\n\n    if (ok) {\n      if (state.pos >= end) { break; }\n      continue;\n    }\n\n    state.pending += state.src[state.pos++];\n  }\n\n  if (state.pending) {\n    state.pushPending();\n  }\n};\n\n/**\n * Parse the given input string.\n *\n * @param  {String} `str`\n * @param  {Object} `options`\n * @param  {Object} `env`\n * @param  {Array} `outTokens`\n * @api private\n */\n\nParserInline.prototype.parse = function (str, options, env, outTokens) {\n  var state = new StateInline(str, this, options, env, outTokens);\n  this.tokenize(state);\n};\n\n/**\n * Validate the given `url` by checking for bad protocols.\n *\n * @param  {String} `url`\n * @return {Boolean}\n */\n\nfunction validateLink(url) {\n  var BAD_PROTOCOLS = [ 'vbscript', 'javascript', 'file', 'data' ];\n  var str = url.trim().toLowerCase();\n  // Care about digital entities \"javascript&#x3A;alert(1)\"\n  str = replaceEntities(str);\n  if (str.indexOf(':') !== -1 && BAD_PROTOCOLS.indexOf(str.split(':')[0]) !== -1) {\n    return false;\n  }\n  return true;\n}\n\n// Remarkable default options\n\nvar defaultConfig = {\n  options: {\n    html:         false,        // Enable HTML tags in source\n    xhtmlOut:     false,        // Use '/' to close single tags (<br />)\n    breaks:       false,        // Convert '\\n' in paragraphs into <br>\n    langPrefix:   'language-',  // CSS language prefix for fenced blocks\n    linkTarget:   '',           // set target to open link in\n\n    // Enable some language-neutral replacements + quotes beautification\n    typographer:  false,\n\n    // Double + single quotes replacement pairs, when typographer enabled,\n    // and smartquotes on. Set doubles to '«»' for Russian, '„“' for German.\n    quotes: '“”‘’',\n\n    // Highlighter function. Should return escaped HTML,\n    // or '' if input not changed\n    //\n    // function (/*str, lang*/) { return ''; }\n    //\n    highlight: null,\n\n    maxNesting:   20            // Internal protection, recursion limit\n  },\n\n  components: {\n\n    core: {\n      rules: [\n        'block',\n        'inline',\n        'references',\n        'replacements',\n        'smartquotes',\n        'references',\n        'abbr2',\n        'footnote_tail'\n      ]\n    },\n\n    block: {\n      rules: [\n        'blockquote',\n        'code',\n        'fences',\n        'footnote',\n        'heading',\n        'hr',\n        'htmlblock',\n        'lheading',\n        'list',\n        'paragraph',\n        'table'\n      ]\n    },\n\n    inline: {\n      rules: [\n        'autolink',\n        'backticks',\n        'del',\n        'emphasis',\n        'entity',\n        'escape',\n        'footnote_ref',\n        'htmltag',\n        'links',\n        'newline',\n        'text'\n      ]\n    }\n  }\n};\n\n// Remarkable default options\n\nvar fullConfig = {\n  options: {\n    html:         false,        // Enable HTML tags in source\n    xhtmlOut:     false,        // Use '/' to close single tags (<br />)\n    breaks:       false,        // Convert '\\n' in paragraphs into <br>\n    langPrefix:   'language-',  // CSS language prefix for fenced blocks\n    linkTarget:   '',           // set target to open link in\n\n    // Enable some language-neutral replacements + quotes beautification\n    typographer:  false,\n\n    // Double + single quotes replacement pairs, when typographer enabled,\n    // and smartquotes on. Set doubles to '«»' for Russian, '„“' for German.\n    quotes:       '“”‘’',\n\n    // Highlighter function. Should return escaped HTML,\n    // or '' if input not changed\n    //\n    // function (/*str, lang*/) { return ''; }\n    //\n    highlight:     null,\n\n    maxNesting:    20            // Internal protection, recursion limit\n  },\n\n  components: {\n    // Don't restrict core/block/inline rules\n    core: {},\n    block: {},\n    inline: {}\n  }\n};\n\n// Commonmark default options\n\nvar commonmarkConfig = {\n  options: {\n    html:         true,         // Enable HTML tags in source\n    xhtmlOut:     true,         // Use '/' to close single tags (<br />)\n    breaks:       false,        // Convert '\\n' in paragraphs into <br>\n    langPrefix:   'language-',  // CSS language prefix for fenced blocks\n    linkTarget:   '',           // set target to open link in\n\n    // Enable some language-neutral replacements + quotes beautification\n    typographer:  false,\n\n    // Double + single quotes replacement pairs, when typographer enabled,\n    // and smartquotes on. Set doubles to '«»' for Russian, '„“' for German.\n    quotes: '“”‘’',\n\n    // Highlighter function. Should return escaped HTML,\n    // or '' if input not changed\n    //\n    // function (/*str, lang*/) { return ''; }\n    //\n    highlight: null,\n\n    maxNesting:   20            // Internal protection, recursion limit\n  },\n\n  components: {\n\n    core: {\n      rules: [\n        'block',\n        'inline',\n        'references',\n        'abbr2'\n      ]\n    },\n\n    block: {\n      rules: [\n        'blockquote',\n        'code',\n        'fences',\n        'heading',\n        'hr',\n        'htmlblock',\n        'lheading',\n        'list',\n        'paragraph'\n      ]\n    },\n\n    inline: {\n      rules: [\n        'autolink',\n        'backticks',\n        'emphasis',\n        'entity',\n        'escape',\n        'htmltag',\n        'links',\n        'newline',\n        'text'\n      ]\n    }\n  }\n};\n\n/**\n * Preset configs\n */\n\nvar config = {\n  'default': defaultConfig,\n  'full': fullConfig,\n  'commonmark': commonmarkConfig\n};\n\n/**\n * The `StateCore` class manages state.\n *\n * @param {Object} `instance` Remarkable instance\n * @param {String} `str` Markdown string\n * @param {Object} `env`\n */\n\nfunction StateCore(instance, str, env) {\n  this.src = str;\n  this.env = env;\n  this.options = instance.options;\n  this.tokens = [];\n  this.inlineMode = false;\n\n  this.inline = instance.inline;\n  this.block = instance.block;\n  this.renderer = instance.renderer;\n  this.typographer = instance.typographer;\n}\n\n/**\n * The main `Remarkable` class. Create an instance of\n * `Remarkable` with a `preset` and/or `options`.\n *\n * @param {String} `preset` If no preset is given, `default` is used.\n * @param {Object} `options`\n */\n\nfunction Remarkable(preset, options) {\n  if (typeof preset !== 'string') {\n    options = preset;\n    preset = 'default';\n  }\n\n  if (options && options.linkify != null) {\n    console.warn(\n      'linkify option is removed. Use linkify plugin instead:\\n\\n' +\n      'import Remarkable from \\'remarkable\\';\\n' +\n      'import linkify from \\'remarkable/linkify\\';\\n' +\n      'new Remarkable().use(linkify)\\n'\n    );\n  }\n\n  this.inline   = new ParserInline();\n  this.block    = new ParserBlock();\n  this.core     = new Core();\n  this.renderer = new Renderer();\n  this.ruler    = new Ruler();\n\n  this.options  = {};\n  this.configure(config[preset]);\n  this.set(options || {});\n}\n\n/**\n * Set options as an alternative to passing them\n * to the constructor.\n *\n * ```js\n * md.set({typographer: true});\n * ```\n * @param {Object} `options`\n * @api public\n */\n\nRemarkable.prototype.set = function (options) {\n  assign(this.options, options);\n};\n\n/**\n * Batch loader for components rules states, and options\n *\n * @param  {Object} `presets`\n */\n\nRemarkable.prototype.configure = function (presets) {\n  var self = this;\n\n  if (!presets) { throw new Error('Wrong `remarkable` preset, check name/content'); }\n  if (presets.options) { self.set(presets.options); }\n  if (presets.components) {\n    Object.keys(presets.components).forEach(function (name) {\n      if (presets.components[name].rules) {\n        self[name].ruler.enable(presets.components[name].rules, true);\n      }\n    });\n  }\n};\n\n/**\n * Use a plugin.\n *\n * ```js\n * var md = new Remarkable();\n *\n * md.use(plugin1)\n *   .use(plugin2, opts)\n *   .use(plugin3);\n * ```\n *\n * @param  {Function} `plugin`\n * @param  {Object} `options`\n * @return {Object} `Remarkable` for chaining\n */\n\nRemarkable.prototype.use = function (plugin, options) {\n  plugin(this, options);\n  return this;\n};\n\n\n/**\n * Parse the input `string` and return a tokens array.\n * Modifies `env` with definitions data.\n *\n * @param  {String} `string`\n * @param  {Object} `env`\n * @return {Array} Array of tokens\n */\n\nRemarkable.prototype.parse = function (str, env) {\n  var state = new StateCore(this, str, env);\n  this.core.process(state);\n  return state.tokens;\n};\n\n/**\n * The main `.render()` method that does all the magic :)\n *\n * @param  {String} `string`\n * @param  {Object} `env`\n * @return {String} Rendered HTML.\n */\n\nRemarkable.prototype.render = function (str, env) {\n  env = env || {};\n  return this.renderer.render(this.parse(str, env), this.options, env);\n};\n\n/**\n * Parse the given content `string` as a single string.\n *\n * @param  {String} `string`\n * @param  {Object} `env`\n * @return {Array} Array of tokens\n */\n\nRemarkable.prototype.parseInline = function (str, env) {\n  var state = new StateCore(this, str, env);\n  state.inlineMode = true;\n  this.core.process(state);\n  return state.tokens;\n};\n\n/**\n * Render a single content `string`, without wrapping it\n * to paragraphs\n *\n * @param  {String} `str`\n * @param  {Object} `env`\n * @return {String}\n */\n\nRemarkable.prototype.renderInline = function (str, env) {\n  env = env || {};\n  return this.renderer.render(this.parseInline(str, env), this.options, env);\n};\n\n\n\n\n/***/ }),\n/* 18 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\nclass Bilibili {\r\n  constructor(config) {\r\n    // this.skipReadImage = true\r\n    this.config = config\r\n    this.name = 'bilibili'\r\n  }\r\n\r\n  async getMetaData() {\r\n    var res = await $.ajax({\r\n      url: 'https://api.bilibili.com/x/web-interface/nav?build=0&mobi_app=web',\r\n    })\r\n    // console.log(res);\r\n    return {\r\n      uid: res.data.mid,\r\n      title: res.data.uname,\r\n      avatar: res.data.face,\r\n      supportTypes: ['html'],\r\n      type: 'bilibili',\r\n      displayName: '哔哩哔哩',\r\n      home: 'https://member.bilibili.com/platform/upload/text',\r\n      icon: 'https://www.bilibili.com/favicon.ico',\r\n    }\r\n  }\r\n\r\n  async addPost(post) {\r\n    return {\r\n      status: 'success',\r\n      post_id: 0,\r\n    }\r\n  }\r\n\r\n  async editPost(post_id, post) {\r\n    // var pgc_feed_covers = []\r\n    // if (post.post_thumbnail_raw && post.post_thumbnail_raw.images) {\r\n    //   pgc_feed_covers.push({\r\n    //     id: 0,\r\n    //     url: post.post_thumbnail_raw.url,\r\n    //     uri: post.post_thumbnail_raw.images[0].origin_web_uri,\r\n    //     origin_uri: post.post_thumbnail_raw.images[0].origin_web_uri,\r\n    //     ic_uri: '',\r\n    //     thumb_width: post.post_thumbnail_raw.images[0].width,\r\n    //     thumb_height: post.post_thumbnail_raw.images[0].height,\r\n    //   })\r\n    // }\r\n\r\n    var csrf = this.config.state.csrf;\r\n    var res = await $.ajax({\r\n      url: 'https://api.bilibili.com/x/article/creative/draft/addupdate',\r\n      type: 'POST',\r\n      dataType: 'JSON',\r\n      data: {\r\n        tid: 4,\r\n        title: post.post_title,\r\n        save: 0,\r\n        pgc_id: 0,\r\n        content: post.post_content,\r\n        csrf: csrf,\r\n        // pgc_feed_covers: JSON.stringify(pgc_feed_covers),\r\n      },\r\n    })\r\n\r\n    if (!res.data) {\r\n      throw new Error(res.message)\r\n    }\r\n\r\n    return {\r\n      status: 'success',\r\n      post_id: res.data.aid,\r\n      draftLink:\r\n        'https://member.bilibili.com/platform/upload/text/edit?aid=' +\r\n        res.data.aid,\r\n    }\r\n  }\r\n\r\n\r\n  async uploadFile(file) {\r\n    var src = file.src\r\n    var csrf = this.config.state.csrf\r\n    \r\n    var uploadUrl ='https://api.bilibili.com/x/article/creative/article/upcover'  \r\n    var file = new File([file.bits], 'temp', {\r\n      type: file.type,\r\n    })\r\n    var formdata = new FormData()\r\n    formdata.append('binary', file)\r\n    formdata.append('csrf', csrf)\r\n    var res = await axios({\r\n      url: uploadUrl,\r\n      method: 'post',\r\n      data: formdata,\r\n      headers: { 'Content-Type': 'multipart/form-data' },\r\n    })\r\n\r\n    if (res.data.code != 0) {\r\n      throw new Error('图片上传失败 ' + src)\r\n    }\r\n    // http only\r\n    console.log('uploadFile', res)\r\n    var id = Math.floor(Math.random() * 100000)\r\n    return [\r\n      {\r\n        id: id,\r\n        object_key: id,\r\n        url: res.data.data.url,\r\n        size: res.data.data.size,\r\n        // images: [res.data],\r\n      },\r\n    ]\r\n  }\r\n\r\n  async preEditPost(post) {\r\n    var div = $('<div>')\r\n    $('body').append(div)\r\n\r\n    div.html(post.content)\r\n    // var org = $(post.content);\r\n    // var doc = $('<div>').append(org.clone());\r\n    var doc = div\r\n    var pres = doc.find('a')\r\n    for (let mindex = 0; mindex < pres.length; mindex++) {\r\n      const pre = pres.eq(mindex)\r\n      try {\r\n        pre.after(pre.html()).remove()\r\n      } catch (e) {}\r\n    }\r\n\r\n    var pres = doc.find('iframe')\r\n    for (let mindex = 0; mindex < pres.length; mindex++) {\r\n      const pre = pres.eq(mindex)\r\n      try {\r\n        pre.remove()\r\n      } catch (e) {}\r\n    }\r\n\r\n    try {\r\n      const images = doc.find('img')\r\n      for (let index = 0; index < images.length; index++) {\r\n        const image = images.eq(index)\r\n        const imgSrc = image.attr('src')\r\n        if (imgSrc && imgSrc.indexOf('.svg') > -1) {\r\n          console.log('remove svg Image')\r\n          image.remove()\r\n        }\r\n      }\r\n      const qqm = doc.find('qqmusic')\r\n      qqm.next().remove()\r\n      qqm.remove()\r\n    } catch (e) {}\r\n\r\n    post.content = $('<div>')\r\n      .append(doc.clone())\r\n      .html()\r\n    console.log('post', post)\r\n  }\r\n\r\n  editImg(img, source) {\r\n    img.attr('size', source.size)\r\n  }\r\n  //   <img class=\"\" src=\"http://p2.pstatp.com/large/pgc-image/bc0a9fc8e595453083d85deb947c3d6e\" data-ic=\"false\" data-ic-uri=\"\" data-height=\"1333\" data-width=\"1000\" image_type=\"1\" web_uri=\"pgc-image/bc0a9fc8e595453083d85deb947c3d6e\" img_width=\"1000\" img_height=\"1333\"></img>\r\n}\n/* harmony export (immutable) */ __webpack_exports__[\"a\"] = Bilibili;\n\r\n\n\n/***/ }),\n/* 19 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\nvar _cacheMeta = null\r\n\r\nclass B51Cto {\r\n  constructor(config) {\r\n    // this.skipReadImage = true\r\n    this.config = config\r\n    this.name = '51cto'\r\n  }\r\n\r\n  async getMetaData() {\r\n    var res = await $.get('https://blog.51cto.com/blogger/publish')\r\n    var parser = new DOMParser()\r\n    var htmlDoc = parser.parseFromString(res, 'text/html')\r\n    var img = htmlDoc.querySelector('li.more.user > a > img')\r\n    var link = img.parentNode.href\r\n    var pie = link.split('/')\r\n    // pie.pop()\r\n    var uid = pie.pop()\r\n    console.log(link)\r\n    var scrs = [].slice\r\n      .call(htmlDoc.querySelectorAll('script'))\r\n      .filter(_ => _.innerText.indexOf('sign') > -1)\r\n\r\n    var uploadSign = null;\r\n    if (scrs.length) {\r\n      try {\r\n        var dataStr = scrs[0].innerText\r\n        var rawStr = dataStr.substring(\r\n          dataStr.indexOf('sign'),\r\n          dataStr.indexOf('uploadUrl', dataStr.indexOf('sign'))\r\n        )\r\n        var signStr = rawStr\r\n          .replace('var', '')\r\n          .trim()\r\n          .replace(\"sign = '\", '')\r\n          .replace(\"';\", '')\r\n          .trim()\r\n          uploadSign = signStr\r\n      } catch (e) {\r\n        console.log('51cto', e)\r\n      }\r\n    }\r\n      _cacheMeta = {\r\n        rawStr: rawStr,\r\n        uploadSign: uploadSign,\r\n        csrf: htmlDoc\r\n          .querySelector('meta[name=csrf-token]')\r\n          .getAttribute('content'),\r\n      }\r\n    console.log('51cto', _cacheMeta)\r\n    return {\r\n      uid: uid,\r\n      title: uid,\r\n      avatar: img.src,\r\n      type: '51cto',\r\n      displayName: '51CTO',\r\n      supportTypes: ['markdown', 'html'],\r\n      home: 'https://blog.51cto.com/blogger/publish',\r\n      icon: 'https://blog.51cto.com/favicon.ico',\r\n    }\r\n  }\r\n\r\n  async addPost(post) {\r\n    return {\r\n      status: 'success',\r\n      post_id: 0,\r\n    }\r\n  }\r\n\r\n  async editPost(post_id, post) {\r\n    // var pgc_feed_covers = []\r\n    // if (post.post_thumbnail_raw && post.post_thumbnail_raw.images) {\r\n    //   pgc_feed_covers.push({\r\n    //     id: 0,\r\n    //     url: post.post_thumbnail_raw.url,\r\n    //     uri: post.post_thumbnail_raw.images[0].origin_web_uri,\r\n    //     origin_uri: post.post_thumbnail_raw.images[0].origin_web_uri,\r\n    //     ic_uri: '',\r\n    //     thumb_width: post.post_thumbnail_raw.images[0].width,\r\n    //     thumb_height: post.post_thumbnail_raw.images[0].height,\r\n    //   })\r\n    // }\r\n    // var csrf = this.config.state.csrf\r\n    var postStruct = {}\r\n\r\n    if (post.markdown) {\r\n      postStruct = {\r\n        title: post.post_title,\r\n        copy_code: 1,\r\n        is_old: 0,\r\n        content: post.markdown,\r\n        _csrf: _cacheMeta.csrf,\r\n      }\r\n    } else {\r\n      postStruct = {\r\n        blog_type: null,\r\n        title: post.post_title,\r\n        copy_code: 1,\r\n        content: post.post_content,\r\n        pid: '',\r\n        cate_id: '',\r\n        custom_id: '',\r\n        tag: '',\r\n        abstract:'', \r\n        is_hide: 0,\r\n        did: '',\r\n        blog_id: '', \r\n        is_old: 1,\r\n        _csrf: _cacheMeta.csrf,\r\n        // editorValue: null,\r\n      }\r\n    }\r\n\r\n    var res = await $.ajax({\r\n      url: 'https://blog.51cto.com/blogger/draft',\r\n      type: 'POST',\r\n      dataType: 'JSON',\r\n      data: postStruct,\r\n    })\r\n\r\n    if (!res.data) {\r\n      throw new Error(res.message)\r\n    }\r\n\r\n    return {\r\n      status: 'success',\r\n      post_id: res.data.did,\r\n      draftLink: 'https://blog.51cto.com/blogger/draft/' + res.data.did,\r\n    }\r\n  }\r\n\r\n  async uploadFile(file) {\r\n    var src = file.src\r\n    // var csrf = this.config.state.csrf\r\n    var uploadUrl = 'https://upload.51cto.com/index.php?c=upload&m=upimg&orig=b'\r\n    var file = new File([file.bits], 'temp', {\r\n      type: file.type,\r\n    })\r\n    var formdata = new FormData()\r\n\r\n    formdata.append('sign', _cacheMeta.uploadSign)\r\n    // formdata.append('file', file)\r\n    formdata.append('file', file, new Date().getTime() + '.jpg')\r\n    formdata.append('type', file.type)\r\n    formdata.append('id', 'WU_FILE_1')\r\n    formdata.append('fileid', `uploadm-` + Math.floor(Math.random() * 1000000))\r\n    // formdata.append('name', new Date().getTime() + '.jpg')\r\n    formdata.append('lastModifiedDate', new Date().toString())\r\n    formdata.append('size', file.size)\r\n    var res = await axios({\r\n      url: uploadUrl,\r\n      method: 'post',\r\n      data: formdata,\r\n      headers: { 'Content-Type': 'multipart/form-data' },\r\n    })\r\n\r\n    if (res.data.status === false) {\r\n      throw new Error('图片上传失败 ' + src)\r\n    }\r\n    // http only\r\n    console.log('uploadFile', res)\r\n    var id = Math.floor(Math.random() * 100000)\r\n    return [\r\n      {\r\n        id: id,\r\n        object_key: id,\r\n        url: `https://s4.51cto.com/` + res.data.data,\r\n        // size: res.data.data.size,\r\n        // images: [res.data],\r\n      },\r\n    ]\r\n  }\r\n\r\n  async preEditPost(post) {\r\n    var div = $('<div>')\r\n    $('body').append(div)\r\n\r\n    div.html(post.content)\r\n    // var org = $(post.content);\r\n    // var doc = $('<div>').append(org.clone());\r\n    var doc = div\r\n    var pres = doc.find('a')\r\n    for (let mindex = 0; mindex < pres.length; mindex++) {\r\n      const pre = pres.eq(mindex)\r\n      try {\r\n        pre.after(pre.html()).remove()\r\n      } catch (e) {}\r\n    }\r\n\r\n    var pres = doc.find('iframe')\r\n    for (let mindex = 0; mindex < pres.length; mindex++) {\r\n      const pre = pres.eq(mindex)\r\n      try {\r\n        pre.remove()\r\n      } catch (e) {}\r\n    }\r\n\r\n    try {\r\n      const images = doc.find('img')\r\n      for (let index = 0; index < images.length; index++) {\r\n        const image = images.eq(index)\r\n        const imgSrc = image.attr('src')\r\n        if (imgSrc && imgSrc.indexOf('.svg') > -1) {\r\n          console.log('remove svg Image')\r\n          image.remove()\r\n        }\r\n      }\r\n      const qqm = doc.find('qqmusic')\r\n      qqm.next().remove()\r\n      qqm.remove()\r\n    } catch (e) {}\r\n\r\n    post.content = $('<div>')\r\n      .append(doc.clone())\r\n      .html()\r\n    console.log('post', post)\r\n  }\r\n\r\n  // editImg(img, source) {\r\n  //   img.attr('size', source.size)\r\n  // }\r\n  //   <img class=\"\" src=\"http://p2.pstatp.com/large/pgc-image/bc0a9fc8e595453083d85deb947c3d6e\" data-ic=\"false\" data-ic-uri=\"\" data-height=\"1333\" data-width=\"1000\" image_type=\"1\" web_uri=\"pgc-image/bc0a9fc8e595453083d85deb947c3d6e\" img_width=\"1000\" img_height=\"1333\"></img>\r\n}\n/* harmony export (immutable) */ __webpack_exports__[\"a\"] = B51Cto;\n\r\n\n\n/***/ }),\n/* 20 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n\r\nclass FocusDriver {\r\n  constructor() {\r\n    this.name = 'weibo'\r\n  }\r\n\r\n  async getMetaData() {\r\n    var res = await $.get('https://mp-fe-pc.focus.cn//user/status?')\r\n    return {\r\n        uid: res.data.uid,\r\n        title: res.data.accountName,\r\n        avatar: null,\r\n        supportTypes: ['html'],\r\n        displayName: '搜狐焦点',\r\n        type: 'sohufocus',\r\n        home: 'https://mp.focus.cn/fe/index.html#/info/draft',\r\n        icon: 'https://mp.focus.cn/favicon.ico',\r\n      }\r\n  }\r\n\r\n  async addPost(post) {\r\n    return {\r\n      status: 'success',\r\n      post_id: 0,\r\n    }\r\n  }\r\n\r\n\r\n  async preEditPost(post) {\r\n    // var div = $('<div>');\r\n    // $('body').append(div);\r\n    // div.html(post.content);\r\n\r\n    // // var doc = div;\r\n    // // doc.clone()\r\n    // var documentClone = document.cloneNode(true);\r\n    // var article = new Readability(documentClone).parse();\r\n\r\n    // div.remove();\r\n    // console.log(article);\r\n    var rexp = new RegExp('>[\\ts ]*<', 'g')\r\n    var result = post.content.replace(rexp, '><')\r\n    post.content = result\r\n  }\r\n\r\n  async editPost(post_id, post) {\r\n    var res = await axios.post('https://mp-fe-pc.focus.cn/news/info/publishNewsInfo', {\r\n        \"projectIds\": [],\r\n        \"newsBasic\": {\r\n          \"id\": \"\",\r\n          \"cityId\": 0,\r\n          \"title\": post.post_title,\r\n          \"category\": 1,\r\n          \"headImg\": \"\",\r\n          \"newsAbstract\": \"\",\r\n          \"isGuide\": 0,\r\n          \"status\": 4\r\n        },\r\n        \"newsContent\": {\r\n          \"content\": post.post_content\r\n        },\r\n        \"videoIds\": []\r\n    })\r\n    // console.log(res)\r\n    var aId = res.data.data.id\r\n    return {\r\n      status: 'success',\r\n      post_id: aId,\r\n      draftLink: 'https://mp.focus.cn/fe/index.html#/info/subinfo/' + aId,\r\n    }\r\n  }\r\n\r\n  async uploadFile(file) {\r\n    var formdata = new FormData()\r\n    var blob = new Blob([file.bits], {\r\n        type: file.type\r\n    });\r\n\r\n    formdata.append('image', blob, new Date().getTime() + '.jpg')\r\n    var res = await axios({\r\n      url: `https://mp-fe-pc.focus.cn/common/image/upload?type=2`,\r\n      method: 'post',\r\n      data: formdata,\r\n      headers: { 'Content-Type': 'multipart/form-data' },\r\n    })\r\n   \r\n    if(res.data.code != 200) {\r\n      console.log(res.data);\r\n      throw new Error('upload failed')\r\n    }\r\n    var url = `https://t-img.51f.com/sh740wsh${res.data.data}`\r\n    return [\r\n      {\r\n        id: res.data.data,\r\n        object_key: res.data.data,\r\n        url: url,\r\n      },\r\n    ]\r\n  }\r\n\r\n}\n/* harmony export (immutable) */ __webpack_exports__[\"a\"] = FocusDriver;\n\r\n\n\n/***/ }),\n/* 21 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n// https://www.51hanghai.com/portal.php?mod=portalcp&ac=article\r\nvar _cacheMeta = null;\r\n\r\nclass Discuz {\r\n  constructor(config) {\r\n    this.config = config || {}\r\n    var url = this.config.url\r\n    this.pubUrl = `${url}/portal.php?mod=portalcp&ac=article`\r\n    // this.upUrl = `${url}/misc.php?mod=swfupload&action=swfupload&operation=portal`\r\n    this.upUrl = `${url}/misc.php?mod=swfupload&action=swfupload&operation=upload`\r\n    this.name = 'discuz'\r\n    \r\n    // this.skipReadImage = true\r\n  }\r\n\r\n  async getMetaData() {\r\n    var url = this.config.url;\r\n    console.log('disduz', this.config)\r\n    var postUrl = `${url}/portal.php?mod=portalcp&ac=article`\r\n    var favIcon = `${url}/favicon.ico`\r\n    var res = await $.get(postUrl)\r\n    var parser = new DOMParser()\r\n    var htmlDoc = parser.parseFromString(res, 'text/html')\r\n    var nickname = htmlDoc.querySelector('.vwmy').innerText\r\n    var img = htmlDoc.querySelector('.avt img').src\r\n\r\n    _cacheMeta = {\r\n      uid: img.split('uid=')[1].split('&size')[0],\r\n      title: nickname,\r\n      avatar: img,\r\n      type: 'discuz',\r\n      displayName: 'Discuz',\r\n      supportTypes: ['html'],\r\n      config: this.config,\r\n      home: postUrl,\r\n      icon: favIcon,\r\n    }\r\n\r\n    var uploadSrciptBlocks = [].slice.apply(htmlDoc.querySelectorAll('script')).filter(_ => _.innerText.indexOf('SWFUpload') > -1);\r\n    if(uploadSrciptBlocks.length) {\r\n      var scripText = uploadSrciptBlocks[0].innerText\r\n      var startTag = 'post_params:'\r\n      var strIndex =  scripText.indexOf(startTag)\r\n      var dataStr = scripText.substring(strIndex + startTag.length, scripText.indexOf('},', strIndex) + 1);\r\n      var post_params = new Function(\r\n        'var config = ' +\r\n        dataStr +\r\n          '; return config;'\r\n      )();\r\n      _cacheMeta.uploadToken = post_params.hash\r\n      _cacheMeta.raw = post_params\r\n    }\r\n    // var parser = new DOMParser()\r\n    // var htmlDoc = parser.parseFromString(res, 'text/html')\r\n    // var img = htmlDoc.querySelector('li.more.user > a > img')\r\n    return _cacheMeta\r\n  }\r\n\r\n  async addPost(post) {\r\n    return {\r\n      status: 'success',\r\n      post_id: 0,\r\n    }\r\n  }\r\n\r\n  async editPost(post_id, post) {\r\n    var postStruct = {}\r\n\r\n    if (post.markdown) {\r\n      postStruct = {\r\n        title: post.post_title,\r\n        catid: 24,\r\n        content: post.markdown,\r\n        romotepic: 1,\r\n      }\r\n    } else {\r\n      postStruct = {\r\n        title: post.post_title,\r\n        catid: 24,\r\n        romotepic: 1,\r\n        content: post.post_content,\r\n      }\r\n    }\r\n\r\n    // title: test\r\n    // highlight_style[0]: \r\n    // highlight_style[1]: \r\n    // highlight_style[2]: \r\n    // highlight_style[3]: \r\n    // htmlname: \r\n    // oldhtmlname: \r\n    // pagetitle: \r\n    // catid: 24\r\n    // from: \r\n    postStruct.fromurl = null\r\n    postStruct.dateline = null\r\n    postStruct.from_idtyp = `tid`\r\n    postStruct.from_id = 0\r\n    postStruct.id = 0\r\n    postStruct.idtype = `tid`;\r\n    postStruct.url = null; \r\n    postStruct.author = null;\r\n    // conver: a:3:{s:3:\"pic\";s:0:\"\";s:5:\"thumb\";i:0;s:6:\"remote\";i:0;}\r\n    // file: (binary)\r\n    // file: (binary)\r\n    // content: test\r\n    // romotepic: 1\r\n    // summary: \r\n    // aid: \r\n    // cid: \r\n    // attach_ids: \r\n    // articlesubmit: true\r\n    postStruct.articlesubmit = true;\r\n    postStruct.formhash = `caa4c6cb`\r\n    postStruct.conver = `a:3:{s:3:\"pic\";s:0:\"\";s:5:\"thumb\";i:0;s:6:\"remote\";i:0;}`;\r\n    // var res = await $.ajax({\r\n    //   url: this.pubUrl,\r\n    //   type: 'POST',\r\n    //   dataType: 'JSON',\r\n    //   data: postStruct,\r\n    // })\r\n    // if (!res.data) {\r\n    //   throw new Error(res.message)\r\n    // }\r\n    setCache('discuz_cache', JSON.stringify(postStruct))\r\n\r\n    return {\r\n      status: 'success',\r\n      post_id: 0,\r\n      draftLink: `${this.config.url}/forum.php?mod=guide&view=my&loaddraft`,\r\n    }\r\n  }\r\n\r\n  async uploadFile(file) {\r\n    var id = Date.now() + Math.floor(Math.random()* 1000);\r\n    return [\r\n      {\r\n        id: id,\r\n        object_key: id,\r\n        url: file.src,\r\n      },\r\n    ]\r\n    var src = file.src\r\n    // var file = new File([file.bits], 'temp', {\r\n    //   type: file.type,\r\n    // })\r\n    var blob = new Blob([file.bits], {\r\n      type: file.type,\r\n    })\r\n    var formdata = new FormData()\r\n\r\n    formdata.append('uid', _cacheMeta.uid)\r\n    formdata.append('hash', _cacheMeta.uploadToken)\r\n    formdata.append('filetype', '.jpg')\r\n    formdata.append('type', 'image')\r\n    formdata.append('aid', '0')\r\n    // formdata.append('catid', '19')\r\n    // formdata.append('Filedata', blob)\r\n    formdata.append('Filedata', blob, new Date().getTime() + '.jpg')\r\n    formdata.append('size', blob.size)\r\n    formdata.append('id', 'WU_FILE_1')\r\n    var res = await axios({\r\n      url: this.upUrl,\r\n      method: 'post',\r\n      data: formdata,\r\n      headers: { 'Content-Type': 'multipart/form-data' },\r\n    })\r\n\r\n    var imageHtmlRes = await axios.get(`${this.config.url}/forum.php?mod=ajax&action=imagelist&type=single&pid=0&aids=` + res.data)\r\n    var parser = new DOMParser()\r\n    var htmlDoc = parser.parseFromString(imageHtmlRes.data, 'text/html')\r\n    var imgSrc = `${this.config.url}/` + htmlDoc.querySelector(\"img\").getAttribute('src')\r\n    console.log('upload.res', imageHtmlRes)\r\n    var imageId = Date.now() +Math.floor( Math.random() * 10);\r\n    // if (res.data.aid === false) {\r\n    //   throw new Error('图片上传失败 ' + src)\r\n    // }\r\n    // http only\r\n    console.log('uploadFile', res)\r\n    return [\r\n       {\r\n        id: imageId,\r\n        object_key: imageId,\r\n        url: imgSrc\r\n      },\r\n      // {\r\n      //   id: res.data.aid,\r\n      //   object_key: res.data.aid,\r\n      //   url: res.data.bigimg,\r\n      //   // size: res.data.data.size,\r\n      //   // images: [res.data],\r\n      // },\r\n    ]\r\n  }\r\n\r\n  async preEditPost(post) {\r\n    var div = $('<div>')\r\n    $('body').append(div)\r\n\r\n    div.html(post.content)\r\n    // var org = $(post.content);\r\n    // var doc = $('<div>').append(org.clone());\r\n    var doc = div\r\n    var pres = doc.find('a')\r\n    for (let mindex = 0; mindex < pres.length; mindex++) {\r\n      const pre = pres.eq(mindex)\r\n      try {\r\n        pre.after(pre.html()).remove()\r\n      } catch (e) {}\r\n    }\r\n\r\n    var pres = doc.find('iframe')\r\n    for (let mindex = 0; mindex < pres.length; mindex++) {\r\n      const pre = pres.eq(mindex)\r\n      try {\r\n        pre.remove()\r\n      } catch (e) {}\r\n    }\r\n\r\n    try {\r\n      const images = doc.find('img')\r\n      for (let index = 0; index < images.length; index++) {\r\n        const image = images.eq(index)\r\n        const imgSrc = image.attr('src')\r\n        if (imgSrc && imgSrc.indexOf('.svg') > -1) {\r\n          console.log('remove svg Image')\r\n          image.remove()\r\n        }\r\n      }\r\n      const qqm = doc.find('qqmusic')\r\n      qqm.next().remove()\r\n      qqm.remove()\r\n    } catch (e) {}\r\n\r\n    post.content = $('<div>')\r\n      .append(doc.clone())\r\n      .html()\r\n    console.log('post', post)\r\n  }\r\n\r\n  editImg(img, source) {\r\n    img.removeAttr('crossorigin')\r\n    img.attr('referrerpolicy', \"no-referrer\")\r\n  }\r\n}\n/* harmony export (immutable) */ __webpack_exports__[\"a\"] = Discuz;\n\r\n\n\n/***/ })\n/******/ ]);for (var k in modules) exports[k] = modules[k];"