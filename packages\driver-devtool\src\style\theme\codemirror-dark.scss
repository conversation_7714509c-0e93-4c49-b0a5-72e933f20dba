/* Based on Sublime Text's dark theme */
/* fork from https://codemirror.net/theme/dark.css */

.cm-s-dark.CodeMirror {
  color: #f8f8f2;
}
.cm-s-dark div.CodeMirror-selected {
  background: #49483e;
}
.cm-s-dark .CodeMirror-line::selection,
.cm-s-dark .CodeMirror-line > span::selection,
.cm-s-dark .CodeMirror-line > span > span::selection {
  background: rgba(73, 72, 62, 0.99);
}
.cm-s-dark .CodeMirror-line::-moz-selection,
.cm-s-dark .CodeMirror-line > span::-moz-selection,
.cm-s-dark .CodeMirror-line > span > span::-moz-selection {
  background: rgba(73, 72, 62, 0.99);
}
.cm-s-dark .CodeMirror-gutters {
  border-right: 0px;
}
.cm-s-dark .CodeMirror-guttermarker {
  color: white;
}
.cm-s-dark .CodeMirror-guttermarker-subtle {
  color: #d0d0d0;
}
.cm-s-dark .CodeMirror-linenumber {
  color: #d0d0d0;
}
.cm-s-dark .CodeMirror-cursor {
  border-left: 1px solid #f8f8f0;
}

.cm-s-dark span.cm-comment {
  color: #75715e;
}
.cm-s-dark span.cm-atom {
  color: #ae81ff;
}
.cm-s-dark span.cm-number {
  color: #ae81ff;
}

.cm-s-dark span.cm-comment.cm-attribute {
  color: #97b757;
}
.cm-s-dark span.cm-comment.cm-def {
  color: #bc9262;
}
.cm-s-dark span.cm-comment.cm-tag {
  color: #bc6283;
}
.cm-s-dark span.cm-comment.cm-type {
  color: #5998a6;
}

.cm-s-dark span.cm-property,
.cm-s-dark span.cm-attribute {
  color: #a6e22e;
}
.cm-s-dark span.cm-keyword {
  color: #f92672;
}
.cm-s-dark span.cm-builtin {
  color: #66d9ef;
}
.cm-s-dark span.cm-string {
  color: #e6db74;
}

.cm-s-dark span.cm-variable {
  color: #f8f8f2;
}
.cm-s-dark span.cm-variable-2 {
  color: #9effff;
}
.cm-s-dark span.cm-variable-3,
.cm-s-dark span.cm-type {
  color: #66d9ef;
}
.cm-s-dark span.cm-def {
  color: #fd971f;
}
.cm-s-dark span.cm-bracket {
  color: #f8f8f2;
}
.cm-s-dark span.cm-tag {
  color: #f92672;
}
.cm-s-dark span.cm-header {
  color: #ae81ff;
}
.cm-s-dark span.cm-link {
  color: #ae81ff;
}
.cm-s-dark span.cm-error {
  background: #f92672;
  color: #f8f8f0;
}

.cm-s-dark .CodeMirror-activeline-background {
  background: #373831;
}
.cm-s-dark .CodeMirror-matchingbracket {
  text-decoration: underline;
  color: white !important;
}
