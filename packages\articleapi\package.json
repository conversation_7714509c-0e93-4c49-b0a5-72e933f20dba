{"private": true, "name": "articleapi", "version": "0.0.1", "description": "article<PERSON><PERSON> from ", "scripts": {"build": "webpack --env production", "start": "node dist/index.js", "prebuilt": "webpack --config ./webpack.code.js"}, "author": "fun", "license": "ISC", "dependencies": {"axios": "^0.21.1", "compare-ver": "^2.0.2", "element-ui": "^2.12.0", "jsdom": "^16.6.0", "juice": "^6.0.0", "mavon-editor": "^2.7.6", "mint-ui": "^2.2.13", "pouchdb": "^7.1.1", "pouchdb-find": "^7.1.1", "react": "^17.0.1", "react-dom": "^17.0.1", "sval": "^0.4.8", "vue": "^2.5.17", "vue-click-outside": "^1.0.7", "vue-json-viewer": "^2.2.8", "vue-moment": "^3.2.0", "vue-router": "^3.0.1", "vue-spinner": "^1.0.4", "vuex": "^3.0.1"}}