<!doctype lake>
<meta name="doc-version" content="1" />
<meta name="typography" content="traditional" />
<meta name="viewport" content="fixed" />
<blockquote data-lake-id="uce1b67c0" id="uce1b67c0">
  <p data-lake-id="u25a386ee" id="u25a386ee" style="text-align: left"><span data-lake-id="u95551104" id="u95551104"
      style="color: #0087fc">“</span></p>
  <p data-lake-id="u5324781f" id="u5324781f" style="text-align: left"><span data-lake-id="u997e5cce" id="u997e5cce"
      style="color: rgb(53, 53, 53)">年初盘点了下去年都做了些啥，发现文章同步助手仅靠自然流量用户增长了也有10倍之多了（投入的精力并不多的情况下）之前也有想好好完善下，增加更多的同步渠道。</span>
  </p>
  <p data-lake-id="u294f2b4e" id="u294f2b4e" style="text-align: left"><span data-lake-id="udc654f06" id="udc654f06"
      style="color: #0087fc">”</span></p>
</blockquote>
<h2 data-lake-id="KO6lL" id="KO6lL" style="text-align: left"><span data-lake-id="uf7d8f00c" id="uf7d8f00c"
    style="color: rgb(34, 34, 34)">收集需求的需求</span></h2>
<p data-lake-id="u2baa5369" id="u2baa5369">
  <card type="inline" name="image"
    value="data:%7B%22src%22%3A%22https%3A%2F%2Fcdn.nlark.com%2Fyuque%2F0%2F2021%2Fpng%2F735745%2F1637808353770-4d3c5d3e-a47e-4193-a29f-849aafb78047.png%22%2C%22taskId%22%3A%22uf784c16b-e834-45c3-8c51-92f840802d6%22%2C%22clientId%22%3A%22uc11383be-b999-4%22%2C%22originalType%22%3A%22binary%22%2C%22linkTarget%22%3A%22_blank%22%2C%22name%22%3A%22%5B8%2C13%5D.png%22%2C%22size%22%3A25167%2C%22from%22%3A%22ui%22%2C%22originWidth%22%3A800%2C%22originHeight%22%3A800%2C%22ratio%22%3A1%2C%22status%22%3A%22done%22%2C%22style%22%3A%22none%22%2C%22search%22%3A%22KE%E4%B8%8AWErE%20it%20play%20%5B8131%20riaueagtesx%22%2C%22ocrLocations%22%3A%5B%7B%22x%22%3A405.72974%2C%22y%22%3A131.60315%2C%22width%22%3A194.57345999999995%2C%22height%22%3A127.25474999999997%2C%22text%22%3A%22KE%E4%B8%8AWErE%22%7D%2C%7B%22x%22%3A326.24814%2C%22y%22%3A134.34892%2C%22width%22%3A57.70962000000003%2C%22height%22%3A42.02704%2C%22text%22%3A%22it%22%7D%2C%7B%22x%22%3A195.48431%2C%22y%22%3A145.26628%2C%22width%22%3A117.84879000000001%2C%22height%22%3A116.85944999999998%2C%22text%22%3A%22play%22%7D%2C%7B%22x%22%3A319.929%2C%22y%22%3A463.8506%2C%22width%22%3A161.**************%2C%22height%22%3A56.99630000000002%2C%22text%22%3A%22%5B8131%22%7D%2C%7B%22x%22%3A178.17773%2C%22y%22%3A479.18433%2C%22width%22%3A453.7912%2C%22height%22%3A182.09051000000005%2C%22text%22%3A%22riaueagtesx%22%7D%5D%2C%22showTitle%22%3Afalse%2C%22title%22%3A%22%22%2C%22rotation%22%3A0%2C%22crop%22%3A%5B0%2C0%2C1%2C1%5D%2C%22id%22%3A%22uec680fd5%22%2C%22margin%22%3A%7B%22top%22%3Atrue%2C%22bottom%22%3Atrue%7D%7D">
  </card>
</p>


<p data-lake-id="u2baa5369" id="u2baa5369">
    <card type="inline" name="image"
      value="">
    </card>
  </p>
<p data-lake-id="u6bd4c0ae" id="u6bd4c0ae" style="text-align: left"><span data-lake-id="u0095bc8b" id="u0095bc8b"
    style="color: #666">对自媒体人士而言，除主流媒体之外，有很多细分的媒体也是很需要的，比如说【马蜂窝、穷游】这样的媒体对于一位旅游行业从业人员来讲，吸引力和需要程度远在主流媒体之上。</span></p>
<p data-lake-id="uaae689d9" id="uaae689d9" style="text-align: left"><span data-lake-id="ue8c3af1b" id="ue8c3af1b"
    style="color: #666">考虑到这些情况的存在，很有必要分行业来增加这些细分的渠道的支持，这样对用户整体而言、可用性价值就更大了。</span></p>
<p data-lake-id="u27c20764" id="u27c20764" style="text-align: left"><span data-lake-id="u5f1a246b" id="u5f1a246b"
    style="color: #666">于是乎：在知道要大量增加渠道的情况下，如何保证增加的渠道是用户所需要的呢？这个时候需求收集就很有用了。</span></p>
<h2 data-lake-id="kwsCn" id="kwsCn" style="text-align: left"><span data-lake-id="ub535d5a7" id="ub535d5a7"
    style="color: rgb(34, 34, 34)">人肉收集？</span></h2>
<p data-lake-id="u26ce1190" id="u26ce1190" style="text-align: left"><span data-lake-id="u250b3ed3" id="u250b3ed3"
    style="color: #666">起初我想在用户微信群，让大家【给自己的群名称】备注上自己的所在的【行业】，再人肉挨个统计行业，再按行业自行去支持这些行业的头部媒体。</span></p>
<p data-lake-id="u23223351" id="u23223351" style="text-align: left"><span data-lake-id="u93996ccf" id="u93996ccf"
    style="color: #666">但在具体实施的过程中我觉得人肉统计这件事可能太费劲了、其次用户也不一定有意愿去给自己备注行业（即便是你告诉了他你的动机是为了了解行业支持更多符合大家需要的渠道）。</span></p>
<h2 data-lake-id="b53IB" id="b53IB" style="text-align: left"><span data-lake-id="u16f7c24e" id="u16f7c24e"
    style="color: rgb(34, 34, 34)">新型文档应用</span></h2>
<p data-lake-id="uac446f03" id="uac446f03" style="text-align: left"><span data-lake-id="uccfd3189" id="uccfd3189"
    style="color: #666">种种原因这让我想到了Coda，或许可以此定义一个文档、做一个小型的需求收集应用，定义基础表格字段，Button绑定自定义表单录入收据。加上投票计数。</span></p>
<p data-lake-id="u246972a9" id="u246972a9" style="text-align: left"><span data-lake-id="u7b007fe8" id="u7b007fe8"
    style="color: #666">这样用户可以自行录入一个渠道，其他用户可以给某渠道进行投票计数（计数能了解用户到底更需要哪个渠道、便于确定优先级），后面只需要把这个文档扔到群里就可以了！</span></p>
<p data-lake-id="u8d7feade" id="u8d7feade" style="text-align: left"><span data-lake-id="ucce64957" id="ucce64957"
    style="color: #666">但事实上Coda相当复杂，即便有很多模板作为开始，我还是感受到了恐慌（在你开始之前你得对Coda的文档和各种特性有一定基础了解，不然根本不知道到底下一步该干啥）</span></p>
<p data-lake-id="ufd8fbb34" id="ufd8fbb34" style="text-align: left"><span data-lake-id="ud366aa3a" id="ud366aa3a"
    style="color: #666">这个时候只能寻求更简便的产品了，比如说先前有调研过的Airtable。相对而言就简单很多了，以定好的数据表为基础，可以衍生到表单提交 ，按某个字段聚合的（看板、图表）视图！</span>
</p>
<h2 data-lake-id="D3OzU" id="D3OzU" style="text-align: left"><span data-lake-id="u2432fb83" id="u2432fb83"
    style="color: rgb(34, 34, 34)">信息的提交和公示</span></h2>
<p data-lake-id="ubfacc5ed" id="ubfacc5ed" style="text-align: left"><span data-lake-id="u6d1df39f" id="u6d1df39f"
    style="color: #666">按【状态】字段聚合表格，方便查看支持情况下的渠道列表</span></p>
<p data-lake-id="uc63ef4ee" id="uc63ef4ee" style="text-align: left"><span data-lake-id="ud251a168" id="ud251a168"
    style="color: #666">新增一个表单视图，让用户填写要提交的媒体名称，网址以及大致的备注，在描述区域外链到状态聚合表格。</span></p>
<p data-lake-id="u4fc6c7dc" id="u4fc6c7dc" style="text-align: left"><span data-lake-id="u13545b71" id="u13545b71"
    style="color: #666">接着就可以把表单链接发到用户微信群里了、放到官网菜单上等。</span></p>
<p data-lake-id="u16efb009" id="u16efb009" style="text-align: left"><span data-lake-id="uec3a224e" id="uec3a224e"
    style="color: #666">在通过Airtable的Iframe嵌套功能，可以很方便的把数据表的状态公示到官网上！再加上提交按钮引导的数据表单上。</span></p>
<p data-lake-id="uac169879" id="uac169879" style="text-align: left"><span data-lake-id="u49b239df" id="u49b239df"
    style="color: #666">信息流动的效率从未如此之高！</span></p>
<h2 data-lake-id="m98z8" id="m98z8" style="text-align: left"><span data-lake-id="uca312456" id="uca312456"
    style="color: rgb(34, 34, 34)">总结</span></h2>
<p data-lake-id="u9234d331" id="u9234d331" style="text-align: left"><span data-lake-id="ue44b01c4" id="ue44b01c4"
    style="color: #666">果不其然，从1月22号加上到今天，已经收集了大概20多个待支持的渠道。这些积压的需求，进而引发了对提高新渠道对接效率的思考。从而有了新的改进需求，以及是否可以有效利用用户的力量来帮助对接更多的渠道等。</span>
</p>
<p data-lake-id="u154fa755" id="u154fa755" style="text-align: left"><span data-lake-id="u6efe1b4c" id="u6efe1b4c"
    style="color: #666">关于Airtable还有很多有趣好玩的地方比如说自动化，表格右边App，比如说表单提交后发送通知到你的IM上等等，这里面各种组合的可玩空间相当大的！</span></p>
<p data-lake-id="uffdf263d" id="uffdf263d" style="text-align: left"><span data-lake-id="u522a8b1b" id="u522a8b1b"
    style="color: #666">此外去年底看了下国内也已经有类似的产品了如Treelab、Hipa、Vika等。</span></p>
<p data-lake-id="ud47e27ef" id="ud47e27ef" style="text-align: left"><span data-lake-id="u55c32709" id="u55c32709"
    style="color: #666">总得来讲这是很有趣的一次尝试！</span></p>
<p data-lake-id="ud6a526ee" id="ud6a526ee"><br></p>
<p data-lake-id="u5db2a731" id="u5db2a731"><span data-lake-id="ubbd98299" id="ubbd98299"> </span></p>