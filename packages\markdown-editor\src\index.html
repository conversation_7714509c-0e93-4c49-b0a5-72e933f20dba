<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8" />
    <title>Markdown编辑器 -
    一键同步多平台 自媒体内容同步、内容营销、分发工具，自媒体助手，一键同步发布，支持微博头条、今日头条、豆瓣、WordPress、知乎、简书、typecho等各大平台</title>
    <meta data-n-head="true" data-hid="description" name="description" content="微信公众号文章多平台同步，内容营销工具，自媒体内容同步，自媒体助手、内容分发，一键同步发布，支持微博头条、今日头条、豆瓣、WordPress、知乎、简书、掘金、CSDN、typecho各大平台，一次发布，多平台同步发布。解放个人生产力">
    <meta data-n-head="true" name="keywords" content="内容营销工具,自媒体助手,自媒体内容同步,内容分发,一键同步发布,微信公众号同步助手,文章同步,独立开发者,WordPress同步,微信文章同步到WordPress,CSDN,博客园,掘金">
    <link href="https://fonts.googleapis.com/css?family=Grand+Hotel|Open+Sans:400,600|Raleway:200,300,400,600,700,800"
        rel="stylesheet" />
    <link rel="stylesheet" href="assets/fonts/font-awesome/css/font-awesome.min.css" />
    <link rel="stylesheet" href="boot.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <!-- wechatsync.com Baidu tongji analytics -->
   <script>
        var _hmt = _hmt || [];
        (function () {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?9b799c9c9f628aece0b71b72ce013bab";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>
    <script>
        !function () {
            var analytics = window.analytics = window.analytics || []; if (!analytics.initialize) if (analytics.invoked) window.console && console.error && console.error("Segment snippet included twice."); else {
                analytics.invoked = !0; analytics.methods = ["trackSubmit", "trackClick", "trackLink", "trackForm", "pageview", "identify", "reset", "group", "track", "ready", "alias", "debug", "page", "once", "off", "on"]; analytics.factory = function (t) { return function () { var e = Array.prototype.slice.call(arguments); e.unshift(t); analytics.push(e); return analytics } }; for (var t = 0; t < analytics.methods.length; t++) { var e = analytics.methods[t]; analytics[e] = analytics.factory(e) } analytics.load = function (t, e) { var n = document.createElement("script"); n.type = "text/javascript"; n.async = !0; n.src = "https://cdn.segment.com/analytics.js/v1/" + t + "/analytics.min.js"; var a = document.getElementsByTagName("script")[0]; a.parentNode.insertBefore(n, a); analytics._loadOptions = e }; analytics.SNIPPET_VERSION = "4.1.0";
                analytics.load("cnCHdmxnAgsvURAZMWNwjALLb7KneUUU");
                analytics.page();
            }
        }();
    </script>
</head>

<body>
    <main id="app">
        <router-view />
    </main>
    <!-- built files will be auto injected -->
</body>
</html>
