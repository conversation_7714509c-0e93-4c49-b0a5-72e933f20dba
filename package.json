{"private": true, "name": "wechat-sync", "version": "0.0.7", "description": "", "workspaces": ["packages/*", "packages/@wechatsync/*"], "author": "fun", "license": "ISC", "scripts": {"prepare": "husky install"}, "config": {"commitizen": {"path": "node_modules/cz-customizable"}}, "devDependencies": {"@babel/core": "^7.13.10", "@babel/plugin-proposal-class-properties": "^7.13.0", "@babel/plugin-proposal-private-methods": "^7.13.0", "@babel/plugin-transform-runtime": "^7.13.10", "@babel/preset-env": "^7.13.10", "@babel/runtime": "^7.13.10", "@commitlint/cli": "^12.0.1", "@commitlint/config-conventional": "^12.0.1", "@commitlint/config-lerna-scopes": "^12.0.1", "babel-loader": "^8.2.2", "commitizen": "^4.2.3", "conventional-commit-types": "^3.0.0", "copy-webpack-plugin": "^8.0.0", "cross-env": "^7.0.2", "css-loader": "^5.1.2", "cz-customizable": "^6.3.0", "dotenv-webpack": "^7.0.1", "html-webpack-plugin": "^5.3.1", "husky": "^5.1.3", "ignore-emit-webpack-plugin": "^2.0.6", "jsdom": "^11.12.0", "jsdom-global": "^3.0.2", "mini-css-extract-plugin": "^1.3.9", "sass": "^1.32.8", "sass-loader": "^11.0.1", "style-loader": "^2.0.0", "terser-webpack-plugin": "^5.1.1", "vue-loader": "^15.9.6", "vue-markdown-loader": "^2.4.1", "vue-template-compiler": "^2.6.12", "webpack": "^5.25.0", "webpack-cli": "^4.5.0", "webpack-dev-server": "^3.11.2", "webpack-node-externals": "^2.5.2", "zip-webpack-plugin": "^4.0.1"}}